"""
文档解析器
支持.docx和.pdf文件的解析和处理
"""

import os
import re
from typing import List, Dict
from docx import Document
import PyPDF2


class DocumentParser:
    def __init__(self):
        self.supported_formats = [".docx", ".pdf"]

    def extract_text_from_docx(self, file_path: str) -> List[Dict]:
        """
        从DOCX文件中提取文本，保持段落结构

        Returns:
            List of dictionaries containing paragraph info
        """
        try:
            doc = Document(file_path)
            paragraphs = []

            for i, para in enumerate(doc.paragraphs):
                if para.text.strip():  # 只保存非空段落
                    paragraphs.append(
                        {
                            "index": i,
                            "text": para.text.strip(),
                            "style": para.style.name if para.style else "Normal",
                            "alignment": para.alignment,
                        }
                    )

            return paragraphs

        except Exception as e:
            raise Exception(f"解析DOCX文件失败: {str(e)}")

    def extract_text_from_pdf(self, file_path: str) -> List[Dict]:
        """
        从PDF文件中提取文本

        Returns:
            List of dictionaries containing text blocks
        """
        try:
            paragraphs = []

            with open(file_path, "rb") as file:
                pdf_reader = PyPDF2.PdfReader(file)

                for page_num, page in enumerate(pdf_reader.pages):
                    text = page.extract_text()

                    # 按段落分割文本
                    page_paragraphs = text.split("\n\n")

                    for i, para_text in enumerate(page_paragraphs):
                        para_text = para_text.strip().replace("\n", " ")
                        if para_text:
                            paragraphs.append(
                                {
                                    "index": len(paragraphs),
                                    "text": para_text,
                                    "page": page_num + 1,
                                    "style": "Normal",
                                    "alignment": None,
                                }
                            )

            return paragraphs

        except Exception as e:
            raise Exception(f"解析PDF文件失败: {str(e)}")

    def parse_document(self, file_path: str) -> List[Dict]:
        """
        解析文档文件

        Args:
            file_path: 文件路径

        Returns:
            段落列表
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")

        file_ext = os.path.splitext(file_path)[1].lower()

        if file_ext == ".docx":
            return self.extract_text_from_docx(file_path)
        elif file_ext == ".pdf":
            return self.extract_text_from_pdf(file_path)
        else:
            raise ValueError(f"不支持的文件格式: {file_ext}")

    def create_docx_from_paragraphs(
        self, paragraphs: List[Dict], output_path: str
    ) -> str:
        """
        根据段落列表创建DOCX文件

        Args:
            paragraphs: 段落列表
            output_path: 输出文件路径

        Returns:
            输出文件路径
        """
        try:
            doc = Document()

            for para_info in paragraphs:
                paragraph = doc.add_paragraph(para_info["text"])

                # 尝试保持原有样式
                if "style" in para_info and para_info["style"]:
                    try:
                        paragraph.style = para_info["style"]
                    except:
                        pass  # 如果样式不存在，使用默认样式

                # 设置对齐方式
                if "alignment" in para_info and para_info["alignment"]:
                    paragraph.alignment = para_info["alignment"]

            doc.save(output_path)
            return output_path

        except Exception as e:
            raise Exception(f"创建DOCX文件失败: {str(e)}")

    def extract_translation_pairs(self, ref_file1: str, ref_file2: str) -> Dict:
        """
        从两个参考文件中提取翻译对应关系

        Args:
            ref_file1: 参考文件1路径
            ref_file2: 参考文件2路径

        Returns:
            翻译对应关系字典
        """
        try:
            # 解析两个参考文件
            paragraphs1 = self.parse_document(ref_file1)
            paragraphs2 = self.parse_document(ref_file2)

            # 提取文本内容
            texts1 = [p["text"] for p in paragraphs1]
            texts2 = [p["text"] for p in paragraphs2]

            # 简单的对应关系匹配（基于段落顺序）
            translation_pairs = []
            min_len = min(len(texts1), len(texts2))

            for i in range(min_len):
                if texts1[i].strip() and texts2[i].strip():
                    translation_pairs.append({"source": texts1[i], "target": texts2[i]})

            # 提取术语对应关系
            terminology = self._extract_terminology(translation_pairs)

            return {
                "pairs": translation_pairs,
                "terminology": terminology,
                "context": self._build_context_summary(translation_pairs),
            }

        except Exception as e:
            raise Exception(f"提取翻译对应关系失败: {str(e)}")

    def _extract_terminology(self, pairs: List[Dict]) -> Dict[str, str]:
        """提取术语对应关系"""
        terminology = {}

        # 简单的术语提取逻辑
        for pair in pairs:
            source = pair["source"]
            target = pair["target"]

            # 提取专业术语（大写字母开头的词组、数字等）
            source_terms = re.findall(r"\b[A-Z][a-zA-Z\s]+\b|[\d,]+", source)
            target_terms = re.findall(r"\b[\u4e00-\u9fff]+\b|[\d,]+", target)

            # 简单匹配（实际应用中需要更复杂的算法）
            if len(source_terms) == len(target_terms):
                for s_term, t_term in zip(source_terms, target_terms):
                    if s_term.strip() and t_term.strip():
                        terminology[s_term.strip()] = t_term.strip()

        return terminology

    def _build_context_summary(self, pairs: List[Dict]) -> str:
        """构建翻译上下文摘要"""
        if not pairs:
            return ""

        # 取前几个翻译对作为上下文示例
        context_examples = pairs[:3]

        context = "翻译参考示例：\n"
        for i, pair in enumerate(context_examples, 1):
            context += f"\n示例{i}:\n"
            context += f"原文: {pair['source']}\n"
            context += f"译文: {pair['target']}\n"

        return context
