{"version": 3, "file": "init-BznC5dqt.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/init.js"], "sourcesContent": ["import { E as ExtensionType, U as UPDATE_PRIORITY, T as Ticker, a6 as Geometry, l as UniformGroup, d as BindGroup, w as warn, I as TexturePool, u as Texture, P as Point, R as RendererType, J as Bounds, M as Matrix, a3 as deprecation, a4 as v8_0_0, c as <PERSON><PERSON><PERSON>, B as BufferUsage, $ as Color, ac as TextStyle, ad as generateTextStyleKey, S as State, z as BigPool, ae as BatchableGraphics, af as getAdjustedBlendModeBlend, N as getAttributeInfoFromFormat, ag as ViewableBuffer, t as Shader, x as GlProgram, G as GpuProgram, ah as TextureStyle, n as compileHighShaderGpuProgram, a7 as compileHighShaderGlProgram, s as roundPixelsBit, aa as roundPixelsBitGl, q as getMaxTexturesPerBatch, o as colorBit, p as generateTextureBatchBit, a8 as colorBitGl, a9 as generateTextureBatchBitGl, ab as getBatchSamplersUniformGroup, ai as BitmapFontManager, aj as getBitmapTextLayout, ak as Graphics, al as Cache, am as updateQuadBounds, D as DOMAdapter, m as CanvasPool, V as Rectangle, an as CanvasTextMetrics, ao as fontStringFromTextStyle, ap as getCanvasFillStyle, aq as nextPow2, e as extensions, ar as GraphicsContextSystem } from \"./Index3.js\";\nimport { c as color32BitToUniform, a as localUniformBit, b as localUniformBitGl, B as BatchableSprite } from \"./colorToUniform.js\";\nclass ResizePlugin {\n  /**\n   * Initialize the plugin with scope of application instance\n   * @static\n   * @private\n   * @param {object} [options] - See application options\n   */\n  static init(options) {\n    Object.defineProperty(\n      this,\n      \"resizeTo\",\n      /**\n       * The HTML element or window to automatically resize the\n       * renderer's view element to match width and height.\n       * @member {Window|HTMLElement}\n       * @name resizeTo\n       * @memberof app.Application#\n       */\n      {\n        set(dom) {\n          globalThis.removeEventListener(\"resize\", this.queueResize);\n          this._resizeTo = dom;\n          if (dom) {\n            globalThis.addEventListener(\"resize\", this.queueResize);\n            this.resize();\n          }\n        },\n        get() {\n          return this._resizeTo;\n        }\n      }\n    );\n    this.queueResize = () => {\n      if (!this._resizeTo) {\n        return;\n      }\n      this._cancelResize();\n      this._resizeId = requestAnimationFrame(() => this.resize());\n    };\n    this._cancelResize = () => {\n      if (this._resizeId) {\n        cancelAnimationFrame(this._resizeId);\n        this._resizeId = null;\n      }\n    };\n    this.resize = () => {\n      if (!this._resizeTo) {\n        return;\n      }\n      this._cancelResize();\n      let width;\n      let height;\n      if (this._resizeTo === globalThis.window) {\n        width = globalThis.innerWidth;\n        height = globalThis.innerHeight;\n      } else {\n        const { clientWidth, clientHeight } = this._resizeTo;\n        width = clientWidth;\n        height = clientHeight;\n      }\n      this.renderer.resize(width, height);\n      this.render();\n    };\n    this._resizeId = null;\n    this._resizeTo = null;\n    this.resizeTo = options.resizeTo || null;\n  }\n  /**\n   * Clean up the ticker, scoped to application\n   * @static\n   * @private\n   */\n  static destroy() {\n    globalThis.removeEventListener(\"resize\", this.queueResize);\n    this._cancelResize();\n    this._cancelResize = null;\n    this.queueResize = null;\n    this.resizeTo = null;\n    this.resize = null;\n  }\n}\nResizePlugin.extension = ExtensionType.Application;\nclass TickerPlugin {\n  /**\n   * Initialize the plugin with scope of application instance\n   * @static\n   * @private\n   * @param {object} [options] - See application options\n   */\n  static init(options) {\n    options = Object.assign({\n      autoStart: true,\n      sharedTicker: false\n    }, options);\n    Object.defineProperty(\n      this,\n      \"ticker\",\n      {\n        set(ticker) {\n          if (this._ticker) {\n            this._ticker.remove(this.render, this);\n          }\n          this._ticker = ticker;\n          if (ticker) {\n            ticker.add(this.render, this, UPDATE_PRIORITY.LOW);\n          }\n        },\n        get() {\n          return this._ticker;\n        }\n      }\n    );\n    this.stop = () => {\n      this._ticker.stop();\n    };\n    this.start = () => {\n      this._ticker.start();\n    };\n    this._ticker = null;\n    this.ticker = options.sharedTicker ? Ticker.shared : new Ticker();\n    if (options.autoStart) {\n      this.start();\n    }\n  }\n  /**\n   * Clean up the ticker, scoped to application.\n   * @static\n   * @private\n   */\n  static destroy() {\n    if (this._ticker) {\n      const oldTicker = this._ticker;\n      this.ticker = null;\n      oldTicker.destroy();\n    }\n  }\n}\nTickerPlugin.extension = ExtensionType.Application;\nclass FilterPipe {\n  constructor(renderer) {\n    this._renderer = renderer;\n  }\n  push(filterEffect, container, instructionSet) {\n    const renderPipes = this._renderer.renderPipes;\n    renderPipes.batch.break(instructionSet);\n    instructionSet.add({\n      renderPipeId: \"filter\",\n      canBundle: false,\n      action: \"pushFilter\",\n      container,\n      filterEffect\n    });\n  }\n  pop(_filterEffect, _container, instructionSet) {\n    this._renderer.renderPipes.batch.break(instructionSet);\n    instructionSet.add({\n      renderPipeId: \"filter\",\n      action: \"popFilter\",\n      canBundle: false\n    });\n  }\n  execute(instruction) {\n    if (instruction.action === \"pushFilter\") {\n      this._renderer.filter.push(instruction);\n    } else if (instruction.action === \"popFilter\") {\n      this._renderer.filter.pop();\n    }\n  }\n  destroy() {\n    this._renderer = null;\n  }\n}\nFilterPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"filter\"\n};\nfunction getGlobalRenderableBounds(renderables, bounds) {\n  bounds.clear();\n  const tempMatrix = bounds.matrix;\n  for (let i = 0; i < renderables.length; i++) {\n    const renderable = renderables[i];\n    if (renderable.globalDisplayStatus < 7) {\n      continue;\n    }\n    bounds.matrix = renderable.worldTransform;\n    bounds.addBounds(renderable.bounds);\n  }\n  bounds.matrix = tempMatrix;\n  return bounds;\n}\nconst quadGeometry = new Geometry({\n  attributes: {\n    aPosition: {\n      buffer: new Float32Array([0, 0, 1, 0, 1, 1, 0, 1]),\n      format: \"float32x2\",\n      stride: 2 * 4,\n      offset: 0\n    }\n  },\n  indexBuffer: new Uint32Array([0, 1, 2, 0, 2, 3])\n});\nclass FilterSystem {\n  constructor(renderer) {\n    this._filterStackIndex = 0;\n    this._filterStack = [];\n    this._filterGlobalUniforms = new UniformGroup({\n      uInputSize: { value: new Float32Array(4), type: \"vec4<f32>\" },\n      uInputPixel: { value: new Float32Array(4), type: \"vec4<f32>\" },\n      uInputClamp: { value: new Float32Array(4), type: \"vec4<f32>\" },\n      uOutputFrame: { value: new Float32Array(4), type: \"vec4<f32>\" },\n      uGlobalFrame: { value: new Float32Array(4), type: \"vec4<f32>\" },\n      uOutputTexture: { value: new Float32Array(4), type: \"vec4<f32>\" }\n    });\n    this._globalFilterBindGroup = new BindGroup({});\n    this.renderer = renderer;\n  }\n  /**\n   * The back texture of the currently active filter. Requires the filter to have `blendRequired` set to true.\n   * @readonly\n   */\n  get activeBackTexture() {\n    return this._activeFilterData?.backTexture;\n  }\n  push(instruction) {\n    const renderer = this.renderer;\n    const filters = instruction.filterEffect.filters;\n    if (!this._filterStack[this._filterStackIndex]) {\n      this._filterStack[this._filterStackIndex] = this._getFilterData();\n    }\n    const filterData = this._filterStack[this._filterStackIndex];\n    this._filterStackIndex++;\n    if (filters.length === 0) {\n      filterData.skip = true;\n      return;\n    }\n    const bounds = filterData.bounds;\n    if (instruction.renderables) {\n      getGlobalRenderableBounds(instruction.renderables, bounds);\n    } else if (instruction.filterEffect.filterArea) {\n      bounds.clear();\n      bounds.addRect(instruction.filterEffect.filterArea);\n      bounds.applyMatrix(instruction.container.worldTransform);\n    } else {\n      instruction.container.getFastGlobalBounds(true, bounds);\n    }\n    if (instruction.container) {\n      const renderGroup = instruction.container.renderGroup || instruction.container.parentRenderGroup;\n      const filterFrameTransform = renderGroup.cacheToLocalTransform;\n      if (filterFrameTransform) {\n        bounds.applyMatrix(filterFrameTransform);\n      }\n    }\n    const colorTextureSource = renderer.renderTarget.renderTarget.colorTexture.source;\n    let resolution = Infinity;\n    let padding = 0;\n    let antialias = true;\n    let blendRequired = false;\n    let enabled = false;\n    let clipToViewport = true;\n    for (let i = 0; i < filters.length; i++) {\n      const filter = filters[i];\n      resolution = Math.min(resolution, filter.resolution === \"inherit\" ? colorTextureSource._resolution : filter.resolution);\n      padding += filter.padding;\n      if (filter.antialias === \"off\") {\n        antialias = false;\n      } else if (filter.antialias === \"inherit\") {\n        antialias && (antialias = colorTextureSource.antialias);\n      }\n      if (!filter.clipToViewport) {\n        clipToViewport = false;\n      }\n      const isCompatible = !!(filter.compatibleRenderers & renderer.type);\n      if (!isCompatible) {\n        enabled = false;\n        break;\n      }\n      if (filter.blendRequired && !(renderer.backBuffer?.useBackBuffer ?? true)) {\n        warn(\"Blend filter requires backBuffer on WebGL renderer to be enabled. Set `useBackBuffer: true` in the renderer options.\");\n        enabled = false;\n        break;\n      }\n      enabled = filter.enabled || enabled;\n      blendRequired || (blendRequired = filter.blendRequired);\n    }\n    if (!enabled) {\n      filterData.skip = true;\n      return;\n    }\n    if (clipToViewport) {\n      const viewPort = renderer.renderTarget.rootViewPort;\n      const rootResolution = renderer.renderTarget.renderTarget.resolution;\n      bounds.fitBounds(0, viewPort.width / rootResolution, 0, viewPort.height / rootResolution);\n    }\n    bounds.scale(resolution).ceil().scale(1 / resolution).pad(padding | 0);\n    if (!bounds.isPositive) {\n      filterData.skip = true;\n      return;\n    }\n    filterData.skip = false;\n    filterData.bounds = bounds;\n    filterData.blendRequired = blendRequired;\n    filterData.container = instruction.container;\n    filterData.filterEffect = instruction.filterEffect;\n    filterData.previousRenderSurface = renderer.renderTarget.renderSurface;\n    filterData.inputTexture = TexturePool.getOptimalTexture(\n      bounds.width,\n      bounds.height,\n      resolution,\n      antialias\n    );\n    renderer.renderTarget.bind(filterData.inputTexture, true);\n    renderer.globalUniforms.push({\n      offset: bounds\n    });\n  }\n  pop() {\n    const renderer = this.renderer;\n    this._filterStackIndex--;\n    const filterData = this._filterStack[this._filterStackIndex];\n    if (filterData.skip) {\n      return;\n    }\n    this._activeFilterData = filterData;\n    const inputTexture = filterData.inputTexture;\n    const bounds = filterData.bounds;\n    let backTexture = Texture.EMPTY;\n    renderer.renderTarget.finishRenderPass();\n    if (filterData.blendRequired) {\n      const previousBounds = this._filterStackIndex > 0 ? this._filterStack[this._filterStackIndex - 1].bounds : null;\n      const renderTarget = renderer.renderTarget.getRenderTarget(filterData.previousRenderSurface);\n      backTexture = this.getBackTexture(renderTarget, bounds, previousBounds);\n    }\n    filterData.backTexture = backTexture;\n    const filters = filterData.filterEffect.filters;\n    this._globalFilterBindGroup.setResource(inputTexture.source.style, 2);\n    this._globalFilterBindGroup.setResource(backTexture.source, 3);\n    renderer.globalUniforms.pop();\n    if (filters.length === 1) {\n      filters[0].apply(this, inputTexture, filterData.previousRenderSurface, false);\n      TexturePool.returnTexture(inputTexture);\n    } else {\n      let flip = filterData.inputTexture;\n      let flop = TexturePool.getOptimalTexture(\n        bounds.width,\n        bounds.height,\n        flip.source._resolution,\n        false\n      );\n      let i = 0;\n      for (i = 0; i < filters.length - 1; ++i) {\n        const filter = filters[i];\n        filter.apply(this, flip, flop, true);\n        const t = flip;\n        flip = flop;\n        flop = t;\n      }\n      filters[i].apply(this, flip, filterData.previousRenderSurface, false);\n      TexturePool.returnTexture(flip);\n      TexturePool.returnTexture(flop);\n    }\n    if (filterData.blendRequired) {\n      TexturePool.returnTexture(backTexture);\n    }\n  }\n  getBackTexture(lastRenderSurface, bounds, previousBounds) {\n    const backgroundResolution = lastRenderSurface.colorTexture.source._resolution;\n    const backTexture = TexturePool.getOptimalTexture(\n      bounds.width,\n      bounds.height,\n      backgroundResolution,\n      false\n    );\n    let x = bounds.minX;\n    let y = bounds.minY;\n    if (previousBounds) {\n      x -= previousBounds.minX;\n      y -= previousBounds.minY;\n    }\n    x = Math.floor(x * backgroundResolution);\n    y = Math.floor(y * backgroundResolution);\n    const width = Math.ceil(bounds.width * backgroundResolution);\n    const height = Math.ceil(bounds.height * backgroundResolution);\n    this.renderer.renderTarget.copyToTexture(\n      lastRenderSurface,\n      backTexture,\n      { x, y },\n      { width, height },\n      { x: 0, y: 0 }\n    );\n    return backTexture;\n  }\n  applyFilter(filter, input, output, clear) {\n    const renderer = this.renderer;\n    const filterData = this._filterStack[this._filterStackIndex];\n    const bounds = filterData.bounds;\n    const offset = Point.shared;\n    const previousRenderSurface = filterData.previousRenderSurface;\n    const isFinalTarget = previousRenderSurface === output;\n    let resolution = this.renderer.renderTarget.rootRenderTarget.colorTexture.source._resolution;\n    let currentIndex = this._filterStackIndex - 1;\n    while (currentIndex > 0 && this._filterStack[currentIndex].skip) {\n      --currentIndex;\n    }\n    if (currentIndex > 0) {\n      resolution = this._filterStack[currentIndex].inputTexture.source._resolution;\n    }\n    const filterUniforms = this._filterGlobalUniforms;\n    const uniforms = filterUniforms.uniforms;\n    const outputFrame = uniforms.uOutputFrame;\n    const inputSize = uniforms.uInputSize;\n    const inputPixel = uniforms.uInputPixel;\n    const inputClamp = uniforms.uInputClamp;\n    const globalFrame = uniforms.uGlobalFrame;\n    const outputTexture = uniforms.uOutputTexture;\n    if (isFinalTarget) {\n      let lastIndex = this._filterStackIndex;\n      while (lastIndex > 0) {\n        lastIndex--;\n        const filterData2 = this._filterStack[this._filterStackIndex - 1];\n        if (!filterData2.skip) {\n          offset.x = filterData2.bounds.minX;\n          offset.y = filterData2.bounds.minY;\n          break;\n        }\n      }\n      outputFrame[0] = bounds.minX - offset.x;\n      outputFrame[1] = bounds.minY - offset.y;\n    } else {\n      outputFrame[0] = 0;\n      outputFrame[1] = 0;\n    }\n    outputFrame[2] = input.frame.width;\n    outputFrame[3] = input.frame.height;\n    inputSize[0] = input.source.width;\n    inputSize[1] = input.source.height;\n    inputSize[2] = 1 / inputSize[0];\n    inputSize[3] = 1 / inputSize[1];\n    inputPixel[0] = input.source.pixelWidth;\n    inputPixel[1] = input.source.pixelHeight;\n    inputPixel[2] = 1 / inputPixel[0];\n    inputPixel[3] = 1 / inputPixel[1];\n    inputClamp[0] = 0.5 * inputPixel[2];\n    inputClamp[1] = 0.5 * inputPixel[3];\n    inputClamp[2] = input.frame.width * inputSize[2] - 0.5 * inputPixel[2];\n    inputClamp[3] = input.frame.height * inputSize[3] - 0.5 * inputPixel[3];\n    const rootTexture = this.renderer.renderTarget.rootRenderTarget.colorTexture;\n    globalFrame[0] = offset.x * resolution;\n    globalFrame[1] = offset.y * resolution;\n    globalFrame[2] = rootTexture.source.width * resolution;\n    globalFrame[3] = rootTexture.source.height * resolution;\n    const renderTarget = this.renderer.renderTarget.getRenderTarget(output);\n    renderer.renderTarget.bind(output, !!clear);\n    if (output instanceof Texture) {\n      outputTexture[0] = output.frame.width;\n      outputTexture[1] = output.frame.height;\n    } else {\n      outputTexture[0] = renderTarget.width;\n      outputTexture[1] = renderTarget.height;\n    }\n    outputTexture[2] = renderTarget.isRoot ? -1 : 1;\n    filterUniforms.update();\n    if (renderer.renderPipes.uniformBatch) {\n      const batchUniforms = renderer.renderPipes.uniformBatch.getUboResource(filterUniforms);\n      this._globalFilterBindGroup.setResource(batchUniforms, 0);\n    } else {\n      this._globalFilterBindGroup.setResource(filterUniforms, 0);\n    }\n    this._globalFilterBindGroup.setResource(input.source, 1);\n    this._globalFilterBindGroup.setResource(input.source.style, 2);\n    filter.groups[0] = this._globalFilterBindGroup;\n    renderer.encoder.draw({\n      geometry: quadGeometry,\n      shader: filter,\n      state: filter._state,\n      topology: \"triangle-list\"\n    });\n    if (renderer.type === RendererType.WEBGL) {\n      renderer.renderTarget.finishRenderPass();\n    }\n  }\n  _getFilterData() {\n    return {\n      skip: false,\n      inputTexture: null,\n      bounds: new Bounds(),\n      container: null,\n      filterEffect: null,\n      blendRequired: false,\n      previousRenderSurface: null\n    };\n  }\n  /**\n   * Multiply _input normalized coordinates_ to this matrix to get _sprite texture normalized coordinates_.\n   *\n   * Use `outputMatrix * vTextureCoord` in the shader.\n   * @param outputMatrix - The matrix to output to.\n   * @param {Sprite} sprite - The sprite to map to.\n   * @returns The mapped matrix.\n   */\n  calculateSpriteMatrix(outputMatrix, sprite) {\n    const data = this._activeFilterData;\n    const mappedMatrix = outputMatrix.set(\n      data.inputTexture._source.width,\n      0,\n      0,\n      data.inputTexture._source.height,\n      data.bounds.minX,\n      data.bounds.minY\n    );\n    const worldTransform = sprite.worldTransform.copyTo(Matrix.shared);\n    const renderGroup = sprite.renderGroup || sprite.parentRenderGroup;\n    if (renderGroup && renderGroup.cacheToLocalTransform) {\n      worldTransform.prepend(renderGroup.cacheToLocalTransform);\n    }\n    worldTransform.invert();\n    mappedMatrix.prepend(worldTransform);\n    mappedMatrix.scale(\n      1 / sprite.texture.frame.width,\n      1 / sprite.texture.frame.height\n    );\n    mappedMatrix.translate(sprite.anchor.x, sprite.anchor.y);\n    return mappedMatrix;\n  }\n}\nFilterSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"filter\"\n};\nconst _MeshGeometry = class _MeshGeometry2 extends Geometry {\n  constructor(...args) {\n    let options = args[0] ?? {};\n    if (options instanceof Float32Array) {\n      deprecation(v8_0_0, \"use new MeshGeometry({ positions, uvs, indices }) instead\");\n      options = {\n        positions: options,\n        uvs: args[1],\n        indices: args[2]\n      };\n    }\n    options = { ..._MeshGeometry2.defaultOptions, ...options };\n    const positions = options.positions || new Float32Array([0, 0, 1, 0, 1, 1, 0, 1]);\n    let uvs = options.uvs;\n    if (!uvs) {\n      if (options.positions) {\n        uvs = new Float32Array(positions.length);\n      } else {\n        uvs = new Float32Array([0, 0, 1, 0, 1, 1, 0, 1]);\n      }\n    }\n    const indices = options.indices || new Uint32Array([0, 1, 2, 0, 2, 3]);\n    const shrinkToFit = options.shrinkBuffersToFit;\n    const positionBuffer = new Buffer({\n      data: positions,\n      label: \"attribute-mesh-positions\",\n      shrinkToFit,\n      usage: BufferUsage.VERTEX | BufferUsage.COPY_DST\n    });\n    const uvBuffer = new Buffer({\n      data: uvs,\n      label: \"attribute-mesh-uvs\",\n      shrinkToFit,\n      usage: BufferUsage.VERTEX | BufferUsage.COPY_DST\n    });\n    const indexBuffer = new Buffer({\n      data: indices,\n      label: \"index-mesh-buffer\",\n      shrinkToFit,\n      usage: BufferUsage.INDEX | BufferUsage.COPY_DST\n    });\n    super({\n      attributes: {\n        aPosition: {\n          buffer: positionBuffer,\n          format: \"float32x2\",\n          stride: 2 * 4,\n          offset: 0\n        },\n        aUV: {\n          buffer: uvBuffer,\n          format: \"float32x2\",\n          stride: 2 * 4,\n          offset: 0\n        }\n      },\n      indexBuffer,\n      topology: options.topology\n    });\n    this.batchMode = \"auto\";\n  }\n  /** The positions of the mesh. */\n  get positions() {\n    return this.attributes.aPosition.buffer.data;\n  }\n  /**\n   * Set the positions of the mesh.\n   * When setting the positions, its important that the uvs array is at least as long as the positions array.\n   * otherwise the geometry will not be valid.\n   * @param {Float32Array} value - The positions of the mesh.\n   */\n  set positions(value) {\n    this.attributes.aPosition.buffer.data = value;\n  }\n  /** The UVs of the mesh. */\n  get uvs() {\n    return this.attributes.aUV.buffer.data;\n  }\n  /**\n   * Set the UVs of the mesh.\n   * Its important that the uvs array you set is at least as long as the positions array.\n   * otherwise the geometry will not be valid.\n   * @param {Float32Array} value - The UVs of the mesh.\n   */\n  set uvs(value) {\n    this.attributes.aUV.buffer.data = value;\n  }\n  /** The indices of the mesh. */\n  get indices() {\n    return this.indexBuffer.data;\n  }\n  set indices(value) {\n    this.indexBuffer.data = value;\n  }\n};\n_MeshGeometry.defaultOptions = {\n  topology: \"triangle-list\",\n  shrinkBuffersToFit: false\n};\nlet MeshGeometry = _MeshGeometry;\nfunction textStyleToCSS(style) {\n  const stroke = style._stroke;\n  const fill = style._fill;\n  const cssStyleString = [\n    `color: ${Color.shared.setValue(fill.color).toHex()}`,\n    `font-size: ${style.fontSize}px`,\n    `font-family: ${style.fontFamily}`,\n    `font-weight: ${style.fontWeight}`,\n    `font-style: ${style.fontStyle}`,\n    `font-variant: ${style.fontVariant}`,\n    `letter-spacing: ${style.letterSpacing}px`,\n    `text-align: ${style.align}`,\n    `padding: ${style.padding}px`,\n    `white-space: ${style.whiteSpace === \"pre\" && style.wordWrap ? \"pre-wrap\" : style.whiteSpace}`,\n    ...style.lineHeight ? [`line-height: ${style.lineHeight}px`] : [],\n    ...style.wordWrap ? [\n      `word-wrap: ${style.breakWords ? \"break-all\" : \"break-word\"}`,\n      `max-width: ${style.wordWrapWidth}px`\n    ] : [],\n    ...stroke ? [strokeToCSS(stroke)] : [],\n    ...style.dropShadow ? [dropShadowToCSS(style.dropShadow)] : [],\n    ...style.cssOverrides\n  ].join(\";\");\n  const cssStyles = [`div { ${cssStyleString} }`];\n  tagStyleToCSS(style.tagStyles, cssStyles);\n  return cssStyles.join(\" \");\n}\nfunction dropShadowToCSS(dropShadowStyle) {\n  const color = Color.shared.setValue(dropShadowStyle.color).setAlpha(dropShadowStyle.alpha).toHexa();\n  const x = Math.round(Math.cos(dropShadowStyle.angle) * dropShadowStyle.distance);\n  const y = Math.round(Math.sin(dropShadowStyle.angle) * dropShadowStyle.distance);\n  const position = `${x}px ${y}px`;\n  if (dropShadowStyle.blur > 0) {\n    return `text-shadow: ${position} ${dropShadowStyle.blur}px ${color}`;\n  }\n  return `text-shadow: ${position} ${color}`;\n}\nfunction strokeToCSS(stroke) {\n  return [\n    `-webkit-text-stroke-width: ${stroke.width}px`,\n    `-webkit-text-stroke-color: ${Color.shared.setValue(stroke.color).toHex()}`,\n    `text-stroke-width: ${stroke.width}px`,\n    `text-stroke-color: ${Color.shared.setValue(stroke.color).toHex()}`,\n    \"paint-order: stroke\"\n  ].join(\";\");\n}\nconst templates = {\n  fontSize: `font-size: {{VALUE}}px`,\n  fontFamily: `font-family: {{VALUE}}`,\n  fontWeight: `font-weight: {{VALUE}}`,\n  fontStyle: `font-style: {{VALUE}}`,\n  fontVariant: `font-variant: {{VALUE}}`,\n  letterSpacing: `letter-spacing: {{VALUE}}px`,\n  align: `text-align: {{VALUE}}`,\n  padding: `padding: {{VALUE}}px`,\n  whiteSpace: `white-space: {{VALUE}}`,\n  lineHeight: `line-height: {{VALUE}}px`,\n  wordWrapWidth: `max-width: {{VALUE}}px`\n};\nconst transform = {\n  fill: (value) => `color: ${Color.shared.setValue(value).toHex()}`,\n  breakWords: (value) => `word-wrap: ${value ? \"break-all\" : \"break-word\"}`,\n  stroke: strokeToCSS,\n  dropShadow: dropShadowToCSS\n};\nfunction tagStyleToCSS(tagStyles, out) {\n  for (const i in tagStyles) {\n    const tagStyle = tagStyles[i];\n    const cssTagStyle = [];\n    for (const j in tagStyle) {\n      if (transform[j]) {\n        cssTagStyle.push(transform[j](tagStyle[j]));\n      } else if (templates[j]) {\n        cssTagStyle.push(templates[j].replace(\"{{VALUE}}\", tagStyle[j]));\n      }\n    }\n    out.push(`${i} { ${cssTagStyle.join(\";\")} }`);\n  }\n}\nclass HTMLTextStyle extends TextStyle {\n  constructor(options = {}) {\n    super(options);\n    this._cssOverrides = [];\n    this.cssOverrides ?? (this.cssOverrides = options.cssOverrides);\n    this.tagStyles = options.tagStyles ?? {};\n  }\n  /** List of style overrides that will be applied to the HTML text. */\n  set cssOverrides(value) {\n    this._cssOverrides = value instanceof Array ? value : [value];\n    this.update();\n  }\n  get cssOverrides() {\n    return this._cssOverrides;\n  }\n  _generateKey() {\n    this._styleKey = generateTextStyleKey(this) + this._cssOverrides.join(\"-\");\n    return this._styleKey;\n  }\n  update() {\n    this._cssStyle = null;\n    super.update();\n  }\n  /**\n   * Creates a new HTMLTextStyle object with the same values as this one.\n   * @returns New cloned HTMLTextStyle object\n   */\n  clone() {\n    return new HTMLTextStyle({\n      align: this.align,\n      breakWords: this.breakWords,\n      dropShadow: this.dropShadow ? { ...this.dropShadow } : null,\n      fill: this._fill,\n      fontFamily: this.fontFamily,\n      fontSize: this.fontSize,\n      fontStyle: this.fontStyle,\n      fontVariant: this.fontVariant,\n      fontWeight: this.fontWeight,\n      letterSpacing: this.letterSpacing,\n      lineHeight: this.lineHeight,\n      padding: this.padding,\n      stroke: this._stroke,\n      whiteSpace: this.whiteSpace,\n      wordWrap: this.wordWrap,\n      wordWrapWidth: this.wordWrapWidth,\n      cssOverrides: this.cssOverrides\n    });\n  }\n  get cssStyle() {\n    if (!this._cssStyle) {\n      this._cssStyle = textStyleToCSS(this);\n    }\n    return this._cssStyle;\n  }\n  /**\n   * Add a style override, this can be any CSS property\n   * it will override any built-in style. This is the\n   * property and the value as a string (e.g., `color: red`).\n   * This will override any other internal style.\n   * @param {string} value - CSS style(s) to add.\n   * @example\n   * style.addOverride('background-color: red');\n   */\n  addOverride(...value) {\n    const toAdd = value.filter((v) => !this.cssOverrides.includes(v));\n    if (toAdd.length > 0) {\n      this.cssOverrides.push(...toAdd);\n      this.update();\n    }\n  }\n  /**\n   * Remove any overrides that match the value.\n   * @param {string} value - CSS style to remove.\n   * @example\n   * style.removeOverride('background-color: red');\n   */\n  removeOverride(...value) {\n    const toRemove = value.filter((v) => this.cssOverrides.includes(v));\n    if (toRemove.length > 0) {\n      this.cssOverrides = this.cssOverrides.filter((v) => !toRemove.includes(v));\n      this.update();\n    }\n  }\n  set fill(value) {\n    if (typeof value !== \"string\" && typeof value !== \"number\") {\n      warn(\"[HTMLTextStyle] only color fill is not supported by HTMLText\");\n    }\n    super.fill = value;\n  }\n  set stroke(value) {\n    if (value && typeof value !== \"string\" && typeof value !== \"number\") {\n      warn(\"[HTMLTextStyle] only color stroke is not supported by HTMLText\");\n    }\n    super.stroke = value;\n  }\n}\nconst nssvg = \"http://www.w3.org/2000/svg\";\nconst nsxhtml = \"http://www.w3.org/1999/xhtml\";\nclass HTMLTextRenderData {\n  constructor() {\n    this.svgRoot = document.createElementNS(nssvg, \"svg\");\n    this.foreignObject = document.createElementNS(nssvg, \"foreignObject\");\n    this.domElement = document.createElementNS(nsxhtml, \"div\");\n    this.styleElement = document.createElementNS(nsxhtml, \"style\");\n    this.image = new Image();\n    const { foreignObject, svgRoot, styleElement, domElement } = this;\n    foreignObject.setAttribute(\"width\", \"10000\");\n    foreignObject.setAttribute(\"height\", \"10000\");\n    foreignObject.style.overflow = \"hidden\";\n    svgRoot.appendChild(foreignObject);\n    foreignObject.appendChild(styleElement);\n    foreignObject.appendChild(domElement);\n  }\n}\nlet tempHTMLTextRenderData;\nfunction measureHtmlText(text, style, fontStyleCSS, htmlTextRenderData) {\n  htmlTextRenderData || (htmlTextRenderData = tempHTMLTextRenderData || (tempHTMLTextRenderData = new HTMLTextRenderData()));\n  const { domElement, styleElement, svgRoot } = htmlTextRenderData;\n  domElement.innerHTML = `<style>${style.cssStyle};</style><div style='padding:0'>${text}</div>`;\n  domElement.setAttribute(\"style\", \"transform-origin: top left; display: inline-block\");\n  if (fontStyleCSS) {\n    styleElement.textContent = fontStyleCSS;\n  }\n  document.body.appendChild(svgRoot);\n  const contentBounds = domElement.getBoundingClientRect();\n  svgRoot.remove();\n  const doublePadding = style.padding * 2;\n  return {\n    width: contentBounds.width - doublePadding,\n    height: contentBounds.height - doublePadding\n  };\n}\nclass GraphicsPipe {\n  constructor(renderer, adaptor) {\n    this.state = State.for2d();\n    this._graphicsBatchesHash = /* @__PURE__ */ Object.create(null);\n    this._destroyRenderableBound = this.destroyRenderable.bind(this);\n    this.renderer = renderer;\n    this._adaptor = adaptor;\n    this._adaptor.init();\n    this.renderer.renderableGC.addManagedHash(this, \"_graphicsBatchesHash\");\n  }\n  validateRenderable(graphics) {\n    const context = graphics.context;\n    const wasBatched = !!this._graphicsBatchesHash[graphics.uid];\n    const gpuContext = this.renderer.graphicsContext.updateGpuContext(context);\n    if (gpuContext.isBatchable || wasBatched !== gpuContext.isBatchable) {\n      return true;\n    }\n    return false;\n  }\n  addRenderable(graphics, instructionSet) {\n    const gpuContext = this.renderer.graphicsContext.updateGpuContext(graphics.context);\n    if (graphics.didViewUpdate) {\n      this._rebuild(graphics);\n    }\n    if (gpuContext.isBatchable) {\n      this._addToBatcher(graphics, instructionSet);\n    } else {\n      this.renderer.renderPipes.batch.break(instructionSet);\n      instructionSet.add(graphics);\n    }\n  }\n  updateRenderable(graphics) {\n    const batches = this._graphicsBatchesHash[graphics.uid];\n    if (batches) {\n      for (let i = 0; i < batches.length; i++) {\n        const batch = batches[i];\n        batch._batcher.updateElement(batch);\n      }\n    }\n  }\n  destroyRenderable(graphics) {\n    if (this._graphicsBatchesHash[graphics.uid]) {\n      this._removeBatchForRenderable(graphics.uid);\n    }\n    graphics.off(\"destroyed\", this._destroyRenderableBound);\n  }\n  execute(graphics) {\n    if (!graphics.isRenderable)\n      return;\n    const renderer = this.renderer;\n    const context = graphics.context;\n    const contextSystem = renderer.graphicsContext;\n    if (!contextSystem.getGpuContext(context).batches.length) {\n      return;\n    }\n    const shader = context.customShader || this._adaptor.shader;\n    this.state.blendMode = graphics.groupBlendMode;\n    const localUniforms = shader.resources.localUniforms.uniforms;\n    localUniforms.uTransformMatrix = graphics.groupTransform;\n    localUniforms.uRound = renderer._roundPixels | graphics._roundPixels;\n    color32BitToUniform(\n      graphics.groupColorAlpha,\n      localUniforms.uColor,\n      0\n    );\n    this._adaptor.execute(this, graphics);\n  }\n  _rebuild(graphics) {\n    const wasBatched = !!this._graphicsBatchesHash[graphics.uid];\n    const gpuContext = this.renderer.graphicsContext.updateGpuContext(graphics.context);\n    if (wasBatched) {\n      this._removeBatchForRenderable(graphics.uid);\n    }\n    if (gpuContext.isBatchable) {\n      this._initBatchesForRenderable(graphics);\n    }\n    graphics.batched = gpuContext.isBatchable;\n  }\n  _addToBatcher(graphics, instructionSet) {\n    const batchPipe = this.renderer.renderPipes.batch;\n    const batches = this._getBatchesForRenderable(graphics);\n    for (let i = 0; i < batches.length; i++) {\n      const batch = batches[i];\n      batchPipe.addToBatch(batch, instructionSet);\n    }\n  }\n  _getBatchesForRenderable(graphics) {\n    return this._graphicsBatchesHash[graphics.uid] || this._initBatchesForRenderable(graphics);\n  }\n  _initBatchesForRenderable(graphics) {\n    const context = graphics.context;\n    const gpuContext = this.renderer.graphicsContext.getGpuContext(context);\n    const roundPixels = this.renderer._roundPixels | graphics._roundPixels;\n    const batches = gpuContext.batches.map((batch) => {\n      const batchClone = BigPool.get(BatchableGraphics);\n      batch.copyTo(batchClone);\n      batchClone.renderable = graphics;\n      batchClone.roundPixels = roundPixels;\n      return batchClone;\n    });\n    if (this._graphicsBatchesHash[graphics.uid] === void 0) {\n      graphics.on(\"destroyed\", this._destroyRenderableBound);\n    }\n    this._graphicsBatchesHash[graphics.uid] = batches;\n    return batches;\n  }\n  _removeBatchForRenderable(graphicsUid) {\n    this._graphicsBatchesHash[graphicsUid].forEach((batch) => {\n      BigPool.return(batch);\n    });\n    this._graphicsBatchesHash[graphicsUid] = null;\n  }\n  destroy() {\n    this.renderer = null;\n    this._adaptor.destroy();\n    this._adaptor = null;\n    this.state = null;\n    for (const i in this._graphicsBatchesHash) {\n      this._removeBatchForRenderable(i);\n    }\n    this._graphicsBatchesHash = null;\n  }\n}\nGraphicsPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"graphics\"\n};\nconst _PlaneGeometry = class _PlaneGeometry2 extends MeshGeometry {\n  constructor(...args) {\n    super({});\n    let options = args[0] ?? {};\n    if (typeof options === \"number\") {\n      deprecation(v8_0_0, \"PlaneGeometry constructor changed please use { width, height, verticesX, verticesY } instead\");\n      options = {\n        width: options,\n        height: args[1],\n        verticesX: args[2],\n        verticesY: args[3]\n      };\n    }\n    this.build(options);\n  }\n  /**\n   * Refreshes plane coordinates\n   * @param options - Options to be applied to plane geometry\n   */\n  build(options) {\n    options = { ..._PlaneGeometry2.defaultOptions, ...options };\n    this.verticesX = this.verticesX ?? options.verticesX;\n    this.verticesY = this.verticesY ?? options.verticesY;\n    this.width = this.width ?? options.width;\n    this.height = this.height ?? options.height;\n    const total = this.verticesX * this.verticesY;\n    const verts = [];\n    const uvs = [];\n    const indices = [];\n    const verticesX = this.verticesX - 1;\n    const verticesY = this.verticesY - 1;\n    const sizeX = this.width / verticesX;\n    const sizeY = this.height / verticesY;\n    for (let i = 0; i < total; i++) {\n      const x = i % this.verticesX;\n      const y = i / this.verticesX | 0;\n      verts.push(x * sizeX, y * sizeY);\n      uvs.push(x / verticesX, y / verticesY);\n    }\n    const totalSub = verticesX * verticesY;\n    for (let i = 0; i < totalSub; i++) {\n      const xpos = i % verticesX;\n      const ypos = i / verticesX | 0;\n      const value = ypos * this.verticesX + xpos;\n      const value2 = ypos * this.verticesX + xpos + 1;\n      const value3 = (ypos + 1) * this.verticesX + xpos;\n      const value4 = (ypos + 1) * this.verticesX + xpos + 1;\n      indices.push(\n        value,\n        value2,\n        value3,\n        value2,\n        value4,\n        value3\n      );\n    }\n    this.buffers[0].data = new Float32Array(verts);\n    this.buffers[1].data = new Float32Array(uvs);\n    this.indexBuffer.data = new Uint32Array(indices);\n    this.buffers[0].update();\n    this.buffers[1].update();\n    this.indexBuffer.update();\n  }\n};\n_PlaneGeometry.defaultOptions = {\n  width: 100,\n  height: 100,\n  verticesX: 10,\n  verticesY: 10\n};\nlet PlaneGeometry = _PlaneGeometry;\nclass BatchableMesh {\n  constructor() {\n    this.batcherName = \"default\";\n    this.packAsQuad = false;\n    this.indexOffset = 0;\n    this.attributeOffset = 0;\n    this.roundPixels = 0;\n    this._batcher = null;\n    this._batch = null;\n    this._uvUpdateId = -1;\n    this._textureMatrixUpdateId = -1;\n  }\n  get blendMode() {\n    return this.renderable.groupBlendMode;\n  }\n  get topology() {\n    return this._topology || this.geometry.topology;\n  }\n  set topology(value) {\n    this._topology = value;\n  }\n  reset() {\n    this.renderable = null;\n    this.texture = null;\n    this._batcher = null;\n    this._batch = null;\n    this.geometry = null;\n    this._uvUpdateId = -1;\n    this._textureMatrixUpdateId = -1;\n  }\n  /**\n   * Sets the texture for the batchable mesh.\n   * As it does so, it resets the texture matrix update ID.\n   * this is to ensure that the texture matrix is recalculated when the uvs are referenced\n   * @param value - The texture to set.\n   */\n  setTexture(value) {\n    if (this.texture === value)\n      return;\n    this.texture = value;\n    this._textureMatrixUpdateId = -1;\n  }\n  get uvs() {\n    const geometry = this.geometry;\n    const uvBuffer = geometry.getBuffer(\"aUV\");\n    const uvs = uvBuffer.data;\n    let transformedUvs = uvs;\n    const textureMatrix = this.texture.textureMatrix;\n    if (!textureMatrix.isSimple) {\n      transformedUvs = this._transformedUvs;\n      if (this._textureMatrixUpdateId !== textureMatrix._updateID || this._uvUpdateId !== uvBuffer._updateID) {\n        if (!transformedUvs || transformedUvs.length < uvs.length) {\n          transformedUvs = this._transformedUvs = new Float32Array(uvs.length);\n        }\n        this._textureMatrixUpdateId = textureMatrix._updateID;\n        this._uvUpdateId = uvBuffer._updateID;\n        textureMatrix.multiplyUvs(uvs, transformedUvs);\n      }\n    }\n    return transformedUvs;\n  }\n  get positions() {\n    return this.geometry.positions;\n  }\n  get indices() {\n    return this.geometry.indices;\n  }\n  get color() {\n    return this.renderable.groupColorAlpha;\n  }\n  get groupTransform() {\n    return this.renderable.groupTransform;\n  }\n  get attributeSize() {\n    return this.geometry.positions.length / 2;\n  }\n  get indexSize() {\n    return this.geometry.indices.length;\n  }\n}\nclass MeshPipe {\n  constructor(renderer, adaptor) {\n    this.localUniforms = new UniformGroup({\n      uTransformMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      uColor: { value: new Float32Array([1, 1, 1, 1]), type: \"vec4<f32>\" },\n      uRound: { value: 0, type: \"f32\" }\n    });\n    this.localUniformsBindGroup = new BindGroup({\n      0: this.localUniforms\n    });\n    this._meshDataHash = /* @__PURE__ */ Object.create(null);\n    this._gpuBatchableMeshHash = /* @__PURE__ */ Object.create(null);\n    this._destroyRenderableBound = this.destroyRenderable.bind(this);\n    this.renderer = renderer;\n    this._adaptor = adaptor;\n    this._adaptor.init();\n    renderer.renderableGC.addManagedHash(this, \"_gpuBatchableMeshHash\");\n    renderer.renderableGC.addManagedHash(this, \"_meshDataHash\");\n  }\n  validateRenderable(mesh) {\n    const meshData = this._getMeshData(mesh);\n    const wasBatched = meshData.batched;\n    const isBatched = mesh.batched;\n    meshData.batched = isBatched;\n    if (wasBatched !== isBatched) {\n      return true;\n    } else if (isBatched) {\n      const geometry = mesh._geometry;\n      if (geometry.indices.length !== meshData.indexSize || geometry.positions.length !== meshData.vertexSize) {\n        meshData.indexSize = geometry.indices.length;\n        meshData.vertexSize = geometry.positions.length;\n        return true;\n      }\n      const batchableMesh = this._getBatchableMesh(mesh);\n      return !batchableMesh._batcher.checkAndUpdateTexture(\n        batchableMesh,\n        mesh.texture\n      );\n    }\n    return false;\n  }\n  addRenderable(mesh, instructionSet) {\n    const batcher = this.renderer.renderPipes.batch;\n    const { batched } = this._getMeshData(mesh);\n    if (batched) {\n      const gpuBatchableMesh = this._getBatchableMesh(mesh);\n      gpuBatchableMesh.texture = mesh._texture;\n      gpuBatchableMesh.geometry = mesh._geometry;\n      batcher.addToBatch(gpuBatchableMesh, instructionSet);\n    } else {\n      batcher.break(instructionSet);\n      instructionSet.add(mesh);\n    }\n  }\n  updateRenderable(mesh) {\n    if (mesh.batched) {\n      const gpuBatchableMesh = this._gpuBatchableMeshHash[mesh.uid];\n      gpuBatchableMesh.setTexture(mesh._texture);\n      gpuBatchableMesh.geometry = mesh._geometry;\n      gpuBatchableMesh._batcher.updateElement(gpuBatchableMesh);\n    }\n  }\n  destroyRenderable(mesh) {\n    this._meshDataHash[mesh.uid] = null;\n    const gpuMesh = this._gpuBatchableMeshHash[mesh.uid];\n    if (gpuMesh) {\n      BigPool.return(gpuMesh);\n      this._gpuBatchableMeshHash[mesh.uid] = null;\n    }\n    mesh.off(\"destroyed\", this._destroyRenderableBound);\n  }\n  execute(mesh) {\n    if (!mesh.isRenderable)\n      return;\n    mesh.state.blendMode = getAdjustedBlendModeBlend(mesh.groupBlendMode, mesh.texture._source);\n    const localUniforms = this.localUniforms;\n    localUniforms.uniforms.uTransformMatrix = mesh.groupTransform;\n    localUniforms.uniforms.uRound = this.renderer._roundPixels | mesh._roundPixels;\n    localUniforms.update();\n    color32BitToUniform(\n      mesh.groupColorAlpha,\n      localUniforms.uniforms.uColor,\n      0\n    );\n    this._adaptor.execute(this, mesh);\n  }\n  _getMeshData(mesh) {\n    return this._meshDataHash[mesh.uid] || this._initMeshData(mesh);\n  }\n  _initMeshData(mesh) {\n    this._meshDataHash[mesh.uid] = {\n      batched: mesh.batched,\n      indexSize: mesh._geometry.indices?.length,\n      vertexSize: mesh._geometry.positions?.length\n    };\n    mesh.on(\"destroyed\", this._destroyRenderableBound);\n    return this._meshDataHash[mesh.uid];\n  }\n  _getBatchableMesh(mesh) {\n    return this._gpuBatchableMeshHash[mesh.uid] || this._initBatchableMesh(mesh);\n  }\n  _initBatchableMesh(mesh) {\n    const gpuMesh = BigPool.get(BatchableMesh);\n    gpuMesh.renderable = mesh;\n    gpuMesh.texture = mesh._texture;\n    gpuMesh.transform = mesh.groupTransform;\n    gpuMesh.roundPixels = this.renderer._roundPixels | mesh._roundPixels;\n    this._gpuBatchableMeshHash[mesh.uid] = gpuMesh;\n    return gpuMesh;\n  }\n  destroy() {\n    for (const i in this._gpuBatchableMeshHash) {\n      if (this._gpuBatchableMeshHash[i]) {\n        BigPool.return(this._gpuBatchableMeshHash[i]);\n      }\n    }\n    this._gpuBatchableMeshHash = null;\n    this._meshDataHash = null;\n    this.localUniforms = null;\n    this.localUniformsBindGroup = null;\n    this._adaptor.destroy();\n    this._adaptor = null;\n    this.renderer = null;\n  }\n}\nMeshPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"mesh\"\n};\nclass GlParticleContainerAdaptor {\n  execute(particleContainerPipe, container) {\n    const state = particleContainerPipe.state;\n    const renderer = particleContainerPipe.renderer;\n    const shader = container.shader || particleContainerPipe.defaultShader;\n    shader.resources.uTexture = container.texture._source;\n    shader.resources.uniforms = particleContainerPipe.localUniforms;\n    const gl = renderer.gl;\n    const buffer = particleContainerPipe.getBuffers(container);\n    renderer.shader.bind(shader);\n    renderer.state.set(state);\n    renderer.geometry.bind(buffer.geometry, shader.glProgram);\n    const byteSize = buffer.geometry.indexBuffer.data.BYTES_PER_ELEMENT;\n    const glType = byteSize === 2 ? gl.UNSIGNED_SHORT : gl.UNSIGNED_INT;\n    gl.drawElements(gl.TRIANGLES, container.particleChildren.length * 6, glType, 0);\n  }\n}\nclass GpuParticleContainerAdaptor {\n  execute(particleContainerPipe, container) {\n    const renderer = particleContainerPipe.renderer;\n    const shader = container.shader || particleContainerPipe.defaultShader;\n    shader.groups[0] = renderer.renderPipes.uniformBatch.getUniformBindGroup(particleContainerPipe.localUniforms, true);\n    shader.groups[1] = renderer.texture.getTextureBindGroup(container.texture);\n    const state = particleContainerPipe.state;\n    const buffer = particleContainerPipe.getBuffers(container);\n    renderer.encoder.draw({\n      geometry: buffer.geometry,\n      shader: container.shader || particleContainerPipe.defaultShader,\n      state,\n      size: container.particleChildren.length * 6\n    });\n  }\n}\nfunction createIndicesForQuads(size, outBuffer = null) {\n  const totalIndices = size * 6;\n  if (totalIndices > 65535) {\n    outBuffer || (outBuffer = new Uint32Array(totalIndices));\n  } else {\n    outBuffer || (outBuffer = new Uint16Array(totalIndices));\n  }\n  if (outBuffer.length !== totalIndices) {\n    throw new Error(`Out buffer length is incorrect, got ${outBuffer.length} and expected ${totalIndices}`);\n  }\n  for (let i = 0, j = 0; i < totalIndices; i += 6, j += 4) {\n    outBuffer[i + 0] = j + 0;\n    outBuffer[i + 1] = j + 1;\n    outBuffer[i + 2] = j + 2;\n    outBuffer[i + 3] = j + 0;\n    outBuffer[i + 4] = j + 2;\n    outBuffer[i + 5] = j + 3;\n  }\n  return outBuffer;\n}\nfunction generateParticleUpdateFunction(properties) {\n  return {\n    dynamicUpdate: generateUpdateFunction(properties, true),\n    staticUpdate: generateUpdateFunction(properties, false)\n  };\n}\nfunction generateUpdateFunction(properties, dynamic) {\n  const funcFragments = [];\n  funcFragments.push(`\n      \n        var index = 0;\n\n        for (let i = 0; i < ps.length; ++i)\n        {\n            const p = ps[i];\n\n            `);\n  let offset = 0;\n  for (const i in properties) {\n    const property = properties[i];\n    if (dynamic !== property.dynamic)\n      continue;\n    funcFragments.push(`offset = index + ${offset}`);\n    funcFragments.push(property.code);\n    const attributeInfo = getAttributeInfoFromFormat(property.format);\n    offset += attributeInfo.stride / 4;\n  }\n  funcFragments.push(`\n            index += stride * 4;\n        }\n    `);\n  funcFragments.unshift(`\n        var stride = ${offset};\n    `);\n  const functionSource = funcFragments.join(\"\\n\");\n  return new Function(\"ps\", \"f32v\", \"u32v\", functionSource);\n}\nclass ParticleBuffer {\n  constructor(options) {\n    this._size = 0;\n    this._generateParticleUpdateCache = {};\n    const size = this._size = options.size ?? 1e3;\n    const properties = options.properties;\n    let staticVertexSize = 0;\n    let dynamicVertexSize = 0;\n    for (const i in properties) {\n      const property = properties[i];\n      const attributeInfo = getAttributeInfoFromFormat(property.format);\n      if (property.dynamic) {\n        dynamicVertexSize += attributeInfo.stride;\n      } else {\n        staticVertexSize += attributeInfo.stride;\n      }\n    }\n    this._dynamicStride = dynamicVertexSize / 4;\n    this._staticStride = staticVertexSize / 4;\n    this.staticAttributeBuffer = new ViewableBuffer(size * 4 * staticVertexSize);\n    this.dynamicAttributeBuffer = new ViewableBuffer(size * 4 * dynamicVertexSize);\n    this.indexBuffer = createIndicesForQuads(size);\n    const geometry = new Geometry();\n    let dynamicOffset = 0;\n    let staticOffset = 0;\n    this._staticBuffer = new Buffer({\n      data: new Float32Array(1),\n      label: \"static-particle-buffer\",\n      shrinkToFit: false,\n      usage: BufferUsage.VERTEX | BufferUsage.COPY_DST\n    });\n    this._dynamicBuffer = new Buffer({\n      data: new Float32Array(1),\n      label: \"dynamic-particle-buffer\",\n      shrinkToFit: false,\n      usage: BufferUsage.VERTEX | BufferUsage.COPY_DST\n    });\n    for (const i in properties) {\n      const property = properties[i];\n      const attributeInfo = getAttributeInfoFromFormat(property.format);\n      if (property.dynamic) {\n        geometry.addAttribute(property.attributeName, {\n          buffer: this._dynamicBuffer,\n          stride: this._dynamicStride * 4,\n          offset: dynamicOffset * 4,\n          format: property.format\n        });\n        dynamicOffset += attributeInfo.size;\n      } else {\n        geometry.addAttribute(property.attributeName, {\n          buffer: this._staticBuffer,\n          stride: this._staticStride * 4,\n          offset: staticOffset * 4,\n          format: property.format\n        });\n        staticOffset += attributeInfo.size;\n      }\n    }\n    geometry.addIndex(this.indexBuffer);\n    const uploadFunction = this.getParticleUpdate(properties);\n    this._dynamicUpload = uploadFunction.dynamicUpdate;\n    this._staticUpload = uploadFunction.staticUpdate;\n    this.geometry = geometry;\n  }\n  getParticleUpdate(properties) {\n    const key = getParticleSyncKey(properties);\n    if (this._generateParticleUpdateCache[key]) {\n      return this._generateParticleUpdateCache[key];\n    }\n    this._generateParticleUpdateCache[key] = this.generateParticleUpdate(properties);\n    return this._generateParticleUpdateCache[key];\n  }\n  generateParticleUpdate(properties) {\n    return generateParticleUpdateFunction(properties);\n  }\n  update(particles, uploadStatic) {\n    if (particles.length > this._size) {\n      uploadStatic = true;\n      this._size = Math.max(particles.length, this._size * 1.5 | 0);\n      this.staticAttributeBuffer = new ViewableBuffer(this._size * this._staticStride * 4 * 4);\n      this.dynamicAttributeBuffer = new ViewableBuffer(this._size * this._dynamicStride * 4 * 4);\n      this.indexBuffer = createIndicesForQuads(this._size);\n      this.geometry.indexBuffer.setDataWithSize(\n        this.indexBuffer,\n        this.indexBuffer.byteLength,\n        true\n      );\n    }\n    const dynamicAttributeBuffer = this.dynamicAttributeBuffer;\n    this._dynamicUpload(particles, dynamicAttributeBuffer.float32View, dynamicAttributeBuffer.uint32View);\n    this._dynamicBuffer.setDataWithSize(\n      this.dynamicAttributeBuffer.float32View,\n      particles.length * this._dynamicStride * 4,\n      true\n    );\n    if (uploadStatic) {\n      const staticAttributeBuffer = this.staticAttributeBuffer;\n      this._staticUpload(particles, staticAttributeBuffer.float32View, staticAttributeBuffer.uint32View);\n      this._staticBuffer.setDataWithSize(\n        staticAttributeBuffer.float32View,\n        particles.length * this._staticStride * 4,\n        true\n      );\n    }\n  }\n  destroy() {\n    this._staticBuffer.destroy();\n    this._dynamicBuffer.destroy();\n    this.geometry.destroy();\n  }\n}\nfunction getParticleSyncKey(properties) {\n  const keyGen = [];\n  for (const key in properties) {\n    const property = properties[key];\n    keyGen.push(key, property.code, property.dynamic ? \"d\" : \"s\");\n  }\n  return keyGen.join(\"_\");\n}\nvar fragment = \"varying vec2 vUV;\\nvarying vec4 vColor;\\n\\nuniform sampler2D uTexture;\\n\\nvoid main(void){\\n    vec4 color = texture2D(uTexture, vUV) * vColor;\\n    gl_FragColor = color;\\n}\";\nvar vertex = \"attribute vec2 aVertex;\\nattribute vec2 aUV;\\nattribute vec4 aColor;\\n\\nattribute vec2 aPosition;\\nattribute float aRotation;\\n\\nuniform mat3 uTranslationMatrix;\\nuniform float uRound;\\nuniform vec2 uResolution;\\nuniform vec4 uColor;\\n\\nvarying vec2 vUV;\\nvarying vec4 vColor;\\n\\nvec2 roundPixels(vec2 position, vec2 targetSize)\\n{       \\n    return (floor(((position * 0.5 + 0.5) * targetSize) + 0.5) / targetSize) * 2.0 - 1.0;\\n}\\n\\nvoid main(void){\\n    float cosRotation = cos(aRotation);\\n    float sinRotation = sin(aRotation);\\n    float x = aVertex.x * cosRotation - aVertex.y * sinRotation;\\n    float y = aVertex.x * sinRotation + aVertex.y * cosRotation;\\n\\n    vec2 v = vec2(x, y);\\n    v = v + aPosition;\\n\\n    gl_Position = vec4((uTranslationMatrix * vec3(v, 1.0)).xy, 0.0, 1.0);\\n\\n    if(uRound == 1.0)\\n    {\\n        gl_Position.xy = roundPixels(gl_Position.xy, uResolution);\\n    }\\n\\n    vUV = aUV;\\n    vColor = vec4(aColor.rgb * aColor.a, aColor.a) * uColor;\\n}\\n\";\nvar wgsl = \"\\nstruct ParticleUniforms {\\n  uProjectionMatrix:mat3x3<f32>,\\n  uColor:vec4<f32>,\\n  uResolution:vec2<f32>,\\n  uRoundPixels:f32,\\n};\\n\\n@group(0) @binding(0) var<uniform> uniforms: ParticleUniforms;\\n\\n@group(1) @binding(0) var uTexture: texture_2d<f32>;\\n@group(1) @binding(1) var uSampler : sampler;\\n\\nstruct VSOutput {\\n    @builtin(position) position: vec4<f32>,\\n    @location(0) uv : vec2<f32>,\\n    @location(1) color : vec4<f32>,\\n  };\\n@vertex\\nfn mainVertex(\\n  @location(0) aVertex: vec2<f32>,\\n  @location(1) aPosition: vec2<f32>,\\n  @location(2) aUV: vec2<f32>,\\n  @location(3) aColor: vec4<f32>,\\n  @location(4) aRotation: f32,\\n) -> VSOutput {\\n  \\n   let v = vec2(\\n       aVertex.x * cos(aRotation) - aVertex.y * sin(aRotation),\\n       aVertex.x * sin(aRotation) + aVertex.y * cos(aRotation)\\n   ) + aPosition;\\n\\n   let position = vec4((uniforms.uProjectionMatrix * vec3(v, 1.0)).xy, 0.0, 1.0);\\n\\n    let vColor = vec4(aColor.rgb * aColor.a, aColor.a) * uniforms.uColor;\\n\\n  return VSOutput(\\n   position,\\n   aUV,\\n   vColor,\\n  );\\n}\\n\\n@fragment\\nfn mainFragment(\\n  @location(0) uv: vec2<f32>,\\n  @location(1) color: vec4<f32>,\\n  @builtin(position) position: vec4<f32>,\\n) -> @location(0) vec4<f32> {\\n\\n    var sample = textureSample(uTexture, uSampler, uv) * color;\\n   \\n    return sample;\\n}\";\nclass ParticleShader extends Shader {\n  constructor() {\n    const glProgram2 = GlProgram.from({\n      vertex,\n      fragment\n    });\n    const gpuProgram2 = GpuProgram.from({\n      fragment: {\n        source: wgsl,\n        entryPoint: \"mainFragment\"\n      },\n      vertex: {\n        source: wgsl,\n        entryPoint: \"mainVertex\"\n      }\n    });\n    super({\n      glProgram: glProgram2,\n      gpuProgram: gpuProgram2,\n      resources: {\n        // this will be replaced with the texture from the particle container\n        uTexture: Texture.WHITE.source,\n        // this will be replaced with the texture style from the particle container\n        uSampler: new TextureStyle({}),\n        // this will be replaced with the local uniforms from the particle container\n        uniforms: {\n          uTranslationMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n          uColor: { value: new Color(16777215), type: \"vec4<f32>\" },\n          uRound: { value: 1, type: \"f32\" },\n          uResolution: { value: [0, 0], type: \"vec2<f32>\" }\n        }\n      }\n    });\n  }\n}\nclass ParticleContainerPipe {\n  /**\n   * @param renderer - The renderer this sprite batch works for.\n   * @param adaptor\n   */\n  constructor(renderer, adaptor) {\n    this.state = State.for2d();\n    this._gpuBufferHash = /* @__PURE__ */ Object.create(null);\n    this._destroyRenderableBound = this.destroyRenderable.bind(this);\n    this.localUniforms = new UniformGroup({\n      uTranslationMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      uColor: { value: new Float32Array(4), type: \"vec4<f32>\" },\n      uRound: { value: 1, type: \"f32\" },\n      uResolution: { value: [0, 0], type: \"vec2<f32>\" }\n    });\n    this.renderer = renderer;\n    this.adaptor = adaptor;\n    this.defaultShader = new ParticleShader();\n    this.state = State.for2d();\n  }\n  validateRenderable(_renderable) {\n    return false;\n  }\n  addRenderable(renderable, instructionSet) {\n    this.renderer.renderPipes.batch.break(instructionSet);\n    instructionSet.add(renderable);\n  }\n  getBuffers(renderable) {\n    return this._gpuBufferHash[renderable.uid] || this._initBuffer(renderable);\n  }\n  _initBuffer(renderable) {\n    this._gpuBufferHash[renderable.uid] = new ParticleBuffer({\n      size: renderable.particleChildren.length,\n      properties: renderable._properties\n    });\n    renderable.on(\"destroyed\", this._destroyRenderableBound);\n    return this._gpuBufferHash[renderable.uid];\n  }\n  updateRenderable(_renderable) {\n  }\n  destroyRenderable(renderable) {\n    const buffer = this._gpuBufferHash[renderable.uid];\n    buffer.destroy();\n    this._gpuBufferHash[renderable.uid] = null;\n    renderable.off(\"destroyed\", this._destroyRenderableBound);\n  }\n  execute(container) {\n    const children = container.particleChildren;\n    if (children.length === 0) {\n      return;\n    }\n    const renderer = this.renderer;\n    const buffer = this.getBuffers(container);\n    container.texture || (container.texture = children[0].texture);\n    const state = this.state;\n    buffer.update(children, container._childrenDirty);\n    container._childrenDirty = false;\n    state.blendMode = getAdjustedBlendModeBlend(container.blendMode, container.texture._source);\n    const uniforms = this.localUniforms.uniforms;\n    const transformationMatrix = uniforms.uTranslationMatrix;\n    container.worldTransform.copyTo(transformationMatrix);\n    transformationMatrix.prepend(renderer.globalUniforms.globalUniformData.projectionMatrix);\n    uniforms.uResolution = renderer.globalUniforms.globalUniformData.resolution;\n    uniforms.uRound = renderer._roundPixels | container._roundPixels;\n    color32BitToUniform(\n      container.groupColorAlpha,\n      uniforms.uColor,\n      0\n    );\n    this.adaptor.execute(this, container);\n  }\n  /** Destroys the ParticleRenderer. */\n  destroy() {\n    if (this.defaultShader) {\n      this.defaultShader.destroy();\n      this.defaultShader = null;\n    }\n  }\n}\nclass GlParticleContainerPipe extends ParticleContainerPipe {\n  constructor(renderer) {\n    super(renderer, new GlParticleContainerAdaptor());\n  }\n}\nGlParticleContainerPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes\n  ],\n  name: \"particle\"\n};\nclass GpuParticleContainerPipe extends ParticleContainerPipe {\n  constructor(renderer) {\n    super(renderer, new GpuParticleContainerAdaptor());\n  }\n}\nGpuParticleContainerPipe.extension = {\n  type: [\n    ExtensionType.WebGPUPipes\n  ],\n  name: \"particle\"\n};\nconst _NineSliceGeometry = class _NineSliceGeometry2 extends PlaneGeometry {\n  constructor(options = {}) {\n    options = { ..._NineSliceGeometry2.defaultOptions, ...options };\n    super({\n      width: options.width,\n      height: options.height,\n      verticesX: 4,\n      verticesY: 4\n    });\n    this.update(options);\n  }\n  /**\n   * Updates the NineSliceGeometry with the options.\n   * @param options - The options of the NineSliceGeometry.\n   */\n  update(options) {\n    this.width = options.width ?? this.width;\n    this.height = options.height ?? this.height;\n    this._originalWidth = options.originalWidth ?? this._originalWidth;\n    this._originalHeight = options.originalHeight ?? this._originalHeight;\n    this._leftWidth = options.leftWidth ?? this._leftWidth;\n    this._rightWidth = options.rightWidth ?? this._rightWidth;\n    this._topHeight = options.topHeight ?? this._topHeight;\n    this._bottomHeight = options.bottomHeight ?? this._bottomHeight;\n    this.updateUvs();\n    this.updatePositions();\n  }\n  /** Updates the positions of the vertices. */\n  updatePositions() {\n    const positions = this.positions;\n    const w = this._leftWidth + this._rightWidth;\n    const scaleW = this.width > w ? 1 : this.width / w;\n    const h = this._topHeight + this._bottomHeight;\n    const scaleH = this.height > h ? 1 : this.height / h;\n    const scale = Math.min(scaleW, scaleH);\n    positions[9] = positions[11] = positions[13] = positions[15] = this._topHeight * scale;\n    positions[17] = positions[19] = positions[21] = positions[23] = this.height - this._bottomHeight * scale;\n    positions[25] = positions[27] = positions[29] = positions[31] = this.height;\n    positions[2] = positions[10] = positions[18] = positions[26] = this._leftWidth * scale;\n    positions[4] = positions[12] = positions[20] = positions[28] = this.width - this._rightWidth * scale;\n    positions[6] = positions[14] = positions[22] = positions[30] = this.width;\n    this.getBuffer(\"aPosition\").update();\n  }\n  /** Updates the UVs of the vertices. */\n  updateUvs() {\n    const uvs = this.uvs;\n    uvs[0] = uvs[8] = uvs[16] = uvs[24] = 0;\n    uvs[1] = uvs[3] = uvs[5] = uvs[7] = 0;\n    uvs[6] = uvs[14] = uvs[22] = uvs[30] = 1;\n    uvs[25] = uvs[27] = uvs[29] = uvs[31] = 1;\n    const _uvw = 1 / this._originalWidth;\n    const _uvh = 1 / this._originalHeight;\n    uvs[2] = uvs[10] = uvs[18] = uvs[26] = _uvw * this._leftWidth;\n    uvs[9] = uvs[11] = uvs[13] = uvs[15] = _uvh * this._topHeight;\n    uvs[4] = uvs[12] = uvs[20] = uvs[28] = 1 - _uvw * this._rightWidth;\n    uvs[17] = uvs[19] = uvs[21] = uvs[23] = 1 - _uvh * this._bottomHeight;\n    this.getBuffer(\"aUV\").update();\n  }\n};\n_NineSliceGeometry.defaultOptions = {\n  /** The width of the NineSlicePlane, setting this will actually modify the vertices and UV's of this plane. */\n  width: 100,\n  /** The height of the NineSlicePlane, setting this will actually modify the vertices and UV's of this plane. */\n  height: 100,\n  /** The width of the left column. */\n  leftWidth: 10,\n  /** The height of the top row. */\n  topHeight: 10,\n  /** The width of the right column. */\n  rightWidth: 10,\n  /** The height of the bottom row. */\n  bottomHeight: 10,\n  /** The original width of the texture */\n  originalWidth: 100,\n  /** The original height of the texture */\n  originalHeight: 100\n};\nlet NineSliceGeometry = _NineSliceGeometry;\nclass NineSliceSpritePipe {\n  constructor(renderer) {\n    this._gpuSpriteHash = /* @__PURE__ */ Object.create(null);\n    this._destroyRenderableBound = this.destroyRenderable.bind(this);\n    this._renderer = renderer;\n    this._renderer.renderableGC.addManagedHash(this, \"_gpuSpriteHash\");\n  }\n  addRenderable(sprite, instructionSet) {\n    const gpuSprite = this._getGpuSprite(sprite);\n    if (sprite.didViewUpdate)\n      this._updateBatchableSprite(sprite, gpuSprite);\n    this._renderer.renderPipes.batch.addToBatch(gpuSprite, instructionSet);\n  }\n  updateRenderable(sprite) {\n    const gpuSprite = this._gpuSpriteHash[sprite.uid];\n    if (sprite.didViewUpdate)\n      this._updateBatchableSprite(sprite, gpuSprite);\n    gpuSprite._batcher.updateElement(gpuSprite);\n  }\n  validateRenderable(sprite) {\n    const gpuSprite = this._getGpuSprite(sprite);\n    return !gpuSprite._batcher.checkAndUpdateTexture(\n      gpuSprite,\n      sprite._texture\n    );\n  }\n  destroyRenderable(sprite) {\n    const batchableMesh = this._gpuSpriteHash[sprite.uid];\n    BigPool.return(batchableMesh.geometry);\n    BigPool.return(batchableMesh);\n    this._gpuSpriteHash[sprite.uid] = null;\n    sprite.off(\"destroyed\", this._destroyRenderableBound);\n  }\n  _updateBatchableSprite(sprite, batchableSprite) {\n    batchableSprite.geometry.update(sprite);\n    batchableSprite.setTexture(sprite._texture);\n  }\n  _getGpuSprite(sprite) {\n    return this._gpuSpriteHash[sprite.uid] || this._initGPUSprite(sprite);\n  }\n  _initGPUSprite(sprite) {\n    const batchableMesh = BigPool.get(BatchableMesh);\n    batchableMesh.geometry = BigPool.get(NineSliceGeometry);\n    batchableMesh.renderable = sprite;\n    batchableMesh.transform = sprite.groupTransform;\n    batchableMesh.texture = sprite._texture;\n    batchableMesh.roundPixels = this._renderer._roundPixels | sprite._roundPixels;\n    this._gpuSpriteHash[sprite.uid] = batchableMesh;\n    if (!sprite.didViewUpdate) {\n      this._updateBatchableSprite(sprite, batchableMesh);\n    }\n    sprite.on(\"destroyed\", this._destroyRenderableBound);\n    return batchableMesh;\n  }\n  destroy() {\n    for (const i in this._gpuSpriteHash) {\n      const batchableMesh = this._gpuSpriteHash[i];\n      batchableMesh.geometry.destroy();\n    }\n    this._gpuSpriteHash = null;\n    this._renderer = null;\n  }\n}\nNineSliceSpritePipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"nineSliceSprite\"\n};\nconst tilingBit = {\n  name: \"tiling-bit\",\n  vertex: {\n    header: (\n      /* wgsl */\n      `\n            struct TilingUniforms {\n                uMapCoord:mat3x3<f32>,\n                uClampFrame:vec4<f32>,\n                uClampOffset:vec2<f32>,\n                uTextureTransform:mat3x3<f32>,\n                uSizeAnchor:vec4<f32>\n            };\n\n            @group(2) @binding(0) var<uniform> tilingUniforms: TilingUniforms;\n            @group(2) @binding(1) var uTexture: texture_2d<f32>;\n            @group(2) @binding(2) var uSampler: sampler;\n        `\n    ),\n    main: (\n      /* wgsl */\n      `\n            uv = (tilingUniforms.uTextureTransform * vec3(uv, 1.0)).xy;\n\n            position = (position - tilingUniforms.uSizeAnchor.zw) * tilingUniforms.uSizeAnchor.xy;\n        `\n    )\n  },\n  fragment: {\n    header: (\n      /* wgsl */\n      `\n            struct TilingUniforms {\n                uMapCoord:mat3x3<f32>,\n                uClampFrame:vec4<f32>,\n                uClampOffset:vec2<f32>,\n                uTextureTransform:mat3x3<f32>,\n                uSizeAnchor:vec4<f32>\n            };\n\n            @group(2) @binding(0) var<uniform> tilingUniforms: TilingUniforms;\n            @group(2) @binding(1) var uTexture: texture_2d<f32>;\n            @group(2) @binding(2) var uSampler: sampler;\n        `\n    ),\n    main: (\n      /* wgsl */\n      `\n\n            var coord = vUV + ceil(tilingUniforms.uClampOffset - vUV);\n            coord = (tilingUniforms.uMapCoord * vec3(coord, 1.0)).xy;\n            var unclamped = coord;\n            coord = clamp(coord, tilingUniforms.uClampFrame.xy, tilingUniforms.uClampFrame.zw);\n\n            var bias = 0.;\n\n            if(unclamped.x == coord.x && unclamped.y == coord.y)\n            {\n                bias = -32.;\n            } \n\n            outColor = textureSampleBias(uTexture, uSampler, coord, bias);\n        `\n    )\n  }\n};\nconst tilingBitGl = {\n  name: \"tiling-bit\",\n  vertex: {\n    header: (\n      /* glsl */\n      `\n            uniform mat3 uTextureTransform;\n            uniform vec4 uSizeAnchor;\n        \n        `\n    ),\n    main: (\n      /* glsl */\n      `\n            uv = (uTextureTransform * vec3(aUV, 1.0)).xy;\n\n            position = (position - uSizeAnchor.zw) * uSizeAnchor.xy;\n        `\n    )\n  },\n  fragment: {\n    header: (\n      /* glsl */\n      `\n            uniform sampler2D uTexture;\n            uniform mat3 uMapCoord;\n            uniform vec4 uClampFrame;\n            uniform vec2 uClampOffset;\n        `\n    ),\n    main: (\n      /* glsl */\n      `\n\n        vec2 coord = vUV + ceil(uClampOffset - vUV);\n        coord = (uMapCoord * vec3(coord, 1.0)).xy;\n        vec2 unclamped = coord;\n        coord = clamp(coord, uClampFrame.xy, uClampFrame.zw);\n        \n        outColor = texture(uTexture, coord, unclamped == coord ? 0.0 : -32.0);// lod-bias very negative to force lod 0\n    \n        `\n    )\n  }\n};\nlet gpuProgram$1;\nlet glProgram$1;\nclass TilingSpriteShader extends Shader {\n  constructor() {\n    gpuProgram$1 ?? (gpuProgram$1 = compileHighShaderGpuProgram({\n      name: \"tiling-sprite-shader\",\n      bits: [\n        localUniformBit,\n        tilingBit,\n        roundPixelsBit\n      ]\n    }));\n    glProgram$1 ?? (glProgram$1 = compileHighShaderGlProgram({\n      name: \"tiling-sprite-shader\",\n      bits: [\n        localUniformBitGl,\n        tilingBitGl,\n        roundPixelsBitGl\n      ]\n    }));\n    const tilingUniforms = new UniformGroup({\n      uMapCoord: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      uClampFrame: { value: new Float32Array([0, 0, 1, 1]), type: \"vec4<f32>\" },\n      uClampOffset: { value: new Float32Array([0, 0]), type: \"vec2<f32>\" },\n      uTextureTransform: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      uSizeAnchor: { value: new Float32Array([100, 100, 0.5, 0.5]), type: \"vec4<f32>\" }\n    });\n    super({\n      glProgram: glProgram$1,\n      gpuProgram: gpuProgram$1,\n      resources: {\n        localUniforms: new UniformGroup({\n          uTransformMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n          uColor: { value: new Float32Array([1, 1, 1, 1]), type: \"vec4<f32>\" },\n          uRound: { value: 0, type: \"f32\" }\n        }),\n        tilingUniforms,\n        uTexture: Texture.EMPTY.source,\n        uSampler: Texture.EMPTY.source.style\n      }\n    });\n  }\n  updateUniforms(width, height, matrix, anchorX, anchorY, texture) {\n    const tilingUniforms = this.resources.tilingUniforms;\n    const textureWidth = texture.width;\n    const textureHeight = texture.height;\n    const textureMatrix = texture.textureMatrix;\n    const uTextureTransform = tilingUniforms.uniforms.uTextureTransform;\n    uTextureTransform.set(\n      matrix.a * textureWidth / width,\n      matrix.b * textureWidth / height,\n      matrix.c * textureHeight / width,\n      matrix.d * textureHeight / height,\n      matrix.tx / width,\n      matrix.ty / height\n    );\n    uTextureTransform.invert();\n    tilingUniforms.uniforms.uMapCoord = textureMatrix.mapCoord;\n    tilingUniforms.uniforms.uClampFrame = textureMatrix.uClampFrame;\n    tilingUniforms.uniforms.uClampOffset = textureMatrix.uClampOffset;\n    tilingUniforms.uniforms.uTextureTransform = uTextureTransform;\n    tilingUniforms.uniforms.uSizeAnchor[0] = width;\n    tilingUniforms.uniforms.uSizeAnchor[1] = height;\n    tilingUniforms.uniforms.uSizeAnchor[2] = anchorX;\n    tilingUniforms.uniforms.uSizeAnchor[3] = anchorY;\n    if (texture) {\n      this.resources.uTexture = texture.source;\n      this.resources.uSampler = texture.source.style;\n    }\n  }\n}\nclass QuadGeometry extends MeshGeometry {\n  constructor() {\n    super({\n      positions: new Float32Array([0, 0, 1, 0, 1, 1, 0, 1]),\n      uvs: new Float32Array([0, 0, 1, 0, 1, 1, 0, 1]),\n      indices: new Uint32Array([0, 1, 2, 0, 2, 3])\n    });\n  }\n}\nfunction setPositions(tilingSprite, positions) {\n  const anchorX = tilingSprite.anchor.x;\n  const anchorY = tilingSprite.anchor.y;\n  positions[0] = -anchorX * tilingSprite.width;\n  positions[1] = -anchorY * tilingSprite.height;\n  positions[2] = (1 - anchorX) * tilingSprite.width;\n  positions[3] = -anchorY * tilingSprite.height;\n  positions[4] = (1 - anchorX) * tilingSprite.width;\n  positions[5] = (1 - anchorY) * tilingSprite.height;\n  positions[6] = -anchorX * tilingSprite.width;\n  positions[7] = (1 - anchorY) * tilingSprite.height;\n}\nfunction applyMatrix(array, stride, offset, matrix) {\n  let index = 0;\n  const size = array.length / stride;\n  const a = matrix.a;\n  const b = matrix.b;\n  const c = matrix.c;\n  const d = matrix.d;\n  const tx = matrix.tx;\n  const ty = matrix.ty;\n  offset *= stride;\n  while (index < size) {\n    const x = array[offset];\n    const y = array[offset + 1];\n    array[offset] = a * x + c * y + tx;\n    array[offset + 1] = b * x + d * y + ty;\n    offset += stride;\n    index++;\n  }\n}\nfunction setUvs(tilingSprite, uvs) {\n  const texture = tilingSprite.texture;\n  const width = texture.frame.width;\n  const height = texture.frame.height;\n  let anchorX = 0;\n  let anchorY = 0;\n  if (tilingSprite.applyAnchorToTexture) {\n    anchorX = tilingSprite.anchor.x;\n    anchorY = tilingSprite.anchor.y;\n  }\n  uvs[0] = uvs[6] = -anchorX;\n  uvs[2] = uvs[4] = 1 - anchorX;\n  uvs[1] = uvs[3] = -anchorY;\n  uvs[5] = uvs[7] = 1 - anchorY;\n  const textureMatrix = Matrix.shared;\n  textureMatrix.copyFrom(tilingSprite._tileTransform.matrix);\n  textureMatrix.tx /= tilingSprite.width;\n  textureMatrix.ty /= tilingSprite.height;\n  textureMatrix.invert();\n  textureMatrix.scale(tilingSprite.width / width, tilingSprite.height / height);\n  applyMatrix(uvs, 2, 0, textureMatrix);\n}\nconst sharedQuad = new QuadGeometry();\nclass TilingSpritePipe {\n  constructor(renderer) {\n    this._state = State.default2d;\n    this._tilingSpriteDataHash = /* @__PURE__ */ Object.create(null);\n    this._destroyRenderableBound = this.destroyRenderable.bind(this);\n    this._renderer = renderer;\n    this._renderer.renderableGC.addManagedHash(this, \"_tilingSpriteDataHash\");\n  }\n  validateRenderable(renderable) {\n    const tilingSpriteData = this._getTilingSpriteData(renderable);\n    const couldBatch = tilingSpriteData.canBatch;\n    this._updateCanBatch(renderable);\n    const canBatch = tilingSpriteData.canBatch;\n    if (canBatch && canBatch === couldBatch) {\n      const { batchableMesh } = tilingSpriteData;\n      return !batchableMesh._batcher.checkAndUpdateTexture(\n        batchableMesh,\n        renderable.texture\n      );\n    }\n    return couldBatch !== canBatch;\n  }\n  addRenderable(tilingSprite, instructionSet) {\n    const batcher = this._renderer.renderPipes.batch;\n    this._updateCanBatch(tilingSprite);\n    const tilingSpriteData = this._getTilingSpriteData(tilingSprite);\n    const { geometry, canBatch } = tilingSpriteData;\n    if (canBatch) {\n      tilingSpriteData.batchableMesh || (tilingSpriteData.batchableMesh = new BatchableMesh());\n      const batchableMesh = tilingSpriteData.batchableMesh;\n      if (tilingSprite.didViewUpdate) {\n        this._updateBatchableMesh(tilingSprite);\n        batchableMesh.geometry = geometry;\n        batchableMesh.renderable = tilingSprite;\n        batchableMesh.transform = tilingSprite.groupTransform;\n        batchableMesh.setTexture(tilingSprite._texture);\n      }\n      batchableMesh.roundPixels = this._renderer._roundPixels | tilingSprite._roundPixels;\n      batcher.addToBatch(batchableMesh, instructionSet);\n    } else {\n      batcher.break(instructionSet);\n      tilingSpriteData.shader || (tilingSpriteData.shader = new TilingSpriteShader());\n      this.updateRenderable(tilingSprite);\n      instructionSet.add(tilingSprite);\n    }\n  }\n  execute(tilingSprite) {\n    const { shader } = this._tilingSpriteDataHash[tilingSprite.uid];\n    shader.groups[0] = this._renderer.globalUniforms.bindGroup;\n    const localUniforms = shader.resources.localUniforms.uniforms;\n    localUniforms.uTransformMatrix = tilingSprite.groupTransform;\n    localUniforms.uRound = this._renderer._roundPixels | tilingSprite._roundPixels;\n    color32BitToUniform(\n      tilingSprite.groupColorAlpha,\n      localUniforms.uColor,\n      0\n    );\n    this._state.blendMode = getAdjustedBlendModeBlend(tilingSprite.groupBlendMode, tilingSprite.texture._source);\n    this._renderer.encoder.draw({\n      geometry: sharedQuad,\n      shader,\n      state: this._state\n    });\n  }\n  updateRenderable(tilingSprite) {\n    const tilingSpriteData = this._getTilingSpriteData(tilingSprite);\n    const { canBatch } = tilingSpriteData;\n    if (canBatch) {\n      const { batchableMesh } = tilingSpriteData;\n      if (tilingSprite.didViewUpdate)\n        this._updateBatchableMesh(tilingSprite);\n      batchableMesh._batcher.updateElement(batchableMesh);\n    } else if (tilingSprite.didViewUpdate) {\n      const { shader } = tilingSpriteData;\n      shader.updateUniforms(\n        tilingSprite.width,\n        tilingSprite.height,\n        tilingSprite._tileTransform.matrix,\n        tilingSprite.anchor.x,\n        tilingSprite.anchor.y,\n        tilingSprite.texture\n      );\n    }\n  }\n  destroyRenderable(tilingSprite) {\n    const tilingSpriteData = this._getTilingSpriteData(tilingSprite);\n    tilingSpriteData.batchableMesh = null;\n    tilingSpriteData.shader?.destroy();\n    this._tilingSpriteDataHash[tilingSprite.uid] = null;\n    tilingSprite.off(\"destroyed\", this._destroyRenderableBound);\n  }\n  _getTilingSpriteData(renderable) {\n    return this._tilingSpriteDataHash[renderable.uid] || this._initTilingSpriteData(renderable);\n  }\n  _initTilingSpriteData(tilingSprite) {\n    const geometry = new MeshGeometry({\n      indices: sharedQuad.indices,\n      positions: sharedQuad.positions.slice(),\n      uvs: sharedQuad.uvs.slice()\n    });\n    this._tilingSpriteDataHash[tilingSprite.uid] = {\n      canBatch: true,\n      renderable: tilingSprite,\n      geometry\n    };\n    tilingSprite.on(\"destroyed\", this._destroyRenderableBound);\n    return this._tilingSpriteDataHash[tilingSprite.uid];\n  }\n  _updateBatchableMesh(tilingSprite) {\n    const renderableData = this._getTilingSpriteData(tilingSprite);\n    const { geometry } = renderableData;\n    const style = tilingSprite.texture.source.style;\n    if (style.addressMode !== \"repeat\") {\n      style.addressMode = \"repeat\";\n      style.update();\n    }\n    setUvs(tilingSprite, geometry.uvs);\n    setPositions(tilingSprite, geometry.positions);\n  }\n  destroy() {\n    for (const i in this._tilingSpriteDataHash) {\n      this.destroyRenderable(this._tilingSpriteDataHash[i].renderable);\n    }\n    this._tilingSpriteDataHash = null;\n    this._renderer = null;\n  }\n  _updateCanBatch(tilingSprite) {\n    const renderableData = this._getTilingSpriteData(tilingSprite);\n    const texture = tilingSprite.texture;\n    let _nonPowOf2wrapping = true;\n    if (this._renderer.type === RendererType.WEBGL) {\n      _nonPowOf2wrapping = this._renderer.context.supports.nonPowOf2wrapping;\n    }\n    renderableData.canBatch = texture.textureMatrix.isSimple && (_nonPowOf2wrapping || texture.source.isPowerOfTwo);\n    return renderableData.canBatch;\n  }\n}\nTilingSpritePipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"tilingSprite\"\n};\nconst localUniformMSDFBit = {\n  name: \"local-uniform-msdf-bit\",\n  vertex: {\n    header: (\n      /* wgsl */\n      `\n            struct LocalUniforms {\n                uColor:vec4<f32>,\n                uTransformMatrix:mat3x3<f32>,\n                uDistance: f32,\n                uRound:f32,\n            }\n\n            @group(2) @binding(0) var<uniform> localUniforms : LocalUniforms;\n        `\n    ),\n    main: (\n      /* wgsl */\n      `\n            vColor *= localUniforms.uColor;\n            modelMatrix *= localUniforms.uTransformMatrix;\n        `\n    ),\n    end: (\n      /* wgsl */\n      `\n            if(localUniforms.uRound == 1)\n            {\n                vPosition = vec4(roundPixels(vPosition.xy, globalUniforms.uResolution), vPosition.zw);\n            }\n        `\n    )\n  },\n  fragment: {\n    header: (\n      /* wgsl */\n      `\n            struct LocalUniforms {\n                uColor:vec4<f32>,\n                uTransformMatrix:mat3x3<f32>,\n                uDistance: f32\n            }\n\n            @group(2) @binding(0) var<uniform> localUniforms : LocalUniforms;\n         `\n    ),\n    main: (\n      /* wgsl */\n      ` \n            outColor = vec4<f32>(calculateMSDFAlpha(outColor, localUniforms.uColor, localUniforms.uDistance));\n        `\n    )\n  }\n};\nconst localUniformMSDFBitGl = {\n  name: \"local-uniform-msdf-bit\",\n  vertex: {\n    header: (\n      /* glsl */\n      `\n            uniform mat3 uTransformMatrix;\n            uniform vec4 uColor;\n            uniform float uRound;\n        `\n    ),\n    main: (\n      /* glsl */\n      `\n            vColor *= uColor;\n            modelMatrix *= uTransformMatrix;\n        `\n    ),\n    end: (\n      /* glsl */\n      `\n            if(uRound == 1.)\n            {\n                gl_Position.xy = roundPixels(gl_Position.xy, uResolution);\n            }\n        `\n    )\n  },\n  fragment: {\n    header: (\n      /* glsl */\n      `\n            uniform float uDistance;\n         `\n    ),\n    main: (\n      /* glsl */\n      ` \n            outColor = vec4(calculateMSDFAlpha(outColor, vColor, uDistance));\n        `\n    )\n  }\n};\nconst mSDFBit = {\n  name: \"msdf-bit\",\n  fragment: {\n    header: (\n      /* wgsl */\n      `\n            fn calculateMSDFAlpha(msdfColor:vec4<f32>, shapeColor:vec4<f32>, distance:f32) -> f32 {\n                \n                // MSDF\n                var median = msdfColor.r + msdfColor.g + msdfColor.b -\n                    min(msdfColor.r, min(msdfColor.g, msdfColor.b)) -\n                    max(msdfColor.r, max(msdfColor.g, msdfColor.b));\n            \n                // SDF\n                median = min(median, msdfColor.a);\n\n                var screenPxDistance = distance * (median - 0.5);\n                var alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);\n                if (median < 0.01) {\n                    alpha = 0.0;\n                } else if (median > 0.99) {\n                    alpha = 1.0;\n                }\n\n                // Gamma correction for coverage-like alpha\n                var luma: f32 = dot(shapeColor.rgb, vec3<f32>(0.299, 0.587, 0.114));\n                var gamma: f32 = mix(1.0, 1.0 / 2.2, luma);\n                var coverage: f32 = pow(shapeColor.a * alpha, gamma);\n\n                return coverage;\n             \n            }\n        `\n    )\n  }\n};\nconst mSDFBitGl = {\n  name: \"msdf-bit\",\n  fragment: {\n    header: (\n      /* glsl */\n      `\n            float calculateMSDFAlpha(vec4 msdfColor, vec4 shapeColor, float distance) {\n                \n                // MSDF\n                float median = msdfColor.r + msdfColor.g + msdfColor.b -\n                                min(msdfColor.r, min(msdfColor.g, msdfColor.b)) -\n                                max(msdfColor.r, max(msdfColor.g, msdfColor.b));\n               \n                // SDF\n                median = min(median, msdfColor.a);\n            \n                float screenPxDistance = distance * (median - 0.5);\n                float alpha = clamp(screenPxDistance + 0.5, 0.0, 1.0);\n           \n                if (median < 0.01) {\n                    alpha = 0.0;\n                } else if (median > 0.99) {\n                    alpha = 1.0;\n                }\n\n                // Gamma correction for coverage-like alpha\n                float luma = dot(shapeColor.rgb, vec3(0.299, 0.587, 0.114));\n                float gamma = mix(1.0, 1.0 / 2.2, luma);\n                float coverage = pow(shapeColor.a * alpha, gamma);  \n              \n                return coverage;\n            }\n        `\n    )\n  }\n};\nlet gpuProgram;\nlet glProgram;\nclass SdfShader extends Shader {\n  constructor() {\n    const uniforms = new UniformGroup({\n      uColor: { value: new Float32Array([1, 1, 1, 1]), type: \"vec4<f32>\" },\n      uTransformMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      uDistance: { value: 4, type: \"f32\" },\n      uRound: { value: 0, type: \"f32\" }\n    });\n    const maxTextures = getMaxTexturesPerBatch();\n    gpuProgram ?? (gpuProgram = compileHighShaderGpuProgram({\n      name: \"sdf-shader\",\n      bits: [\n        colorBit,\n        generateTextureBatchBit(maxTextures),\n        localUniformMSDFBit,\n        mSDFBit,\n        roundPixelsBit\n      ]\n    }));\n    glProgram ?? (glProgram = compileHighShaderGlProgram({\n      name: \"sdf-shader\",\n      bits: [\n        colorBitGl,\n        generateTextureBatchBitGl(maxTextures),\n        localUniformMSDFBitGl,\n        mSDFBitGl,\n        roundPixelsBitGl\n      ]\n    }));\n    super({\n      glProgram,\n      gpuProgram,\n      resources: {\n        localUniforms: uniforms,\n        batchSamplers: getBatchSamplersUniformGroup(maxTextures)\n      }\n    });\n  }\n}\nclass BitmapTextPipe {\n  constructor(renderer) {\n    this._gpuBitmapText = {};\n    this._destroyRenderableBound = this.destroyRenderable.bind(this);\n    this._renderer = renderer;\n    this._renderer.renderableGC.addManagedHash(this, \"_gpuBitmapText\");\n  }\n  validateRenderable(bitmapText) {\n    const graphicsRenderable = this._getGpuBitmapText(bitmapText);\n    if (bitmapText._didTextUpdate) {\n      bitmapText._didTextUpdate = false;\n      this._updateContext(bitmapText, graphicsRenderable);\n    }\n    return this._renderer.renderPipes.graphics.validateRenderable(graphicsRenderable);\n  }\n  addRenderable(bitmapText, instructionSet) {\n    const graphicsRenderable = this._getGpuBitmapText(bitmapText);\n    syncWithProxy(bitmapText, graphicsRenderable);\n    if (bitmapText._didTextUpdate) {\n      bitmapText._didTextUpdate = false;\n      this._updateContext(bitmapText, graphicsRenderable);\n    }\n    this._renderer.renderPipes.graphics.addRenderable(graphicsRenderable, instructionSet);\n    if (graphicsRenderable.context.customShader) {\n      this._updateDistanceField(bitmapText);\n    }\n  }\n  destroyRenderable(bitmapText) {\n    bitmapText.off(\"destroyed\", this._destroyRenderableBound);\n    this._destroyRenderableByUid(bitmapText.uid);\n  }\n  _destroyRenderableByUid(renderableUid) {\n    const context = this._gpuBitmapText[renderableUid].context;\n    if (context.customShader) {\n      BigPool.return(context.customShader);\n      context.customShader = null;\n    }\n    BigPool.return(this._gpuBitmapText[renderableUid]);\n    this._gpuBitmapText[renderableUid] = null;\n  }\n  updateRenderable(bitmapText) {\n    const graphicsRenderable = this._getGpuBitmapText(bitmapText);\n    syncWithProxy(bitmapText, graphicsRenderable);\n    this._renderer.renderPipes.graphics.updateRenderable(graphicsRenderable);\n    if (graphicsRenderable.context.customShader) {\n      this._updateDistanceField(bitmapText);\n    }\n  }\n  _updateContext(bitmapText, proxyGraphics) {\n    const { context } = proxyGraphics;\n    const bitmapFont = BitmapFontManager.getFont(bitmapText.text, bitmapText._style);\n    context.clear();\n    if (bitmapFont.distanceField.type !== \"none\") {\n      if (!context.customShader) {\n        context.customShader = BigPool.get(SdfShader);\n      }\n    }\n    const chars = Array.from(bitmapText.text);\n    const style = bitmapText._style;\n    let currentY = bitmapFont.baseLineOffset;\n    const bitmapTextLayout = getBitmapTextLayout(chars, style, bitmapFont, true);\n    let index = 0;\n    const padding = style.padding;\n    const scale = bitmapTextLayout.scale;\n    let tx = bitmapTextLayout.width;\n    let ty = bitmapTextLayout.height + bitmapTextLayout.offsetY;\n    if (style._stroke) {\n      tx += style._stroke.width / scale;\n      ty += style._stroke.width / scale;\n    }\n    context.translate(-bitmapText._anchor._x * tx - padding, -bitmapText._anchor._y * ty - padding).scale(scale, scale);\n    const tint = bitmapFont.applyFillAsTint ? style._fill.color : 16777215;\n    for (let i = 0; i < bitmapTextLayout.lines.length; i++) {\n      const line = bitmapTextLayout.lines[i];\n      for (let j = 0; j < line.charPositions.length; j++) {\n        const char = chars[index++];\n        const charData = bitmapFont.chars[char];\n        if (charData?.texture) {\n          context.texture(\n            charData.texture,\n            tint ? tint : \"black\",\n            Math.round(line.charPositions[j] + charData.xOffset),\n            Math.round(currentY + charData.yOffset)\n          );\n        }\n      }\n      currentY += bitmapFont.lineHeight;\n    }\n  }\n  _getGpuBitmapText(bitmapText) {\n    return this._gpuBitmapText[bitmapText.uid] || this.initGpuText(bitmapText);\n  }\n  initGpuText(bitmapText) {\n    const proxyRenderable = BigPool.get(Graphics);\n    this._gpuBitmapText[bitmapText.uid] = proxyRenderable;\n    this._updateContext(bitmapText, proxyRenderable);\n    bitmapText.on(\"destroyed\", this._destroyRenderableBound);\n    return this._gpuBitmapText[bitmapText.uid];\n  }\n  _updateDistanceField(bitmapText) {\n    const context = this._getGpuBitmapText(bitmapText).context;\n    const fontFamily = bitmapText._style.fontFamily;\n    const dynamicFont = Cache.get(`${fontFamily}-bitmap`);\n    const { a, b, c, d } = bitmapText.groupTransform;\n    const dx = Math.sqrt(a * a + b * b);\n    const dy = Math.sqrt(c * c + d * d);\n    const worldScale = (Math.abs(dx) + Math.abs(dy)) / 2;\n    const fontScale = dynamicFont.baseRenderedFontSize / bitmapText._style.fontSize;\n    const distance = worldScale * dynamicFont.distanceField.range * (1 / fontScale);\n    context.customShader.resources.localUniforms.uniforms.uDistance = distance;\n  }\n  destroy() {\n    for (const uid in this._gpuBitmapText) {\n      this._destroyRenderableByUid(uid);\n    }\n    this._gpuBitmapText = null;\n    this._renderer = null;\n  }\n}\nBitmapTextPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"bitmapText\"\n};\nfunction syncWithProxy(container, proxy) {\n  proxy.groupTransform = container.groupTransform;\n  proxy.groupColorAlpha = container.groupColorAlpha;\n  proxy.groupColor = container.groupColor;\n  proxy.groupBlendMode = container.groupBlendMode;\n  proxy.globalDisplayStatus = container.globalDisplayStatus;\n  proxy.groupTransform = container.groupTransform;\n  proxy.localDisplayStatus = container.localDisplayStatus;\n  proxy.groupAlpha = container.groupAlpha;\n  proxy._roundPixels = container._roundPixels;\n}\nfunction updateTextBounds(batchableSprite, text) {\n  const { texture, bounds } = batchableSprite;\n  updateQuadBounds(bounds, text._anchor, texture);\n  const padding = text._style.padding;\n  bounds.minX -= padding;\n  bounds.minY -= padding;\n  bounds.maxX -= padding;\n  bounds.maxY -= padding;\n}\nclass HTMLTextPipe {\n  constructor(renderer) {\n    this._gpuText = /* @__PURE__ */ Object.create(null);\n    this._destroyRenderableBound = this.destroyRenderable.bind(this);\n    this._renderer = renderer;\n    this._renderer.runners.resolutionChange.add(this);\n    this._renderer.renderableGC.addManagedHash(this, \"_gpuText\");\n  }\n  resolutionChange() {\n    for (const i in this._gpuText) {\n      const gpuText = this._gpuText[i];\n      if (!gpuText)\n        continue;\n      const text = gpuText.batchableSprite.renderable;\n      if (text._autoResolution) {\n        text._resolution = this._renderer.resolution;\n        text.onViewUpdate();\n      }\n    }\n  }\n  validateRenderable(htmlText) {\n    const gpuText = this._getGpuText(htmlText);\n    const newKey = htmlText._getKey();\n    if (gpuText.textureNeedsUploading) {\n      gpuText.textureNeedsUploading = false;\n      return true;\n    }\n    if (gpuText.currentKey !== newKey) {\n      return true;\n    }\n    return false;\n  }\n  addRenderable(htmlText, instructionSet) {\n    const gpuText = this._getGpuText(htmlText);\n    const batchableSprite = gpuText.batchableSprite;\n    if (htmlText._didTextUpdate) {\n      this._updateText(htmlText);\n    }\n    this._renderer.renderPipes.batch.addToBatch(batchableSprite, instructionSet);\n  }\n  updateRenderable(htmlText) {\n    const gpuText = this._getGpuText(htmlText);\n    const batchableSprite = gpuText.batchableSprite;\n    if (htmlText._didTextUpdate) {\n      this._updateText(htmlText);\n    }\n    batchableSprite._batcher.updateElement(batchableSprite);\n  }\n  destroyRenderable(htmlText) {\n    htmlText.off(\"destroyed\", this._destroyRenderableBound);\n    this._destroyRenderableById(htmlText.uid);\n  }\n  _destroyRenderableById(htmlTextUid) {\n    const gpuText = this._gpuText[htmlTextUid];\n    this._renderer.htmlText.decreaseReferenceCount(gpuText.currentKey);\n    BigPool.return(gpuText.batchableSprite);\n    this._gpuText[htmlTextUid] = null;\n  }\n  _updateText(htmlText) {\n    const newKey = htmlText._getKey();\n    const gpuText = this._getGpuText(htmlText);\n    const batchableSprite = gpuText.batchableSprite;\n    if (gpuText.currentKey !== newKey) {\n      this._updateGpuText(htmlText).catch((e) => {\n        console.error(e);\n      });\n    }\n    htmlText._didTextUpdate = false;\n    updateTextBounds(batchableSprite, htmlText);\n  }\n  async _updateGpuText(htmlText) {\n    htmlText._didTextUpdate = false;\n    const gpuText = this._getGpuText(htmlText);\n    if (gpuText.generatingTexture)\n      return;\n    const newKey = htmlText._getKey();\n    this._renderer.htmlText.decreaseReferenceCount(gpuText.currentKey);\n    gpuText.generatingTexture = true;\n    gpuText.currentKey = newKey;\n    const resolution = htmlText.resolution ?? this._renderer.resolution;\n    const texture = await this._renderer.htmlText.getManagedTexture(\n      htmlText.text,\n      resolution,\n      htmlText._style,\n      htmlText._getKey()\n    );\n    const batchableSprite = gpuText.batchableSprite;\n    batchableSprite.texture = gpuText.texture = texture;\n    gpuText.generatingTexture = false;\n    gpuText.textureNeedsUploading = true;\n    htmlText.onViewUpdate();\n    updateTextBounds(batchableSprite, htmlText);\n  }\n  _getGpuText(htmlText) {\n    return this._gpuText[htmlText.uid] || this.initGpuText(htmlText);\n  }\n  initGpuText(htmlText) {\n    const gpuTextData = {\n      texture: Texture.EMPTY,\n      currentKey: \"--\",\n      batchableSprite: BigPool.get(BatchableSprite),\n      textureNeedsUploading: false,\n      generatingTexture: false\n    };\n    const batchableSprite = gpuTextData.batchableSprite;\n    batchableSprite.renderable = htmlText;\n    batchableSprite.transform = htmlText.groupTransform;\n    batchableSprite.texture = Texture.EMPTY;\n    batchableSprite.bounds = { minX: 0, maxX: 1, minY: 0, maxY: 0 };\n    batchableSprite.roundPixels = this._renderer._roundPixels | htmlText._roundPixels;\n    htmlText._resolution = htmlText._autoResolution ? this._renderer.resolution : htmlText.resolution;\n    this._gpuText[htmlText.uid] = gpuTextData;\n    htmlText.on(\"destroyed\", this._destroyRenderableBound);\n    return gpuTextData;\n  }\n  destroy() {\n    for (const i in this._gpuText) {\n      this._destroyRenderableById(i);\n    }\n    this._gpuText = null;\n    this._renderer = null;\n  }\n}\nHTMLTextPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"htmlText\"\n};\nfunction isSafari() {\n  const { userAgent } = DOMAdapter.get().getNavigator();\n  return /^((?!chrome|android).)*safari/i.test(userAgent);\n}\nconst tempBounds = new Bounds();\nfunction getPo2TextureFromSource(image, width, height, resolution) {\n  const bounds = tempBounds;\n  bounds.minX = 0;\n  bounds.minY = 0;\n  bounds.maxX = image.width / resolution | 0;\n  bounds.maxY = image.height / resolution | 0;\n  const texture = TexturePool.getOptimalTexture(\n    bounds.width,\n    bounds.height,\n    resolution,\n    false\n  );\n  texture.source.uploadMethodId = \"image\";\n  texture.source.resource = image;\n  texture.source.alphaMode = \"premultiply-alpha-on-upload\";\n  texture.frame.width = width / resolution;\n  texture.frame.height = height / resolution;\n  texture.source.emit(\"update\", texture.source);\n  texture.updateUvs();\n  return texture;\n}\nfunction extractFontFamilies(text, style) {\n  const fontFamily = style.fontFamily;\n  const fontFamilies = [];\n  const dedupe = {};\n  const regex = /font-family:([^;\"\\s]+)/g;\n  const matches = text.match(regex);\n  function addFontFamily(fontFamily2) {\n    if (!dedupe[fontFamily2]) {\n      fontFamilies.push(fontFamily2);\n      dedupe[fontFamily2] = true;\n    }\n  }\n  if (Array.isArray(fontFamily)) {\n    for (let i = 0; i < fontFamily.length; i++) {\n      addFontFamily(fontFamily[i]);\n    }\n  } else {\n    addFontFamily(fontFamily);\n  }\n  if (matches) {\n    matches.forEach((match) => {\n      const fontFamily2 = match.split(\":\")[1].trim();\n      addFontFamily(fontFamily2);\n    });\n  }\n  for (const i in style.tagStyles) {\n    const fontFamily2 = style.tagStyles[i].fontFamily;\n    addFontFamily(fontFamily2);\n  }\n  return fontFamilies;\n}\nasync function loadFontAsBase64(url) {\n  const response = await DOMAdapter.get().fetch(url);\n  const blob = await response.blob();\n  const reader = new FileReader();\n  const dataSrc = await new Promise((resolve, reject) => {\n    reader.onloadend = () => resolve(reader.result);\n    reader.onerror = reject;\n    reader.readAsDataURL(blob);\n  });\n  return dataSrc;\n}\nasync function loadFontCSS(style, url) {\n  const dataSrc = await loadFontAsBase64(url);\n  return `@font-face {\n        font-family: \"${style.fontFamily}\";\n        src: url('${dataSrc}');\n        font-weight: ${style.fontWeight};\n        font-style: ${style.fontStyle};\n    }`;\n}\nconst FontStylePromiseCache = /* @__PURE__ */ new Map();\nasync function getFontCss(fontFamilies, style, defaultOptions) {\n  const fontPromises = fontFamilies.filter((fontFamily) => Cache.has(`${fontFamily}-and-url`)).map((fontFamily, i) => {\n    if (!FontStylePromiseCache.has(fontFamily)) {\n      const { url } = Cache.get(`${fontFamily}-and-url`);\n      if (i === 0) {\n        FontStylePromiseCache.set(fontFamily, loadFontCSS({\n          fontWeight: style.fontWeight,\n          fontStyle: style.fontStyle,\n          fontFamily\n        }, url));\n      } else {\n        FontStylePromiseCache.set(fontFamily, loadFontCSS({\n          fontWeight: defaultOptions.fontWeight,\n          fontStyle: defaultOptions.fontStyle,\n          fontFamily\n        }, url));\n      }\n    }\n    return FontStylePromiseCache.get(fontFamily);\n  });\n  return (await Promise.all(fontPromises)).join(\"\\n\");\n}\nfunction getSVGUrl(text, style, resolution, fontCSS, htmlTextData) {\n  const { domElement, styleElement, svgRoot } = htmlTextData;\n  domElement.innerHTML = `<style>${style.cssStyle}</style><div style='padding:0;'>${text}</div>`;\n  domElement.setAttribute(\"style\", `transform: scale(${resolution});transform-origin: top left; display: inline-block`);\n  styleElement.textContent = fontCSS;\n  const { width, height } = htmlTextData.image;\n  svgRoot.setAttribute(\"width\", width.toString());\n  svgRoot.setAttribute(\"height\", height.toString());\n  return new XMLSerializer().serializeToString(svgRoot);\n}\nfunction getTemporaryCanvasFromImage(image, resolution) {\n  const canvasAndContext = CanvasPool.getOptimalCanvasAndContext(\n    image.width,\n    image.height,\n    resolution\n  );\n  const { context } = canvasAndContext;\n  context.clearRect(0, 0, image.width, image.height);\n  context.drawImage(image, 0, 0);\n  return canvasAndContext;\n}\nfunction loadSVGImage(image, url, delay) {\n  return new Promise(async (resolve) => {\n    if (delay) {\n      await new Promise((resolve2) => setTimeout(resolve2, 100));\n    }\n    image.onload = () => {\n      resolve();\n    };\n    image.src = `data:image/svg+xml;charset=utf8,${encodeURIComponent(url)}`;\n    image.crossOrigin = \"anonymous\";\n  });\n}\nclass HTMLTextSystem {\n  constructor(renderer) {\n    this._activeTextures = {};\n    this._renderer = renderer;\n    this._createCanvas = renderer.type === RendererType.WEBGPU;\n  }\n  getTexture(options) {\n    return this._buildTexturePromise(\n      options.text,\n      options.resolution,\n      options.style\n    );\n  }\n  getManagedTexture(text, resolution, style, textKey) {\n    if (this._activeTextures[textKey]) {\n      this._increaseReferenceCount(textKey);\n      return this._activeTextures[textKey].promise;\n    }\n    const promise = this._buildTexturePromise(text, resolution, style).then((texture) => {\n      this._activeTextures[textKey].texture = texture;\n      return texture;\n    });\n    this._activeTextures[textKey] = {\n      texture: null,\n      promise,\n      usageCount: 1\n    };\n    return promise;\n  }\n  async _buildTexturePromise(text, resolution, style) {\n    const htmlTextData = BigPool.get(HTMLTextRenderData);\n    const fontFamilies = extractFontFamilies(text, style);\n    const fontCSS = await getFontCss(\n      fontFamilies,\n      style,\n      HTMLTextStyle.defaultTextStyle\n    );\n    const measured = measureHtmlText(text, style, fontCSS, htmlTextData);\n    const width = Math.ceil(Math.ceil(Math.max(1, measured.width) + style.padding * 2) * resolution);\n    const height = Math.ceil(Math.ceil(Math.max(1, measured.height) + style.padding * 2) * resolution);\n    const image = htmlTextData.image;\n    const uvSafeOffset = 2;\n    image.width = (width | 0) + uvSafeOffset;\n    image.height = (height | 0) + uvSafeOffset;\n    const svgURL = getSVGUrl(text, style, resolution, fontCSS, htmlTextData);\n    await loadSVGImage(image, svgURL, isSafari() && fontFamilies.length > 0);\n    const resource = image;\n    let canvasAndContext;\n    if (this._createCanvas) {\n      canvasAndContext = getTemporaryCanvasFromImage(image, resolution);\n    }\n    const texture = getPo2TextureFromSource(\n      canvasAndContext ? canvasAndContext.canvas : resource,\n      image.width - uvSafeOffset,\n      image.height - uvSafeOffset,\n      resolution\n    );\n    if (this._createCanvas) {\n      this._renderer.texture.initSource(texture.source);\n      CanvasPool.returnCanvasAndContext(canvasAndContext);\n    }\n    BigPool.return(htmlTextData);\n    return texture;\n  }\n  _increaseReferenceCount(textKey) {\n    this._activeTextures[textKey].usageCount++;\n  }\n  decreaseReferenceCount(textKey) {\n    const activeTexture = this._activeTextures[textKey];\n    if (!activeTexture)\n      return;\n    activeTexture.usageCount--;\n    if (activeTexture.usageCount === 0) {\n      if (activeTexture.texture) {\n        this._cleanUp(activeTexture);\n      } else {\n        activeTexture.promise.then((texture) => {\n          activeTexture.texture = texture;\n          this._cleanUp(activeTexture);\n        }).catch(() => {\n          warn(\"HTMLTextSystem: Failed to clean texture\");\n        });\n      }\n      this._activeTextures[textKey] = null;\n    }\n  }\n  _cleanUp(activeTexture) {\n    TexturePool.returnTexture(activeTexture.texture);\n    activeTexture.texture.source.resource = null;\n    activeTexture.texture.source.uploadMethodId = \"unknown\";\n  }\n  getReferenceCount(textKey) {\n    return this._activeTextures[textKey].usageCount;\n  }\n  destroy() {\n    this._activeTextures = null;\n  }\n}\nHTMLTextSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem,\n    ExtensionType.CanvasSystem\n  ],\n  name: \"htmlText\"\n};\nHTMLTextSystem.defaultFontOptions = {\n  fontFamily: \"Arial\",\n  fontStyle: \"normal\",\n  fontWeight: \"normal\"\n};\nclass CanvasTextPipe {\n  constructor(renderer) {\n    this._gpuText = /* @__PURE__ */ Object.create(null);\n    this._destroyRenderableBound = this.destroyRenderable.bind(this);\n    this._renderer = renderer;\n    this._renderer.runners.resolutionChange.add(this);\n    this._renderer.renderableGC.addManagedHash(this, \"_gpuText\");\n  }\n  resolutionChange() {\n    for (const i in this._gpuText) {\n      const gpuText = this._gpuText[i];\n      if (!gpuText)\n        continue;\n      const text = gpuText.batchableSprite.renderable;\n      if (text._autoResolution) {\n        text._resolution = this._renderer.resolution;\n        text.onViewUpdate();\n      }\n    }\n  }\n  validateRenderable(text) {\n    const gpuText = this._getGpuText(text);\n    const newKey = text._getKey();\n    if (gpuText.currentKey !== newKey) {\n      return true;\n    }\n    return false;\n  }\n  addRenderable(text, instructionSet) {\n    const gpuText = this._getGpuText(text);\n    const batchableSprite = gpuText.batchableSprite;\n    if (text._didTextUpdate) {\n      this._updateText(text);\n    }\n    this._renderer.renderPipes.batch.addToBatch(batchableSprite, instructionSet);\n  }\n  updateRenderable(text) {\n    const gpuText = this._getGpuText(text);\n    const batchableSprite = gpuText.batchableSprite;\n    if (text._didTextUpdate) {\n      this._updateText(text);\n    }\n    batchableSprite._batcher.updateElement(batchableSprite);\n  }\n  destroyRenderable(text) {\n    text.off(\"destroyed\", this._destroyRenderableBound);\n    this._destroyRenderableById(text.uid);\n  }\n  _destroyRenderableById(textUid) {\n    const gpuText = this._gpuText[textUid];\n    this._renderer.canvasText.decreaseReferenceCount(gpuText.currentKey);\n    BigPool.return(gpuText.batchableSprite);\n    this._gpuText[textUid] = null;\n  }\n  _updateText(text) {\n    const newKey = text._getKey();\n    const gpuText = this._getGpuText(text);\n    const batchableSprite = gpuText.batchableSprite;\n    if (gpuText.currentKey !== newKey) {\n      this._updateGpuText(text);\n    }\n    text._didTextUpdate = false;\n    updateTextBounds(batchableSprite, text);\n  }\n  _updateGpuText(text) {\n    const gpuText = this._getGpuText(text);\n    const batchableSprite = gpuText.batchableSprite;\n    if (gpuText.texture) {\n      this._renderer.canvasText.decreaseReferenceCount(gpuText.currentKey);\n    }\n    gpuText.texture = batchableSprite.texture = this._renderer.canvasText.getManagedTexture(text);\n    gpuText.currentKey = text._getKey();\n    batchableSprite.texture = gpuText.texture;\n  }\n  _getGpuText(text) {\n    return this._gpuText[text.uid] || this.initGpuText(text);\n  }\n  initGpuText(text) {\n    const gpuTextData = {\n      texture: null,\n      currentKey: \"--\",\n      batchableSprite: BigPool.get(BatchableSprite)\n    };\n    gpuTextData.batchableSprite.renderable = text;\n    gpuTextData.batchableSprite.transform = text.groupTransform;\n    gpuTextData.batchableSprite.bounds = { minX: 0, maxX: 1, minY: 0, maxY: 0 };\n    gpuTextData.batchableSprite.roundPixels = this._renderer._roundPixels | text._roundPixels;\n    this._gpuText[text.uid] = gpuTextData;\n    text._resolution = text._autoResolution ? this._renderer.resolution : text.resolution;\n    this._updateText(text);\n    text.on(\"destroyed\", this._destroyRenderableBound);\n    return gpuTextData;\n  }\n  destroy() {\n    for (const i in this._gpuText) {\n      this._destroyRenderableById(i);\n    }\n    this._gpuText = null;\n    this._renderer = null;\n  }\n}\nCanvasTextPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"text\"\n};\nfunction checkRow(data, width, y) {\n  for (let x = 0, index = 4 * y * width; x < width; ++x, index += 4) {\n    if (data[index + 3] !== 0)\n      return false;\n  }\n  return true;\n}\nfunction checkColumn(data, width, x, top, bottom) {\n  const stride = 4 * width;\n  for (let y = top, index = top * stride + 4 * x; y <= bottom; ++y, index += stride) {\n    if (data[index + 3] !== 0)\n      return false;\n  }\n  return true;\n}\nfunction getCanvasBoundingBox(canvas, resolution = 1) {\n  const { width, height } = canvas;\n  const context = canvas.getContext(\"2d\", {\n    willReadFrequently: true\n  });\n  if (context === null) {\n    throw new TypeError(\"Failed to get canvas 2D context\");\n  }\n  const imageData = context.getImageData(0, 0, width, height);\n  const data = imageData.data;\n  let left = 0;\n  let top = 0;\n  let right = width - 1;\n  let bottom = height - 1;\n  while (top < height && checkRow(data, width, top))\n    ++top;\n  if (top === height)\n    return Rectangle.EMPTY;\n  while (checkRow(data, width, bottom))\n    --bottom;\n  while (checkColumn(data, width, left, top, bottom))\n    ++left;\n  while (checkColumn(data, width, right, top, bottom))\n    --right;\n  ++right;\n  ++bottom;\n  return new Rectangle(left / resolution, top / resolution, (right - left) / resolution, (bottom - top) / resolution);\n}\nclass CanvasTextSystem {\n  constructor(_renderer) {\n    this._activeTextures = {};\n    this._renderer = _renderer;\n  }\n  getTextureSize(text, resolution, style) {\n    const measured = CanvasTextMetrics.measureText(text || \" \", style);\n    let width = Math.ceil(Math.ceil(Math.max(1, measured.width) + style.padding * 2) * resolution);\n    let height = Math.ceil(Math.ceil(Math.max(1, measured.height) + style.padding * 2) * resolution);\n    width = Math.ceil(width - 1e-6);\n    height = Math.ceil(height - 1e-6);\n    width = nextPow2(width);\n    height = nextPow2(height);\n    return { width, height };\n  }\n  getTexture(options, resolution, style, _textKey) {\n    if (typeof options === \"string\") {\n      deprecation(\"8.0.0\", \"CanvasTextSystem.getTexture: Use object TextOptions instead of separate arguments\");\n      options = {\n        text: options,\n        style,\n        resolution\n      };\n    }\n    if (!(options.style instanceof TextStyle)) {\n      options.style = new TextStyle(options.style);\n    }\n    const { texture, canvasAndContext } = this.createTextureAndCanvas(\n      options\n    );\n    this._renderer.texture.initSource(texture._source);\n    CanvasPool.returnCanvasAndContext(canvasAndContext);\n    return texture;\n  }\n  createTextureAndCanvas(options) {\n    const { text, style } = options;\n    const resolution = options.resolution ?? this._renderer.resolution;\n    const measured = CanvasTextMetrics.measureText(text || \" \", style);\n    const width = Math.ceil(Math.ceil(Math.max(1, measured.width) + style.padding * 2) * resolution);\n    const height = Math.ceil(Math.ceil(Math.max(1, measured.height) + style.padding * 2) * resolution);\n    const canvasAndContext = CanvasPool.getOptimalCanvasAndContext(width, height);\n    const { canvas } = canvasAndContext;\n    this.renderTextToCanvas(text, style, resolution, canvasAndContext);\n    const texture = getPo2TextureFromSource(canvas, width, height, resolution);\n    if (style.trim) {\n      const trimmed = getCanvasBoundingBox(canvas, resolution);\n      texture.frame.copyFrom(trimmed);\n      texture.updateUvs();\n    }\n    return { texture, canvasAndContext };\n  }\n  getManagedTexture(text) {\n    text._resolution = text._autoResolution ? this._renderer.resolution : text.resolution;\n    const textKey = text._getKey();\n    if (this._activeTextures[textKey]) {\n      this._increaseReferenceCount(textKey);\n      return this._activeTextures[textKey].texture;\n    }\n    const { texture, canvasAndContext } = this.createTextureAndCanvas(text);\n    this._activeTextures[textKey] = {\n      canvasAndContext,\n      texture,\n      usageCount: 1\n    };\n    return texture;\n  }\n  _increaseReferenceCount(textKey) {\n    this._activeTextures[textKey].usageCount++;\n  }\n  decreaseReferenceCount(textKey) {\n    const activeTexture = this._activeTextures[textKey];\n    activeTexture.usageCount--;\n    if (activeTexture.usageCount === 0) {\n      CanvasPool.returnCanvasAndContext(activeTexture.canvasAndContext);\n      TexturePool.returnTexture(activeTexture.texture);\n      const source = activeTexture.texture.source;\n      source.resource = null;\n      source.uploadMethodId = \"unknown\";\n      source.alphaMode = \"no-premultiply-alpha\";\n      this._activeTextures[textKey] = null;\n    }\n  }\n  getReferenceCount(textKey) {\n    return this._activeTextures[textKey].usageCount;\n  }\n  /**\n   * Renders text to its canvas, and updates its texture.\n   *\n   * By default this is used internally to ensure the texture is correct before rendering,\n   * but it can be used called externally, for example from this class to 'pre-generate' the texture from a piece of text,\n   * and then shared across multiple Sprites.\n   * @param text\n   * @param style\n   * @param resolution\n   * @param canvasAndContext\n   */\n  renderTextToCanvas(text, style, resolution, canvasAndContext) {\n    const { canvas, context } = canvasAndContext;\n    const font = fontStringFromTextStyle(style);\n    const measured = CanvasTextMetrics.measureText(text || \" \", style);\n    const lines = measured.lines;\n    const lineHeight = measured.lineHeight;\n    const lineWidths = measured.lineWidths;\n    const maxLineWidth = measured.maxLineWidth;\n    const fontProperties = measured.fontProperties;\n    const height = canvas.height;\n    context.resetTransform();\n    context.scale(resolution, resolution);\n    context.textBaseline = style.textBaseline;\n    if (style._stroke?.width) {\n      const strokeStyle = style._stroke;\n      context.lineWidth = strokeStyle.width;\n      context.miterLimit = strokeStyle.miterLimit;\n      context.lineJoin = strokeStyle.join;\n      context.lineCap = strokeStyle.cap;\n    }\n    context.font = font;\n    let linePositionX;\n    let linePositionY;\n    const passesCount = style.dropShadow ? 2 : 1;\n    for (let i = 0; i < passesCount; ++i) {\n      const isShadowPass = style.dropShadow && i === 0;\n      const dsOffsetText = isShadowPass ? Math.ceil(Math.max(1, height) + style.padding * 2) : 0;\n      const dsOffsetShadow = dsOffsetText * resolution;\n      if (isShadowPass) {\n        context.fillStyle = \"black\";\n        context.strokeStyle = \"black\";\n        const shadowOptions = style.dropShadow;\n        const dropShadowColor = shadowOptions.color;\n        const dropShadowAlpha = shadowOptions.alpha;\n        context.shadowColor = Color.shared.setValue(dropShadowColor).setAlpha(dropShadowAlpha).toRgbaString();\n        const dropShadowBlur = shadowOptions.blur * resolution;\n        const dropShadowDistance = shadowOptions.distance * resolution;\n        context.shadowBlur = dropShadowBlur;\n        context.shadowOffsetX = Math.cos(shadowOptions.angle) * dropShadowDistance;\n        context.shadowOffsetY = Math.sin(shadowOptions.angle) * dropShadowDistance + dsOffsetShadow;\n      } else {\n        context.fillStyle = style._fill ? getCanvasFillStyle(style._fill, context) : null;\n        if (style._stroke?.width) {\n          context.strokeStyle = getCanvasFillStyle(style._stroke, context);\n        }\n        context.shadowColor = \"black\";\n      }\n      let linePositionYShift = (lineHeight - fontProperties.fontSize) / 2;\n      if (lineHeight - fontProperties.fontSize < 0) {\n        linePositionYShift = 0;\n      }\n      const strokeWidth = style._stroke?.width ?? 0;\n      for (let i2 = 0; i2 < lines.length; i2++) {\n        linePositionX = strokeWidth / 2;\n        linePositionY = strokeWidth / 2 + i2 * lineHeight + fontProperties.ascent + linePositionYShift;\n        if (style.align === \"right\") {\n          linePositionX += maxLineWidth - lineWidths[i2];\n        } else if (style.align === \"center\") {\n          linePositionX += (maxLineWidth - lineWidths[i2]) / 2;\n        }\n        if (style._stroke?.width) {\n          this._drawLetterSpacing(\n            lines[i2],\n            style,\n            canvasAndContext,\n            linePositionX + style.padding,\n            linePositionY + style.padding - dsOffsetText,\n            true\n          );\n        }\n        if (style._fill !== void 0) {\n          this._drawLetterSpacing(\n            lines[i2],\n            style,\n            canvasAndContext,\n            linePositionX + style.padding,\n            linePositionY + style.padding - dsOffsetText\n          );\n        }\n      }\n    }\n  }\n  /**\n   * Render the text with letter-spacing.\n   * @param text - The text to draw\n   * @param style\n   * @param canvasAndContext\n   * @param x - Horizontal position to draw the text\n   * @param y - Vertical position to draw the text\n   * @param isStroke - Is this drawing for the outside stroke of the\n   *  text? If not, it's for the inside fill\n   */\n  _drawLetterSpacing(text, style, canvasAndContext, x, y, isStroke = false) {\n    const { context } = canvasAndContext;\n    const letterSpacing = style.letterSpacing;\n    let useExperimentalLetterSpacing = false;\n    if (CanvasTextMetrics.experimentalLetterSpacingSupported) {\n      if (CanvasTextMetrics.experimentalLetterSpacing) {\n        context.letterSpacing = `${letterSpacing}px`;\n        context.textLetterSpacing = `${letterSpacing}px`;\n        useExperimentalLetterSpacing = true;\n      } else {\n        context.letterSpacing = \"0px\";\n        context.textLetterSpacing = \"0px\";\n      }\n    }\n    if (letterSpacing === 0 || useExperimentalLetterSpacing) {\n      if (isStroke) {\n        context.strokeText(text, x, y);\n      } else {\n        context.fillText(text, x, y);\n      }\n      return;\n    }\n    let currentPosition = x;\n    const stringArray = CanvasTextMetrics.graphemeSegmenter(text);\n    let previousWidth = context.measureText(text).width;\n    let currentWidth = 0;\n    for (let i = 0; i < stringArray.length; ++i) {\n      const currentChar = stringArray[i];\n      if (isStroke) {\n        context.strokeText(currentChar, currentPosition, y);\n      } else {\n        context.fillText(currentChar, currentPosition, y);\n      }\n      let textStr = \"\";\n      for (let j = i + 1; j < stringArray.length; ++j) {\n        textStr += stringArray[j];\n      }\n      currentWidth = context.measureText(textStr).width;\n      currentPosition += previousWidth - currentWidth + letterSpacing;\n      previousWidth = currentWidth;\n    }\n  }\n  destroy() {\n    this._activeTextures = null;\n  }\n}\nCanvasTextSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem,\n    ExtensionType.CanvasSystem\n  ],\n  name: \"canvasText\"\n};\nextensions.add(ResizePlugin);\nextensions.add(TickerPlugin);\nextensions.add(GraphicsPipe);\nextensions.add(GraphicsContextSystem);\nextensions.add(MeshPipe);\nextensions.add(GlParticleContainerPipe);\nextensions.add(GpuParticleContainerPipe);\nextensions.add(CanvasTextSystem);\nextensions.add(CanvasTextPipe);\nextensions.add(BitmapTextPipe);\nextensions.add(HTMLTextSystem);\nextensions.add(HTMLTextPipe);\nextensions.add(TilingSpritePipe);\nextensions.add(NineSliceSpritePipe);\nextensions.add(FilterSystem);\nextensions.add(FilterPipe);\n"], "names": [], "mappings": ";;;AAEA,MAAM,YAAY,CAAC;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,IAAI,CAAC,OAAO,EAAE;AACvB,IAAI,MAAM,CAAC,cAAc;AACzB,MAAM,IAAI;AACV,MAAM,UAAU;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN,QAAQ,GAAG,CAAC,GAAG,EAAE;AACjB,UAAU,UAAU,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACrE,UAAU,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC;AAC/B,UAAU,IAAI,GAAG,EAAE;AACnB,YAAY,UAAU,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACpE,YAAY,IAAI,CAAC,MAAM,EAAE,CAAC;AAC1B,WAAW;AACX,SAAS;AACT,QAAQ,GAAG,GAAG;AACd,UAAU,OAAO,IAAI,CAAC,SAAS,CAAC;AAChC,SAAS;AACT,OAAO;AACP,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,WAAW,GAAG,MAAM;AAC7B,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AAC3B,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;AAC3B,MAAM,IAAI,CAAC,SAAS,GAAG,qBAAqB,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;AAClE,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,aAAa,GAAG,MAAM;AAC/B,MAAM,IAAI,IAAI,CAAC,SAAS,EAAE;AAC1B,QAAQ,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7C,QAAQ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC9B,OAAO;AACP,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM;AACxB,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AAC3B,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;AAC3B,MAAM,IAAI,KAAK,CAAC;AAChB,MAAM,IAAI,MAAM,CAAC;AACjB,MAAM,IAAI,IAAI,CAAC,SAAS,KAAK,UAAU,CAAC,MAAM,EAAE;AAChD,QAAQ,KAAK,GAAG,UAAU,CAAC,UAAU,CAAC;AACtC,QAAQ,MAAM,GAAG,UAAU,CAAC,WAAW,CAAC;AACxC,OAAO,MAAM;AACb,QAAQ,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC;AAC7D,QAAQ,KAAK,GAAG,WAAW,CAAC;AAC5B,QAAQ,MAAM,GAAG,YAAY,CAAC;AAC9B,OAAO;AACP,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAC1C,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;AACpB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC;AAC7C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,OAAO,GAAG;AACnB,IAAI,UAAU,CAAC,mBAAmB,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAC/D,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;AACzB,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC9B,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,GAAG;AACH,CAAC;AACD,YAAY,CAAC,SAAS,GAAG,aAAa,CAAC,WAAW,CAAC;AACnD,MAAM,YAAY,CAAC;AACnB;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,IAAI,CAAC,OAAO,EAAE;AACvB,IAAI,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AAC5B,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,YAAY,EAAE,KAAK;AACzB,KAAK,EAAE,OAAO,CAAC,CAAC;AAChB,IAAI,MAAM,CAAC,cAAc;AACzB,MAAM,IAAI;AACV,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,GAAG,CAAC,MAAM,EAAE;AACpB,UAAU,IAAI,IAAI,CAAC,OAAO,EAAE;AAC5B,YAAY,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACnD,WAAW;AACX,UAAU,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;AAChC,UAAU,IAAI,MAAM,EAAE;AACtB,YAAY,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,eAAe,CAAC,GAAG,CAAC,CAAC;AAC/D,WAAW;AACX,SAAS;AACT,QAAQ,GAAG,GAAG;AACd,UAAU,OAAO,IAAI,CAAC,OAAO,CAAC;AAC9B,SAAS;AACT,OAAO;AACP,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,IAAI,GAAG,MAAM;AACtB,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;AAC1B,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,KAAK,GAAG,MAAM;AACvB,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;AAC3B,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,IAAI,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;AACtE,IAAI,IAAI,OAAO,CAAC,SAAS,EAAE;AAC3B,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;AACnB,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,OAAO,GAAG;AACnB,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;AACtB,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC;AACrC,MAAM,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACzB,MAAM,SAAS,CAAC,OAAO,EAAE,CAAC;AAC1B,KAAK;AACL,GAAG;AACH,CAAC;AACD,YAAY,CAAC,SAAS,GAAG,aAAa,CAAC,WAAW,CAAC;AACnD,MAAM,UAAU,CAAC;AACjB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH,EAAE,IAAI,CAAC,YAAY,EAAE,SAAS,EAAE,cAAc,EAAE;AAChD,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;AACnD,IAAI,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC5C,IAAI,cAAc,CAAC,GAAG,CAAC;AACvB,MAAM,YAAY,EAAE,QAAQ;AAC5B,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,SAAS;AACf,MAAM,YAAY;AAClB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,GAAG,CAAC,aAAa,EAAE,UAAU,EAAE,cAAc,EAAE;AACjD,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC3D,IAAI,cAAc,CAAC,GAAG,CAAC;AACvB,MAAM,YAAY,EAAE,QAAQ;AAC5B,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,SAAS,EAAE,KAAK;AACtB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,CAAC,WAAW,EAAE;AACvB,IAAI,IAAI,WAAW,CAAC,MAAM,KAAK,YAAY,EAAE;AAC7C,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC9C,KAAK,MAAM,IAAI,WAAW,CAAC,MAAM,KAAK,WAAW,EAAE;AACnD,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;AAClC,KAAK;AACL,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,CAAC;AACD,UAAU,CAAC,SAAS,GAAG;AACvB,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,UAAU;AAC5B,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,QAAQ;AAChB,CAAC,CAAC;AACF,SAAS,yBAAyB,CAAC,WAAW,EAAE,MAAM,EAAE;AACxD,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC;AACjB,EAAE,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC;AACnC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC/C,IAAI,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AACtC,IAAI,IAAI,UAAU,CAAC,mBAAmB,GAAG,CAAC,EAAE;AAC5C,MAAM,SAAS;AACf,KAAK;AACL,IAAI,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC,cAAc,CAAC;AAC9C,IAAI,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AACxC,GAAG;AACH,EAAE,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;AAC7B,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC;AACD,MAAM,YAAY,GAAG,IAAI,QAAQ,CAAC;AAClC,EAAE,UAAU,EAAE;AACd,IAAI,SAAS,EAAE;AACf,MAAM,MAAM,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACxD,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,CAAC,GAAG,CAAC;AACnB,MAAM,MAAM,EAAE,CAAC;AACf,KAAK;AACL,GAAG;AACH,EAAE,WAAW,EAAE,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAClD,CAAC,CAAC,CAAC;AACH,MAAM,YAAY,CAAC;AACnB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;AAC/B,IAAI,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;AAC3B,IAAI,IAAI,CAAC,qBAAqB,GAAG,IAAI,YAAY,CAAC;AAClD,MAAM,UAAU,EAAE,EAAE,KAAK,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AACnE,MAAM,WAAW,EAAE,EAAE,KAAK,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AACpE,MAAM,WAAW,EAAE,EAAE,KAAK,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AACpE,MAAM,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AACrE,MAAM,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AACrE,MAAM,cAAc,EAAE,EAAE,KAAK,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AACvE,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,sBAAsB,GAAG,IAAI,SAAS,CAAC,EAAE,CAAC,CAAC;AACpD,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,iBAAiB,GAAG;AAC1B,IAAI,OAAO,IAAI,CAAC,iBAAiB,EAAE,WAAW,CAAC;AAC/C,GAAG;AACH,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACnC,IAAI,MAAM,OAAO,GAAG,WAAW,CAAC,YAAY,CAAC,OAAO,CAAC;AACrD,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAAE;AACpD,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,cAAc,EAAE,CAAC;AACxE,KAAK;AACL,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACjE,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC7B,IAAI,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9B,MAAM,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;AAC7B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;AACrC,IAAI,IAAI,WAAW,CAAC,WAAW,EAAE;AACjC,MAAM,yBAAyB,CAAC,WAAW,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;AACjE,KAAK,MAAM,IAAI,WAAW,CAAC,YAAY,CAAC,UAAU,EAAE;AACpD,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;AACrB,MAAM,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;AAC1D,MAAM,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;AAC/D,KAAK,MAAM;AACX,MAAM,WAAW,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAC9D,KAAK;AACL,IAAI,IAAI,WAAW,CAAC,SAAS,EAAE;AAC/B,MAAM,MAAM,WAAW,GAAG,WAAW,CAAC,SAAS,CAAC,WAAW,IAAI,WAAW,CAAC,SAAS,CAAC,iBAAiB,CAAC;AACvG,MAAM,MAAM,oBAAoB,GAAG,WAAW,CAAC,qBAAqB,CAAC;AACrE,MAAM,IAAI,oBAAoB,EAAE;AAChC,QAAQ,MAAM,CAAC,WAAW,CAAC,oBAAoB,CAAC,CAAC;AACjD,OAAO;AACP,KAAK;AACL,IAAI,MAAM,kBAAkB,GAAG,QAAQ,CAAC,YAAY,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC;AACtF,IAAI,IAAI,UAAU,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,OAAO,GAAG,CAAC,CAAC;AACpB,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,aAAa,GAAG,KAAK,CAAC;AAC9B,IAAI,IAAI,OAAO,GAAG,KAAK,CAAC;AACxB,IAAI,IAAI,cAAc,GAAG,IAAI,CAAC;AAC9B,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC7C,MAAM,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAChC,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,KAAK,SAAS,GAAG,kBAAkB,CAAC,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC;AAC9H,MAAM,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC;AAChC,MAAM,IAAI,MAAM,CAAC,SAAS,KAAK,KAAK,EAAE;AACtC,QAAQ,SAAS,GAAG,KAAK,CAAC;AAC1B,OAAO,MAAM,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,EAAE;AACjD,QAAQ,SAAS,KAAK,SAAS,GAAG,kBAAkB,CAAC,SAAS,CAAC,CAAC;AAChE,OAAO;AACP,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,EAAE;AAClC,QAAQ,cAAc,GAAG,KAAK,CAAC;AAC/B,OAAO;AACP,MAAM,MAAM,YAAY,GAAG,CAAC,EAAE,MAAM,CAAC,mBAAmB,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC1E,MAAM,IAAI,CAAC,YAAY,EAAE;AACzB,QAAQ,OAAO,GAAG,KAAK,CAAC;AACxB,QAAQ,MAAM;AACd,OAAO;AACP,MAAM,IAAI,MAAM,CAAC,aAAa,IAAI,EAAE,QAAQ,CAAC,UAAU,EAAE,aAAa,IAAI,IAAI,CAAC,EAAE;AACjF,QAAQ,IAAI,CAAC,sHAAsH,CAAC,CAAC;AACrI,QAAQ,OAAO,GAAG,KAAK,CAAC;AACxB,QAAQ,MAAM;AACd,OAAO;AACP,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,IAAI,OAAO,CAAC;AAC1C,MAAM,aAAa,KAAK,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC;AAC9D,KAAK;AACL,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;AAC7B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,cAAc,EAAE;AACxB,MAAM,MAAM,QAAQ,GAAG,QAAQ,CAAC,YAAY,CAAC,YAAY,CAAC;AAC1D,MAAM,MAAM,cAAc,GAAG,QAAQ,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU,CAAC;AAC3E,MAAM,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,GAAG,cAAc,EAAE,CAAC,EAAE,QAAQ,CAAC,MAAM,GAAG,cAAc,CAAC,CAAC;AAChG,KAAK;AACL,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;AAC3E,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;AAC5B,MAAM,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC;AAC7B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,UAAU,CAAC,IAAI,GAAG,KAAK,CAAC;AAC5B,IAAI,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC;AAC/B,IAAI,UAAU,CAAC,aAAa,GAAG,aAAa,CAAC;AAC7C,IAAI,UAAU,CAAC,SAAS,GAAG,WAAW,CAAC,SAAS,CAAC;AACjD,IAAI,UAAU,CAAC,YAAY,GAAG,WAAW,CAAC,YAAY,CAAC;AACvD,IAAI,UAAU,CAAC,qBAAqB,GAAG,QAAQ,CAAC,YAAY,CAAC,aAAa,CAAC;AAC3E,IAAI,UAAU,CAAC,YAAY,GAAG,WAAW,CAAC,iBAAiB;AAC3D,MAAM,MAAM,CAAC,KAAK;AAClB,MAAM,MAAM,CAAC,MAAM;AACnB,MAAM,UAAU;AAChB,MAAM,SAAS;AACf,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;AAC9D,IAAI,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC;AACjC,MAAM,MAAM,EAAE,MAAM;AACpB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,GAAG,GAAG;AACR,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACnC,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC7B,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACjE,IAAI,IAAI,UAAU,CAAC,IAAI,EAAE;AACzB,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,iBAAiB,GAAG,UAAU,CAAC;AACxC,IAAI,MAAM,YAAY,GAAG,UAAU,CAAC,YAAY,CAAC;AACjD,IAAI,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;AACrC,IAAI,IAAI,WAAW,GAAG,OAAO,CAAC,KAAK,CAAC;AACpC,IAAI,QAAQ,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;AAC7C,IAAI,IAAI,UAAU,CAAC,aAAa,EAAE;AAClC,MAAM,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC;AACtH,MAAM,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC,eAAe,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC;AACnG,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;AAC9E,KAAK;AACL,IAAI,UAAU,CAAC,WAAW,GAAG,WAAW,CAAC;AACzC,IAAI,MAAM,OAAO,GAAG,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC;AACpD,IAAI,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAC1E,IAAI,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AACnE,IAAI,QAAQ,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC;AAClC,IAAI,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AAC9B,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,YAAY,EAAE,UAAU,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;AACpF,MAAM,WAAW,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;AAC9C,KAAK,MAAM;AACX,MAAM,IAAI,IAAI,GAAG,UAAU,CAAC,YAAY,CAAC;AACzC,MAAM,IAAI,IAAI,GAAG,WAAW,CAAC,iBAAiB;AAC9C,QAAQ,MAAM,CAAC,KAAK;AACpB,QAAQ,MAAM,CAAC,MAAM;AACrB,QAAQ,IAAI,CAAC,MAAM,CAAC,WAAW;AAC/B,QAAQ,KAAK;AACb,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,GAAG,CAAC,CAAC;AAChB,MAAM,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;AAC/C,QAAQ,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAClC,QAAQ,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC7C,QAAQ,MAAM,CAAC,GAAG,IAAI,CAAC;AACvB,QAAQ,IAAI,GAAG,IAAI,CAAC;AACpB,QAAQ,IAAI,GAAG,CAAC,CAAC;AACjB,OAAO;AACP,MAAM,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;AAC5E,MAAM,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AACtC,MAAM,WAAW,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AACtC,KAAK;AACL,IAAI,IAAI,UAAU,CAAC,aAAa,EAAE;AAClC,MAAM,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;AAC7C,KAAK;AACL,GAAG;AACH,EAAE,cAAc,CAAC,iBAAiB,EAAE,MAAM,EAAE,cAAc,EAAE;AAC5D,IAAI,MAAM,oBAAoB,GAAG,iBAAiB,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC;AACnF,IAAI,MAAM,WAAW,GAAG,WAAW,CAAC,iBAAiB;AACrD,MAAM,MAAM,CAAC,KAAK;AAClB,MAAM,MAAM,CAAC,MAAM;AACnB,MAAM,oBAAoB;AAC1B,MAAM,KAAK;AACX,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC;AACxB,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC;AACxB,IAAI,IAAI,cAAc,EAAE;AACxB,MAAM,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC;AAC/B,MAAM,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC;AAC/B,KAAK;AACL,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC;AAC7C,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,oBAAoB,CAAC,CAAC;AAC7C,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,oBAAoB,CAAC,CAAC;AACjE,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,oBAAoB,CAAC,CAAC;AACnE,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,aAAa;AAC5C,MAAM,iBAAiB;AACvB,MAAM,WAAW;AACjB,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE;AACd,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE;AACvB,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AACpB,KAAK,CAAC;AACN,IAAI,OAAO,WAAW,CAAC;AACvB,GAAG;AACH,EAAE,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;AAC5C,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACnC,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AACjE,IAAI,MAAM,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;AACrC,IAAI,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;AAChC,IAAI,MAAM,qBAAqB,GAAG,UAAU,CAAC,qBAAqB,CAAC;AACnE,IAAI,MAAM,aAAa,GAAG,qBAAqB,KAAK,MAAM,CAAC;AAC3D,IAAI,IAAI,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,gBAAgB,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC;AACjG,IAAI,IAAI,YAAY,GAAG,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;AAClD,IAAI,OAAO,YAAY,GAAG,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,IAAI,EAAE;AACrE,MAAM,EAAE,YAAY,CAAC;AACrB,KAAK;AACL,IAAI,IAAI,YAAY,GAAG,CAAC,EAAE;AAC1B,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC;AACnF,KAAK;AACL,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,qBAAqB,CAAC;AACtD,IAAI,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC;AAC7C,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,YAAY,CAAC;AAC9C,IAAI,MAAM,SAAS,GAAG,QAAQ,CAAC,UAAU,CAAC;AAC1C,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,WAAW,CAAC;AAC5C,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,WAAW,CAAC;AAC5C,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,YAAY,CAAC;AAC9C,IAAI,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC;AAClD,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,IAAI,SAAS,GAAG,IAAI,CAAC,iBAAiB,CAAC;AAC7C,MAAM,OAAO,SAAS,GAAG,CAAC,EAAE;AAC5B,QAAQ,SAAS,EAAE,CAAC;AACpB,QAAQ,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;AAC1E,QAAQ,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE;AAC/B,UAAU,MAAM,CAAC,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC;AAC7C,UAAU,MAAM,CAAC,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC;AAC7C,UAAU,MAAM;AAChB,SAAS;AACT,OAAO;AACP,MAAM,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC;AAC9C,MAAM,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC;AAC9C,KAAK,MAAM;AACX,MAAM,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACzB,MAAM,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACzB,KAAK;AACL,IAAI,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;AACvC,IAAI,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;AACxC,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;AACtC,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;AACvC,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACpC,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACpC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC;AAC5C,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC;AAC7C,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACtC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACtC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACxC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACxC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAC3E,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAC5E,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,gBAAgB,CAAC,YAAY,CAAC;AACjF,IAAI,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC;AAC3C,IAAI,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,GAAG,UAAU,CAAC;AAC3C,IAAI,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,KAAK,GAAG,UAAU,CAAC;AAC3D,IAAI,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC,MAAM,GAAG,UAAU,CAAC;AAC5D,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AAC5E,IAAI,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;AAChD,IAAI,IAAI,MAAM,YAAY,OAAO,EAAE;AACnC,MAAM,aAAa,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;AAC5C,MAAM,aAAa,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC;AAC7C,KAAK,MAAM;AACX,MAAM,aAAa,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC;AAC5C,MAAM,aAAa,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC;AAC7C,KAAK;AACL,IAAI,aAAa,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACpD,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;AAC5B,IAAI,IAAI,QAAQ,CAAC,WAAW,CAAC,YAAY,EAAE;AAC3C,MAAM,MAAM,aAAa,GAAG,QAAQ,CAAC,WAAW,CAAC,YAAY,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC7F,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;AAChE,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC;AACjE,KAAK;AACL,IAAI,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAC7D,IAAI,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACnE,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC;AACnD,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;AAC1B,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,KAAK,EAAE,MAAM,CAAC,MAAM;AAC1B,MAAM,QAAQ,EAAE,eAAe;AAC/B,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,QAAQ,CAAC,IAAI,KAAK,YAAY,CAAC,KAAK,EAAE;AAC9C,MAAM,QAAQ,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;AAC/C,KAAK;AACL,GAAG;AACH,EAAE,cAAc,GAAG;AACnB,IAAI,OAAO;AACX,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,YAAY,EAAE,IAAI;AACxB,MAAM,MAAM,EAAE,IAAI,MAAM,EAAE;AAC1B,MAAM,SAAS,EAAE,IAAI;AACrB,MAAM,YAAY,EAAE,IAAI;AACxB,MAAM,aAAa,EAAE,KAAK;AAC1B,MAAM,qBAAqB,EAAE,IAAI;AACjC,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,qBAAqB,CAAC,YAAY,EAAE,MAAM,EAAE;AAC9C,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC;AACxC,IAAI,MAAM,YAAY,GAAG,YAAY,CAAC,GAAG;AACzC,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK;AACrC,MAAM,CAAC;AACP,MAAM,CAAC;AACP,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM;AACtC,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI;AACtB,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI;AACtB,KAAK,CAAC;AACN,IAAI,MAAM,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACvE,IAAI,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,IAAI,MAAM,CAAC,iBAAiB,CAAC;AACvE,IAAI,IAAI,WAAW,IAAI,WAAW,CAAC,qBAAqB,EAAE;AAC1D,MAAM,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;AAChE,KAAK;AACL,IAAI,cAAc,CAAC,MAAM,EAAE,CAAC;AAC5B,IAAI,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;AACzC,IAAI,YAAY,CAAC,KAAK;AACtB,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK;AACpC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM;AACrC,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC7D,IAAI,OAAO,YAAY,CAAC;AACxB,GAAG;AACH,CAAC;AACD,YAAY,CAAC,SAAS,GAAG;AACzB,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,QAAQ;AAChB,CAAC,CAAC;AACF,MAAM,aAAa,GAAG,MAAM,cAAc,SAAS,QAAQ,CAAC;AAC5D,EAAE,WAAW,CAAC,GAAG,IAAI,EAAE;AACvB,IAAI,IAAI,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AAChC,IAAI,IAAI,OAAO,YAAY,YAAY,EAAE;AACzC,MAAM,WAAW,CAAC,MAAM,EAAE,2DAA2D,CAAC,CAAC;AACvF,MAAM,OAAO,GAAG;AAChB,QAAQ,SAAS,EAAE,OAAO;AAC1B,QAAQ,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AACpB,QAAQ,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;AACxB,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO,GAAG,EAAE,GAAG,cAAc,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;AAC/D,IAAI,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACtF,IAAI,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;AAC1B,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,IAAI,OAAO,CAAC,SAAS,EAAE;AAC7B,QAAQ,GAAG,GAAG,IAAI,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;AACjD,OAAO,MAAM;AACb,QAAQ,GAAG,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACzD,OAAO;AACP,KAAK;AACL,IAAI,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3E,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,kBAAkB,CAAC;AACnD,IAAI,MAAM,cAAc,GAAG,IAAI,MAAM,CAAC;AACtC,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,KAAK,EAAE,0BAA0B;AACvC,MAAM,WAAW;AACjB,MAAM,KAAK,EAAE,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,QAAQ;AACtD,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC;AAChC,MAAM,IAAI,EAAE,GAAG;AACf,MAAM,KAAK,EAAE,oBAAoB;AACjC,MAAM,WAAW;AACjB,MAAM,KAAK,EAAE,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,QAAQ;AACtD,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC;AACnC,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,KAAK,EAAE,mBAAmB;AAChC,MAAM,WAAW;AACjB,MAAM,KAAK,EAAE,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,QAAQ;AACrD,KAAK,CAAC,CAAC;AACP,IAAI,KAAK,CAAC;AACV,MAAM,UAAU,EAAE;AAClB,QAAQ,SAAS,EAAE;AACnB,UAAU,MAAM,EAAE,cAAc;AAChC,UAAU,MAAM,EAAE,WAAW;AAC7B,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,MAAM,EAAE,CAAC;AACnB,SAAS;AACT,QAAQ,GAAG,EAAE;AACb,UAAU,MAAM,EAAE,QAAQ;AAC1B,UAAU,MAAM,EAAE,WAAW;AAC7B,UAAU,MAAM,EAAE,CAAC,GAAG,CAAC;AACvB,UAAU,MAAM,EAAE,CAAC;AACnB,SAAS;AACT,OAAO;AACP,MAAM,WAAW;AACjB,MAAM,QAAQ,EAAE,OAAO,CAAC,QAAQ;AAChC,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;AAC5B,GAAG;AACH;AACA,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;AACjD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,SAAS,CAAC,KAAK,EAAE;AACvB,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC;AAClD,GAAG;AACH;AACA,EAAE,IAAI,GAAG,GAAG;AACZ,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC;AAC3C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,GAAG,CAAC,KAAK,EAAE;AACjB,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC;AAC5C,GAAG;AACH;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;AACjC,GAAG;AACH,EAAE,IAAI,OAAO,CAAC,KAAK,EAAE;AACrB,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,KAAK,CAAC;AAClC,GAAG;AACH,CAAC,CAAC;AACF,aAAa,CAAC,cAAc,GAAG;AAC/B,EAAE,QAAQ,EAAE,eAAe;AAC3B,EAAE,kBAAkB,EAAE,KAAK;AAC3B,CAAC,CAAC;AACF,IAAI,YAAY,GAAG,aAAa,CAAC;AACjC,SAAS,cAAc,CAAC,KAAK,EAAE;AAC/B,EAAE,MAAM,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC;AAC/B,EAAE,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC;AAC3B,EAAE,MAAM,cAAc,GAAG;AACzB,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;AACzD,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;AACpC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;AACtC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;AACtC,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC;AACpC,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;AACxC,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;AAC9C,IAAI,CAAC,YAAY,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AAChC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;AACjC,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,UAAU,KAAK,KAAK,IAAI,KAAK,CAAC,QAAQ,GAAG,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;AAClG,IAAI,GAAG,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,aAAa,EAAE,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE;AACrE,IAAI,GAAG,KAAK,CAAC,QAAQ,GAAG;AACxB,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,UAAU,GAAG,WAAW,GAAG,YAAY,CAAC,CAAC;AACnE,MAAM,CAAC,WAAW,EAAE,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC;AAC3C,KAAK,GAAG,EAAE;AACV,IAAI,GAAG,MAAM,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE;AAC1C,IAAI,GAAG,KAAK,CAAC,UAAU,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE;AAClE,IAAI,GAAG,KAAK,CAAC,YAAY;AACzB,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACd,EAAE,MAAM,SAAS,GAAG,CAAC,CAAC,MAAM,EAAE,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC;AAClD,EAAE,aAAa,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AAC5C,EAAE,OAAO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC7B,CAAC;AACD,SAAS,eAAe,CAAC,eAAe,EAAE;AAC1C,EAAE,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;AACtG,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;AACnF,EAAE,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;AACnF,EAAE,MAAM,QAAQ,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACnC,EAAE,IAAI,eAAe,CAAC,IAAI,GAAG,CAAC,EAAE;AAChC,IAAI,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,EAAE,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;AACzE,GAAG;AACH,EAAE,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AAC7C,CAAC;AACD,SAAS,WAAW,CAAC,MAAM,EAAE;AAC7B,EAAE,OAAO;AACT,IAAI,CAAC,2BAA2B,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;AAClD,IAAI,CAAC,2BAA2B,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;AAC/E,IAAI,CAAC,mBAAmB,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC;AAC1C,IAAI,CAAC,mBAAmB,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;AACvE,IAAI,qBAAqB;AACzB,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACd,CAAC;AACD,MAAM,SAAS,GAAG;AAClB,EAAE,QAAQ,EAAE,CAAC,sBAAsB,CAAC;AACpC,EAAE,UAAU,EAAE,CAAC,sBAAsB,CAAC;AACtC,EAAE,UAAU,EAAE,CAAC,sBAAsB,CAAC;AACtC,EAAE,SAAS,EAAE,CAAC,qBAAqB,CAAC;AACpC,EAAE,WAAW,EAAE,CAAC,uBAAuB,CAAC;AACxC,EAAE,aAAa,EAAE,CAAC,2BAA2B,CAAC;AAC9C,EAAE,KAAK,EAAE,CAAC,qBAAqB,CAAC;AAChC,EAAE,OAAO,EAAE,CAAC,oBAAoB,CAAC;AACjC,EAAE,UAAU,EAAE,CAAC,sBAAsB,CAAC;AACtC,EAAE,UAAU,EAAE,CAAC,wBAAwB,CAAC;AACxC,EAAE,aAAa,EAAE,CAAC,sBAAsB,CAAC;AACzC,CAAC,CAAC;AACF,MAAM,SAAS,GAAG;AAClB,EAAE,IAAI,EAAE,CAAC,KAAK,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC;AACnE,EAAE,UAAU,EAAE,CAAC,KAAK,KAAK,CAAC,WAAW,EAAE,KAAK,GAAG,WAAW,GAAG,YAAY,CAAC,CAAC;AAC3E,EAAE,MAAM,EAAE,WAAW;AACrB,EAAE,UAAU,EAAE,eAAe;AAC7B,CAAC,CAAC;AACF,SAAS,aAAa,CAAC,SAAS,EAAE,GAAG,EAAE;AACvC,EAAE,KAAK,MAAM,CAAC,IAAI,SAAS,EAAE;AAC7B,IAAI,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAClC,IAAI,MAAM,WAAW,GAAG,EAAE,CAAC;AAC3B,IAAI,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE;AAC9B,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE;AACxB,QAAQ,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpD,OAAO,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC,EAAE;AAC/B,QAAQ,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzE,OAAO;AACP,KAAK;AACL,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAClD,GAAG;AACH,CAAC;AACD,MAAM,aAAa,SAAS,SAAS,CAAC;AACtC,EAAE,WAAW,CAAC,OAAO,GAAG,EAAE,EAAE;AAC5B,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC;AACnB,IAAI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AAC5B,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC;AACpE,IAAI,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;AAC7C,GAAG;AACH;AACA,EAAE,IAAI,YAAY,CAAC,KAAK,EAAE;AAC1B,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK,YAAY,KAAK,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;AAClE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;AAClB,GAAG;AACH,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC;AAC9B,GAAG;AACH,EAAE,YAAY,GAAG;AACjB,IAAI,IAAI,CAAC,SAAS,GAAG,oBAAoB,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/E,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC;AAC1B,GAAG;AACH,EAAE,MAAM,GAAG;AACX,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;AACnB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,KAAK,GAAG;AACV,IAAI,OAAO,IAAI,aAAa,CAAC;AAC7B,MAAM,KAAK,EAAE,IAAI,CAAC,KAAK;AACvB,MAAM,UAAU,EAAE,IAAI,CAAC,UAAU;AACjC,MAAM,UAAU,EAAE,IAAI,CAAC,UAAU,GAAG,EAAE,GAAG,IAAI,CAAC,UAAU,EAAE,GAAG,IAAI;AACjE,MAAM,IAAI,EAAE,IAAI,CAAC,KAAK;AACtB,MAAM,UAAU,EAAE,IAAI,CAAC,UAAU;AACjC,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ;AAC7B,MAAM,SAAS,EAAE,IAAI,CAAC,SAAS;AAC/B,MAAM,WAAW,EAAE,IAAI,CAAC,WAAW;AACnC,MAAM,UAAU,EAAE,IAAI,CAAC,UAAU;AACjC,MAAM,aAAa,EAAE,IAAI,CAAC,aAAa;AACvC,MAAM,UAAU,EAAE,IAAI,CAAC,UAAU;AACjC,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;AAC3B,MAAM,MAAM,EAAE,IAAI,CAAC,OAAO;AAC1B,MAAM,UAAU,EAAE,IAAI,CAAC,UAAU;AACjC,MAAM,QAAQ,EAAE,IAAI,CAAC,QAAQ;AAC7B,MAAM,aAAa,EAAE,IAAI,CAAC,aAAa;AACvC,MAAM,YAAY,EAAE,IAAI,CAAC,YAAY;AACrC,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,IAAI,QAAQ,GAAG;AACjB,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;AACzB,MAAM,IAAI,CAAC,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC;AAC1B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,GAAG,KAAK,EAAE;AACxB,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAC1B,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;AACvC,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;AACpB,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,GAAG,KAAK,EAAE;AAC3B,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACxE,IAAI,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7B,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AACjF,MAAM,IAAI,CAAC,MAAM,EAAE,CAAC;AACpB,KAAK;AACL,GAAG;AACH,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE;AAClB,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAChE,MAAM,IAAI,CAAC,8DAA8D,CAAC,CAAC;AAC3E,KAAK;AACL,IAAI,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;AACvB,GAAG;AACH,EAAE,IAAI,MAAM,CAAC,KAAK,EAAE;AACpB,IAAI,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACzE,MAAM,IAAI,CAAC,gEAAgE,CAAC,CAAC;AAC7E,KAAK;AACL,IAAI,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC;AACzB,GAAG;AACH,CAAC;AACD,MAAM,KAAK,GAAG,4BAA4B,CAAC;AAC3C,MAAM,OAAO,GAAG,8BAA8B,CAAC;AAC/C,MAAM,kBAAkB,CAAC;AACzB,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC1D,IAAI,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,eAAe,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC;AAC1E,IAAI,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,eAAe,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC/D,IAAI,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACnE,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;AAC7B,IAAI,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;AACtE,IAAI,aAAa,CAAC,YAAY,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACjD,IAAI,aAAa,CAAC,YAAY,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAClD,IAAI,aAAa,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC5C,IAAI,OAAO,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;AACvC,IAAI,aAAa,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;AAC5C,IAAI,aAAa,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AAC1C,GAAG;AACH,CAAC;AACD,IAAI,sBAAsB,CAAC;AAC3B,SAAS,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,kBAAkB,EAAE;AACxE,EAAE,kBAAkB,KAAK,kBAAkB,GAAG,sBAAsB,KAAK,sBAAsB,GAAG,IAAI,kBAAkB,EAAE,CAAC,CAAC,CAAC;AAC7H,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,kBAAkB,CAAC;AACnE,EAAE,UAAU,CAAC,SAAS,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,gCAAgC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACjG,EAAE,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,mDAAmD,CAAC,CAAC;AACxF,EAAE,IAAI,YAAY,EAAE;AACpB,IAAI,YAAY,CAAC,WAAW,GAAG,YAAY,CAAC;AAC5C,GAAG;AACH,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACrC,EAAE,MAAM,aAAa,GAAG,UAAU,CAAC,qBAAqB,EAAE,CAAC;AAC3D,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC;AACnB,EAAE,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC;AAC1C,EAAE,OAAO;AACT,IAAI,KAAK,EAAE,aAAa,CAAC,KAAK,GAAG,aAAa;AAC9C,IAAI,MAAM,EAAE,aAAa,CAAC,MAAM,GAAG,aAAa;AAChD,GAAG,CAAC;AACJ,CAAC;AACD,MAAM,YAAY,CAAC;AACnB,EAAE,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE;AACjC,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AAC/B,IAAI,IAAI,CAAC,oBAAoB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACpE,IAAI,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrE,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,IAAI,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;AAC5B,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;AACzB,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,sBAAsB,CAAC,CAAC;AAC5E,GAAG;AACH,EAAE,kBAAkB,CAAC,QAAQ,EAAE;AAC/B,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;AACrC,IAAI,MAAM,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACjE,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAC/E,IAAI,IAAI,UAAU,CAAC,WAAW,IAAI,UAAU,KAAK,UAAU,CAAC,WAAW,EAAE;AACzE,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,aAAa,CAAC,QAAQ,EAAE,cAAc,EAAE;AAC1C,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACxF,IAAI,IAAI,QAAQ,CAAC,aAAa,EAAE;AAChC,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC9B,KAAK;AACL,IAAI,IAAI,UAAU,CAAC,WAAW,EAAE;AAChC,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;AACnD,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC5D,MAAM,cAAc,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACnC,KAAK;AACL,GAAG;AACH,EAAE,gBAAgB,CAAC,QAAQ,EAAE;AAC7B,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC5D,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC/C,QAAQ,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AACjC,QAAQ,KAAK,CAAC,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC5C,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,iBAAiB,CAAC,QAAQ,EAAE;AAC9B,IAAI,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACjD,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AAC5D,GAAG;AACH,EAAE,OAAO,CAAC,QAAQ,EAAE;AACpB,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY;AAC9B,MAAM,OAAO;AACb,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACnC,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;AACrC,IAAI,MAAM,aAAa,GAAG,QAAQ,CAAC,eAAe,CAAC;AACnD,IAAI,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,MAAM,EAAE;AAC9D,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,MAAM,GAAG,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;AAChE,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC;AACnD,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC;AAClE,IAAI,aAAa,CAAC,gBAAgB,GAAG,QAAQ,CAAC,cAAc,CAAC;AAC7D,IAAI,aAAa,CAAC,MAAM,GAAG,QAAQ,CAAC,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;AACzE,IAAI,mBAAmB;AACvB,MAAM,QAAQ,CAAC,eAAe;AAC9B,MAAM,aAAa,CAAC,MAAM;AAC1B,MAAM,CAAC;AACP,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;AAC1C,GAAG;AACH,EAAE,QAAQ,CAAC,QAAQ,EAAE;AACrB,IAAI,MAAM,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACjE,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACxF,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,IAAI,UAAU,CAAC,WAAW,EAAE;AAChC,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;AAC/C,KAAK;AACL,IAAI,QAAQ,CAAC,OAAO,GAAG,UAAU,CAAC,WAAW,CAAC;AAC9C,GAAG;AACH,EAAE,aAAa,CAAC,QAAQ,EAAE,cAAc,EAAE;AAC1C,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC;AACtD,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;AAC5D,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC7C,MAAM,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAC/B,MAAM,SAAS,CAAC,UAAU,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC;AAClD,KAAK;AACL,GAAG;AACH,EAAE,wBAAwB,CAAC,QAAQ,EAAE;AACrC,IAAI,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;AAC/F,GAAG;AACH,EAAE,yBAAyB,CAAC,QAAQ,EAAE;AACtC,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC;AACrC,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAC5E,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;AAC3E,IAAI,MAAM,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,KAAK,KAAK;AACtD,MAAM,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AACxD,MAAM,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AAC/B,MAAM,UAAU,CAAC,UAAU,GAAG,QAAQ,CAAC;AACvC,MAAM,UAAU,CAAC,WAAW,GAAG,WAAW,CAAC;AAC3C,MAAM,OAAO,UAAU,CAAC;AACxB,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,EAAE;AAC5D,MAAM,QAAQ,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AAC7D,KAAK;AACL,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC;AACtD,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG;AACH,EAAE,yBAAyB,CAAC,WAAW,EAAE;AACzC,IAAI,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AAC9D,MAAM,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAC5B,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;AAClD,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;AAC5B,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACtB,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,oBAAoB,EAAE;AAC/C,MAAM,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;AACrC,GAAG;AACH,CAAC;AACD,YAAY,CAAC,SAAS,GAAG;AACzB,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,UAAU;AAC5B,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,UAAU;AAClB,CAAC,CAAC;AACF,MAAM,cAAc,GAAG,MAAM,eAAe,SAAS,YAAY,CAAC;AAClE,EAAE,WAAW,CAAC,GAAG,IAAI,EAAE;AACvB,IAAI,KAAK,CAAC,EAAE,CAAC,CAAC;AACd,IAAI,IAAI,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AAChC,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACrC,MAAM,WAAW,CAAC,MAAM,EAAE,8FAA8F,CAAC,CAAC;AAC1H,MAAM,OAAO,GAAG;AAChB,QAAQ,KAAK,EAAE,OAAO;AACtB,QAAQ,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;AACvB,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;AAC1B,QAAQ,SAAS,EAAE,IAAI,CAAC,CAAC,CAAC;AAC1B,OAAO,CAAC;AACR,KAAK;AACL,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACxB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,KAAK,CAAC,OAAO,EAAE;AACjB,IAAI,OAAO,GAAG,EAAE,GAAG,eAAe,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;AAChE,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC;AACzD,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC;AACzD,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC;AAC7C,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,MAAM,CAAC;AAChD,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AAClD,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;AACrB,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC;AACnB,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC;AACvB,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACzC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACzC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;AACzC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;AAC1C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;AACpC,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;AACnC,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;AACvC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;AACvC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;AAC7C,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC;AAC3C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;AACvC,MAAM,MAAM,IAAI,GAAG,CAAC,GAAG,SAAS,CAAC;AACjC,MAAM,MAAM,IAAI,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC;AACrC,MAAM,MAAM,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACjD,MAAM,MAAM,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC;AACtD,MAAM,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AACxD,MAAM,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC;AAC5D,MAAM,OAAO,CAAC,IAAI;AAClB,QAAQ,KAAK;AACb,QAAQ,MAAM;AACd,QAAQ,MAAM;AACd,QAAQ,MAAM;AACd,QAAQ,MAAM;AACd,QAAQ,MAAM;AACd,OAAO,CAAC;AACR,KAAK;AACL,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,YAAY,CAAC,KAAK,CAAC,CAAC;AACnD,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC;AACjD,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC;AACrD,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAC7B,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAC7B,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE,CAAC;AAC9B,GAAG;AACH,CAAC,CAAC;AACF,cAAc,CAAC,cAAc,GAAG;AAChC,EAAE,KAAK,EAAE,GAAG;AACZ,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,SAAS,EAAE,EAAE;AACf,EAAE,SAAS,EAAE,EAAE;AACf,CAAC,CAAC;AACF,IAAI,aAAa,GAAG,cAAc,CAAC;AACnC,MAAM,aAAa,CAAC;AACpB,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;AACjC,IAAI,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AAC5B,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;AAC7B,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;AAC1B,IAAI,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC,CAAC;AACrC,GAAG;AACH,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;AAC1C,GAAG;AACH,EAAE,IAAI,QAAQ,GAAG;AACjB,IAAI,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACpD,GAAG;AACH,EAAE,IAAI,QAAQ,CAAC,KAAK,EAAE;AACtB,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC3B,GAAG;AACH,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;AAC1B,IAAI,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC,CAAC;AACrC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,CAAC,KAAK,EAAE;AACpB,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK;AAC9B,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACzB,IAAI,IAAI,CAAC,sBAAsB,GAAG,CAAC,CAAC,CAAC;AACrC,GAAG;AACH,EAAE,IAAI,GAAG,GAAG;AACZ,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACnC,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AAC/C,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC;AAC9B,IAAI,IAAI,cAAc,GAAG,GAAG,CAAC;AAC7B,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;AACrD,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;AACjC,MAAM,cAAc,GAAG,IAAI,CAAC,eAAe,CAAC;AAC5C,MAAM,IAAI,IAAI,CAAC,sBAAsB,KAAK,aAAa,CAAC,SAAS,IAAI,IAAI,CAAC,WAAW,KAAK,QAAQ,CAAC,SAAS,EAAE;AAC9G,QAAQ,IAAI,CAAC,cAAc,IAAI,cAAc,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,EAAE;AACnE,UAAU,cAAc,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAC/E,SAAS;AACT,QAAQ,IAAI,CAAC,sBAAsB,GAAG,aAAa,CAAC,SAAS,CAAC;AAC9D,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,CAAC;AAC9C,QAAQ,aAAa,CAAC,WAAW,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;AACvD,OAAO;AACP,KAAK;AACL,IAAI,OAAO,cAAc,CAAC;AAC1B,GAAG;AACH,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;AACnC,GAAG;AACH,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;AACjC,GAAG;AACH,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC;AAC3C,GAAG;AACH,EAAE,IAAI,cAAc,GAAG;AACvB,IAAI,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC;AAC1C,GAAG;AACH,EAAE,IAAI,aAAa,GAAG;AACtB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC;AAC9C,GAAG;AACH,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC;AACxC,GAAG;AACH,CAAC;AACD,MAAM,QAAQ,CAAC;AACf,EAAE,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE;AACjC,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,YAAY,CAAC;AAC1C,MAAM,gBAAgB,EAAE,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;AACpE,MAAM,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AAC1E,MAAM,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;AACvC,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,sBAAsB,GAAG,IAAI,SAAS,CAAC;AAChD,MAAM,CAAC,EAAE,IAAI,CAAC,aAAa;AAC3B,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,aAAa,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC7D,IAAI,IAAI,CAAC,qBAAqB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrE,IAAI,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrE,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,IAAI,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;AAC5B,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;AACzB,IAAI,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;AACxE,IAAI,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;AAChE,GAAG;AACH,EAAE,kBAAkB,CAAC,IAAI,EAAE;AAC3B,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AAC7C,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC;AACxC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC;AACnC,IAAI,QAAQ,CAAC,OAAO,GAAG,SAAS,CAAC;AACjC,IAAI,IAAI,UAAU,KAAK,SAAS,EAAE;AAClC,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK,MAAM,IAAI,SAAS,EAAE;AAC1B,MAAM,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACtC,MAAM,IAAI,QAAQ,CAAC,OAAO,CAAC,MAAM,KAAK,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,KAAK,QAAQ,CAAC,UAAU,EAAE;AAC/G,QAAQ,QAAQ,CAAC,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC;AACrD,QAAQ,QAAQ,CAAC,UAAU,GAAG,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC;AACxD,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO;AACP,MAAM,MAAM,aAAa,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACzD,MAAM,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,qBAAqB;AAC1D,QAAQ,aAAa;AACrB,QAAQ,IAAI,CAAC,OAAO;AACpB,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,aAAa,CAAC,IAAI,EAAE,cAAc,EAAE;AACtC,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC;AACpD,IAAI,MAAM,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AAChD,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,MAAM,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAC5D,MAAM,gBAAgB,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC/C,MAAM,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACjD,MAAM,OAAO,CAAC,UAAU,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;AAC3D,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACpC,MAAM,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC/B,KAAK;AACL,GAAG;AACH,EAAE,gBAAgB,CAAC,IAAI,EAAE;AACzB,IAAI,IAAI,IAAI,CAAC,OAAO,EAAE;AACtB,MAAM,MAAM,gBAAgB,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACpE,MAAM,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACjD,MAAM,gBAAgB,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACjD,MAAM,gBAAgB,CAAC,QAAQ,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;AAChE,KAAK;AACL,GAAG;AACH,EAAE,iBAAiB,CAAC,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACxC,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACzD,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC9B,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AAClD,KAAK;AACL,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AACxD,GAAG;AACH,EAAE,OAAO,CAAC,IAAI,EAAE;AAChB,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY;AAC1B,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,yBAAyB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChG,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;AAC7C,IAAI,aAAa,CAAC,QAAQ,CAAC,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC;AAClE,IAAI,aAAa,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;AACnF,IAAI,aAAa,CAAC,MAAM,EAAE,CAAC;AAC3B,IAAI,mBAAmB;AACvB,MAAM,IAAI,CAAC,eAAe;AAC1B,MAAM,aAAa,CAAC,QAAQ,CAAC,MAAM;AACnC,MAAM,CAAC;AACP,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACtC,GAAG;AACH,EAAE,YAAY,CAAC,IAAI,EAAE;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AACpE,GAAG;AACH,EAAE,aAAa,CAAC,IAAI,EAAE;AACtB,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG;AACnC,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;AAC3B,MAAM,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,MAAM;AAC/C,MAAM,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,MAAM;AAClD,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AACvD,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACxC,GAAG;AACH,EAAE,iBAAiB,CAAC,IAAI,EAAE;AAC1B,IAAI,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;AACjF,GAAG;AACH,EAAE,kBAAkB,CAAC,IAAI,EAAE;AAC3B,IAAI,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;AAC/C,IAAI,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC;AAC9B,IAAI,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;AACpC,IAAI,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC;AAC5C,IAAI,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;AACzE,IAAI,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC;AACnD,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChD,MAAM,IAAI,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,EAAE;AACzC,QAAQ,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACtC,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC9B,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC9B,IAAI,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;AACvC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;AAC5B,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,GAAG;AACH,CAAC;AACD,QAAQ,CAAC,SAAS,GAAG;AACrB,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,UAAU;AAC5B,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,MAAM;AACd,CAAC,CAAC;AACF,MAAM,0BAA0B,CAAC;AACjC,EAAE,OAAO,CAAC,qBAAqB,EAAE,SAAS,EAAE;AAC5C,IAAI,MAAM,KAAK,GAAG,qBAAqB,CAAC,KAAK,CAAC;AAC9C,IAAI,MAAM,QAAQ,GAAG,qBAAqB,CAAC,QAAQ,CAAC;AACpD,IAAI,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,IAAI,qBAAqB,CAAC,aAAa,CAAC;AAC3E,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC;AAC1D,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,GAAG,qBAAqB,CAAC,aAAa,CAAC;AACpE,IAAI,MAAM,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC;AAC3B,IAAI,MAAM,MAAM,GAAG,qBAAqB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;AAC/D,IAAI,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACjC,IAAI,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC9B,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;AAC9D,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,iBAAiB,CAAC;AACxE,IAAI,MAAM,MAAM,GAAG,QAAQ,KAAK,CAAC,GAAG,EAAE,CAAC,cAAc,GAAG,EAAE,CAAC,YAAY,CAAC;AACxE,IAAI,EAAE,CAAC,YAAY,CAAC,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;AACpF,GAAG;AACH,CAAC;AACD,MAAM,2BAA2B,CAAC;AAClC,EAAE,OAAO,CAAC,qBAAqB,EAAE,SAAS,EAAE;AAC5C,IAAI,MAAM,QAAQ,GAAG,qBAAqB,CAAC,QAAQ,CAAC;AACpD,IAAI,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,IAAI,qBAAqB,CAAC,aAAa,CAAC;AAC3E,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,WAAW,CAAC,YAAY,CAAC,mBAAmB,CAAC,qBAAqB,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AACxH,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAC/E,IAAI,MAAM,KAAK,GAAG,qBAAqB,CAAC,KAAK,CAAC;AAC9C,IAAI,MAAM,MAAM,GAAG,qBAAqB,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;AAC/D,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC;AAC1B,MAAM,QAAQ,EAAE,MAAM,CAAC,QAAQ;AAC/B,MAAM,MAAM,EAAE,SAAS,CAAC,MAAM,IAAI,qBAAqB,CAAC,aAAa;AACrE,MAAM,KAAK;AACX,MAAM,IAAI,EAAE,SAAS,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC;AACjD,KAAK,CAAC,CAAC;AACP,GAAG;AACH,CAAC;AACD,SAAS,qBAAqB,CAAC,IAAI,EAAE,SAAS,GAAG,IAAI,EAAE;AACvD,EAAE,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,CAAC;AAChC,EAAE,IAAI,YAAY,GAAG,KAAK,EAAE;AAC5B,IAAI,SAAS,KAAK,SAAS,GAAG,IAAI,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC;AAC7D,GAAG,MAAM;AACT,IAAI,SAAS,KAAK,SAAS,GAAG,IAAI,WAAW,CAAC,YAAY,CAAC,CAAC,CAAC;AAC7D,GAAG;AACH,EAAE,IAAI,SAAS,CAAC,MAAM,KAAK,YAAY,EAAE;AACzC,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,oCAAoC,EAAE,SAAS,CAAC,MAAM,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;AAC5G,GAAG;AACH,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;AAC3D,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC7B,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC7B,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC7B,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC7B,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC7B,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC7B,GAAG;AACH,EAAE,OAAO,SAAS,CAAC;AACnB,CAAC;AACD,SAAS,8BAA8B,CAAC,UAAU,EAAE;AACpD,EAAE,OAAO;AACT,IAAI,aAAa,EAAE,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC;AAC3D,IAAI,YAAY,EAAE,sBAAsB,CAAC,UAAU,EAAE,KAAK,CAAC;AAC3D,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,sBAAsB,CAAC,UAAU,EAAE,OAAO,EAAE;AACrD,EAAE,MAAM,aAAa,GAAG,EAAE,CAAC;AAC3B,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY,CAAC,CAAC,CAAC;AACf,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB,EAAE,KAAK,MAAM,CAAC,IAAI,UAAU,EAAE;AAC9B,IAAI,MAAM,QAAQ,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACnC,IAAI,IAAI,OAAO,KAAK,QAAQ,CAAC,OAAO;AACpC,MAAM,SAAS;AACf,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;AACrD,IAAI,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACtC,IAAI,MAAM,aAAa,GAAG,0BAA0B,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACtE,IAAI,MAAM,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC;AACvC,GAAG;AACH,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;AACtB;AACA;AACA,IAAI,CAAC,CAAC,CAAC;AACP,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC;AACzB,qBAAqB,EAAE,MAAM,CAAC;AAC9B,IAAI,CAAC,CAAC,CAAC;AACP,EAAE,MAAM,cAAc,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAClD,EAAE,OAAO,IAAI,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;AAC5D,CAAC;AACD,MAAM,cAAc,CAAC;AACrB,EAAE,WAAW,CAAC,OAAO,EAAE;AACvB,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACnB,IAAI,IAAI,CAAC,4BAA4B,GAAG,EAAE,CAAC;AAC3C,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,IAAI,GAAG,CAAC;AAClD,IAAI,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;AAC1C,IAAI,IAAI,gBAAgB,GAAG,CAAC,CAAC;AAC7B,IAAI,IAAI,iBAAiB,GAAG,CAAC,CAAC;AAC9B,IAAI,KAAK,MAAM,CAAC,IAAI,UAAU,EAAE;AAChC,MAAM,MAAM,QAAQ,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACrC,MAAM,MAAM,aAAa,GAAG,0BAA0B,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACxE,MAAM,IAAI,QAAQ,CAAC,OAAO,EAAE;AAC5B,QAAQ,iBAAiB,IAAI,aAAa,CAAC,MAAM,CAAC;AAClD,OAAO,MAAM;AACb,QAAQ,gBAAgB,IAAI,aAAa,CAAC,MAAM,CAAC;AACjD,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,cAAc,GAAG,iBAAiB,GAAG,CAAC,CAAC;AAChD,IAAI,IAAI,CAAC,aAAa,GAAG,gBAAgB,GAAG,CAAC,CAAC;AAC9C,IAAI,IAAI,CAAC,qBAAqB,GAAG,IAAI,cAAc,CAAC,IAAI,GAAG,CAAC,GAAG,gBAAgB,CAAC,CAAC;AACjF,IAAI,IAAI,CAAC,sBAAsB,GAAG,IAAI,cAAc,CAAC,IAAI,GAAG,CAAC,GAAG,iBAAiB,CAAC,CAAC;AACnF,IAAI,IAAI,CAAC,WAAW,GAAG,qBAAqB,CAAC,IAAI,CAAC,CAAC;AACnD,IAAI,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;AACpC,IAAI,IAAI,aAAa,GAAG,CAAC,CAAC;AAC1B,IAAI,IAAI,YAAY,GAAG,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,MAAM,CAAC;AACpC,MAAM,IAAI,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC;AAC/B,MAAM,KAAK,EAAE,wBAAwB;AACrC,MAAM,WAAW,EAAE,KAAK;AACxB,MAAM,KAAK,EAAE,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,QAAQ;AACtD,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,MAAM,CAAC;AACrC,MAAM,IAAI,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC;AAC/B,MAAM,KAAK,EAAE,yBAAyB;AACtC,MAAM,WAAW,EAAE,KAAK;AACxB,MAAM,KAAK,EAAE,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,QAAQ;AACtD,KAAK,CAAC,CAAC;AACP,IAAI,KAAK,MAAM,CAAC,IAAI,UAAU,EAAE;AAChC,MAAM,MAAM,QAAQ,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACrC,MAAM,MAAM,aAAa,GAAG,0BAA0B,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACxE,MAAM,IAAI,QAAQ,CAAC,OAAO,EAAE;AAC5B,QAAQ,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,aAAa,EAAE;AACtD,UAAU,MAAM,EAAE,IAAI,CAAC,cAAc;AACrC,UAAU,MAAM,EAAE,IAAI,CAAC,cAAc,GAAG,CAAC;AACzC,UAAU,MAAM,EAAE,aAAa,GAAG,CAAC;AACnC,UAAU,MAAM,EAAE,QAAQ,CAAC,MAAM;AACjC,SAAS,CAAC,CAAC;AACX,QAAQ,aAAa,IAAI,aAAa,CAAC,IAAI,CAAC;AAC5C,OAAO,MAAM;AACb,QAAQ,QAAQ,CAAC,YAAY,CAAC,QAAQ,CAAC,aAAa,EAAE;AACtD,UAAU,MAAM,EAAE,IAAI,CAAC,aAAa;AACpC,UAAU,MAAM,EAAE,IAAI,CAAC,aAAa,GAAG,CAAC;AACxC,UAAU,MAAM,EAAE,YAAY,GAAG,CAAC;AAClC,UAAU,MAAM,EAAE,QAAQ,CAAC,MAAM;AACjC,SAAS,CAAC,CAAC;AACX,QAAQ,YAAY,IAAI,aAAa,CAAC,IAAI,CAAC;AAC3C,OAAO;AACP,KAAK;AACL,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACxC,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;AAC9D,IAAI,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC,aAAa,CAAC;AACvD,IAAI,IAAI,CAAC,aAAa,GAAG,cAAc,CAAC,YAAY,CAAC;AACrD,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,GAAG;AACH,EAAE,iBAAiB,CAAC,UAAU,EAAE;AAChC,IAAI,MAAM,GAAG,GAAG,kBAAkB,CAAC,UAAU,CAAC,CAAC;AAC/C,IAAI,IAAI,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,EAAE;AAChD,MAAM,OAAO,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,CAAC;AACpD,KAAK;AACL,IAAI,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;AACrF,IAAI,OAAO,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,CAAC;AAClD,GAAG;AACH,EAAE,sBAAsB,CAAC,UAAU,EAAE;AACrC,IAAI,OAAO,8BAA8B,CAAC,UAAU,CAAC,CAAC;AACtD,GAAG;AACH,EAAE,MAAM,CAAC,SAAS,EAAE,YAAY,EAAE;AAClC,IAAI,IAAI,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,EAAE;AACvC,MAAM,YAAY,GAAG,IAAI,CAAC;AAC1B,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;AACpE,MAAM,IAAI,CAAC,qBAAqB,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC/F,MAAM,IAAI,CAAC,sBAAsB,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACjG,MAAM,IAAI,CAAC,WAAW,GAAG,qBAAqB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3D,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,eAAe;AAC/C,QAAQ,IAAI,CAAC,WAAW;AACxB,QAAQ,IAAI,CAAC,WAAW,CAAC,UAAU;AACnC,QAAQ,IAAI;AACZ,OAAO,CAAC;AACR,KAAK;AACL,IAAI,MAAM,sBAAsB,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAC/D,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE,sBAAsB,CAAC,WAAW,EAAE,sBAAsB,CAAC,UAAU,CAAC,CAAC;AAC1G,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe;AACvC,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW;AAC7C,MAAM,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,GAAG,CAAC;AAChD,MAAM,IAAI;AACV,KAAK,CAAC;AACN,IAAI,IAAI,YAAY,EAAE;AACtB,MAAM,MAAM,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC;AAC/D,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,qBAAqB,CAAC,WAAW,EAAE,qBAAqB,CAAC,UAAU,CAAC,CAAC;AACzG,MAAM,IAAI,CAAC,aAAa,CAAC,eAAe;AACxC,QAAQ,qBAAqB,CAAC,WAAW;AACzC,QAAQ,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC;AACjD,QAAQ,IAAI;AACZ,OAAO,CAAC;AACR,KAAK;AACL,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;AACjC,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;AAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;AAC5B,GAAG;AACH,CAAC;AACD,SAAS,kBAAkB,CAAC,UAAU,EAAE;AACxC,EAAE,MAAM,MAAM,GAAG,EAAE,CAAC;AACpB,EAAE,KAAK,MAAM,GAAG,IAAI,UAAU,EAAE;AAChC,IAAI,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AACrC,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,OAAO,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;AAClE,GAAG;AACH,EAAE,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1B,CAAC;AACD,IAAI,QAAQ,GAAG,+KAA+K,CAAC;AAC/L,IAAI,MAAM,GAAG,69BAA69B,CAAC;AAC3+B,IAAI,IAAI,GAAG,6yCAA6yC,CAAC;AACzzC,MAAM,cAAc,SAAS,MAAM,CAAC;AACpC,EAAE,WAAW,GAAG;AAChB,IAAI,MAAM,UAAU,GAAG,SAAS,CAAC,IAAI,CAAC;AACtC,MAAM,MAAM;AACZ,MAAM,QAAQ;AACd,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,WAAW,GAAG,UAAU,CAAC,IAAI,CAAC;AACxC,MAAM,QAAQ,EAAE;AAChB,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,UAAU,EAAE,cAAc;AAClC,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,UAAU,EAAE,YAAY;AAChC,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,KAAK,CAAC;AACV,MAAM,SAAS,EAAE,UAAU;AAC3B,MAAM,UAAU,EAAE,WAAW;AAC7B,MAAM,SAAS,EAAE;AACjB;AACA,QAAQ,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM;AACtC;AACA,QAAQ,QAAQ,EAAE,IAAI,YAAY,CAAC,EAAE,CAAC;AACtC;AACA,QAAQ,QAAQ,EAAE;AAClB,UAAU,kBAAkB,EAAE,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;AAC1E,UAAU,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AACnE,UAAU,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;AAC3C,UAAU,WAAW,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AAC3D,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,CAAC;AACD,MAAM,qBAAqB,CAAC;AAC5B;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE;AACjC,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AAC/B,IAAI,IAAI,CAAC,cAAc,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9D,IAAI,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrE,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,YAAY,CAAC;AAC1C,MAAM,kBAAkB,EAAE,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;AACtE,MAAM,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AAC/D,MAAM,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;AACvC,MAAM,WAAW,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AACvD,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,cAAc,EAAE,CAAC;AAC9C,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AAC/B,GAAG;AACH,EAAE,kBAAkB,CAAC,WAAW,EAAE;AAClC,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,aAAa,CAAC,UAAU,EAAE,cAAc,EAAE;AAC5C,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC1D,IAAI,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACnC,GAAG;AACH,EAAE,UAAU,CAAC,UAAU,EAAE;AACzB,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AAC/E,GAAG;AACH,EAAE,WAAW,CAAC,UAAU,EAAE;AAC1B,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,cAAc,CAAC;AAC7D,MAAM,IAAI,EAAE,UAAU,CAAC,gBAAgB,CAAC,MAAM;AAC9C,MAAM,UAAU,EAAE,UAAU,CAAC,WAAW;AACxC,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AAC7D,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAC/C,GAAG;AACH,EAAE,gBAAgB,CAAC,WAAW,EAAE;AAChC,GAAG;AACH,EAAE,iBAAiB,CAAC,UAAU,EAAE;AAChC,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACvD,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;AACrB,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AAC/C,IAAI,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AAC9D,GAAG;AACH,EAAE,OAAO,CAAC,SAAS,EAAE;AACrB,IAAI,MAAM,QAAQ,GAAG,SAAS,CAAC,gBAAgB,CAAC;AAChD,IAAI,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AAC/B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACnC,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;AAC9C,IAAI,SAAS,CAAC,OAAO,KAAK,SAAS,CAAC,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACnE,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC7B,IAAI,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,SAAS,CAAC,cAAc,CAAC,CAAC;AACtD,IAAI,SAAS,CAAC,cAAc,GAAG,KAAK,CAAC;AACrC,IAAI,KAAK,CAAC,SAAS,GAAG,yBAAyB,CAAC,SAAS,CAAC,SAAS,EAAE,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChG,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;AACjD,IAAI,MAAM,oBAAoB,GAAG,QAAQ,CAAC,kBAAkB,CAAC;AAC7D,IAAI,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;AAC1D,IAAI,oBAAoB,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,gBAAgB,CAAC,CAAC;AAC7F,IAAI,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,UAAU,CAAC;AAChF,IAAI,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;AACrE,IAAI,mBAAmB;AACvB,MAAM,SAAS,CAAC,eAAe;AAC/B,MAAM,QAAQ,CAAC,MAAM;AACrB,MAAM,CAAC;AACP,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AAC1C,GAAG;AACH;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE;AAC5B,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;AACnC,MAAM,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAChC,KAAK;AACL,GAAG;AACH,CAAC;AACD,MAAM,uBAAuB,SAAS,qBAAqB,CAAC;AAC5D,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,KAAK,CAAC,QAAQ,EAAE,IAAI,0BAA0B,EAAE,CAAC,CAAC;AACtD,GAAG;AACH,CAAC;AACD,uBAAuB,CAAC,SAAS,GAAG;AACpC,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,UAAU;AAC5B,GAAG;AACH,EAAE,IAAI,EAAE,UAAU;AAClB,CAAC,CAAC;AACF,MAAM,wBAAwB,SAAS,qBAAqB,CAAC;AAC7D,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,KAAK,CAAC,QAAQ,EAAE,IAAI,2BAA2B,EAAE,CAAC,CAAC;AACvD,GAAG;AACH,CAAC;AACD,wBAAwB,CAAC,SAAS,GAAG;AACrC,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,UAAU;AAClB,CAAC,CAAC;AACF,MAAM,kBAAkB,GAAG,MAAM,mBAAmB,SAAS,aAAa,CAAC;AAC3E,EAAE,WAAW,CAAC,OAAO,GAAG,EAAE,EAAE;AAC5B,IAAI,OAAO,GAAG,EAAE,GAAG,mBAAmB,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;AACpE,IAAI,KAAK,CAAC;AACV,MAAM,KAAK,EAAE,OAAO,CAAC,KAAK;AAC1B,MAAM,MAAM,EAAE,OAAO,CAAC,MAAM;AAC5B,MAAM,SAAS,EAAE,CAAC;AAClB,MAAM,SAAS,EAAE,CAAC;AAClB,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACzB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,OAAO,EAAE;AAClB,IAAI,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC;AAC7C,IAAI,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC;AAChD,IAAI,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,cAAc,CAAC;AACvE,IAAI,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe,CAAC;AAC1E,IAAI,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC;AAC3D,IAAI,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC;AAC9D,IAAI,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC;AAC3D,IAAI,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,YAAY,IAAI,IAAI,CAAC,aAAa,CAAC;AACpE,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;AACrB,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;AAC3B,GAAG;AACH;AACA,EAAE,eAAe,GAAG;AACpB,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACrC,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;AACjD,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACvD,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC;AACnD,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AACzD,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC3C,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AAC3F,IAAI,SAAS,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC7G,IAAI,SAAS,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;AAChF,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AAC3F,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AACzG,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;AAC9E,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE,CAAC;AACzC,GAAG;AACH;AACA,EAAE,SAAS,GAAG;AACd,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACzB,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AAC5C,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AAC1C,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AAC7C,IAAI,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;AAC9C,IAAI,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC;AACzC,IAAI,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;AAC1C,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;AAClE,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC;AAClE,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC;AACvE,IAAI,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC;AAC1E,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;AACnC,GAAG;AACH,CAAC,CAAC;AACF,kBAAkB,CAAC,cAAc,GAAG;AACpC;AACA,EAAE,KAAK,EAAE,GAAG;AACZ;AACA,EAAE,MAAM,EAAE,GAAG;AACb;AACA,EAAE,SAAS,EAAE,EAAE;AACf;AACA,EAAE,SAAS,EAAE,EAAE;AACf;AACA,EAAE,UAAU,EAAE,EAAE;AAChB;AACA,EAAE,YAAY,EAAE,EAAE;AAClB;AACA,EAAE,aAAa,EAAE,GAAG;AACpB;AACA,EAAE,cAAc,EAAE,GAAG;AACrB,CAAC,CAAC;AACF,IAAI,iBAAiB,GAAG,kBAAkB,CAAC;AAC3C,MAAM,mBAAmB,CAAC;AAC1B,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,cAAc,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9D,IAAI,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrE,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;AACvE,GAAG;AACH,EAAE,aAAa,CAAC,MAAM,EAAE,cAAc,EAAE;AACxC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AACjD,IAAI,IAAI,MAAM,CAAC,aAAa;AAC5B,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AACrD,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;AAC3E,GAAG;AACH,EAAE,gBAAgB,CAAC,MAAM,EAAE;AAC3B,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACtD,IAAI,IAAI,MAAM,CAAC,aAAa;AAC5B,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AACrD,IAAI,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;AAChD,GAAG;AACH,EAAE,kBAAkB,CAAC,MAAM,EAAE;AAC7B,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AACjD,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,qBAAqB;AACpD,MAAM,SAAS;AACf,MAAM,MAAM,CAAC,QAAQ;AACrB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,iBAAiB,CAAC,MAAM,EAAE;AAC5B,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC1D,IAAI,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC3C,IAAI,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AAClC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3C,IAAI,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AAC1D,GAAG;AACH,EAAE,sBAAsB,CAAC,MAAM,EAAE,eAAe,EAAE;AAClD,IAAI,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC5C,IAAI,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AAChD,GAAG;AACH,EAAE,aAAa,CAAC,MAAM,EAAE;AACxB,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAC1E,GAAG;AACH,EAAE,cAAc,CAAC,MAAM,EAAE;AACzB,IAAI,MAAM,aAAa,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;AACrD,IAAI,aAAa,CAAC,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AAC5D,IAAI,aAAa,CAAC,UAAU,GAAG,MAAM,CAAC;AACtC,IAAI,aAAa,CAAC,SAAS,GAAG,MAAM,CAAC,cAAc,CAAC;AACpD,IAAI,aAAa,CAAC,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5C,IAAI,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;AAClF,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC;AACpD,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa,EAAE;AAC/B,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;AACzD,KAAK;AACL,IAAI,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AACzD,IAAI,OAAO,aAAa,CAAC;AACzB,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE;AACzC,MAAM,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;AACnD,MAAM,aAAa,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;AACvC,KAAK;AACL,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,CAAC;AACD,mBAAmB,CAAC,SAAS,GAAG;AAChC,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,UAAU;AAC5B,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,iBAAiB;AACzB,CAAC,CAAC;AACF,MAAM,SAAS,GAAG;AAClB,EAAE,IAAI,EAAE,YAAY;AACpB,EAAE,MAAM,EAAE;AACV,IAAI,MAAM;AACV;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,IAAI,IAAI;AACR;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,MAAM;AACV;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,IAAI,IAAI;AACR;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,GAAG;AACH,CAAC,CAAC;AACF,MAAM,WAAW,GAAG;AACpB,EAAE,IAAI,EAAE,YAAY;AACpB,EAAE,MAAM,EAAE;AACV,IAAI,MAAM;AACV;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,IAAI,IAAI;AACR;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,MAAM;AACV;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,IAAI,IAAI;AACR;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,GAAG;AACH,CAAC,CAAC;AACF,IAAI,YAAY,CAAC;AACjB,IAAI,WAAW,CAAC;AAChB,MAAM,kBAAkB,SAAS,MAAM,CAAC;AACxC,EAAE,WAAW,GAAG;AAChB,IAAI,YAAY,KAAK,YAAY,GAAG,2BAA2B,CAAC;AAChE,MAAM,IAAI,EAAE,sBAAsB;AAClC,MAAM,IAAI,EAAE;AACZ,QAAQ,eAAe;AACvB,QAAQ,SAAS;AACjB,QAAQ,cAAc;AACtB,OAAO;AACP,KAAK,CAAC,CAAC,CAAC;AACR,IAAI,WAAW,KAAK,WAAW,GAAG,0BAA0B,CAAC;AAC7D,MAAM,IAAI,EAAE,sBAAsB;AAClC,MAAM,IAAI,EAAE;AACZ,QAAQ,iBAAiB;AACzB,QAAQ,WAAW;AACnB,QAAQ,gBAAgB;AACxB,OAAO;AACP,KAAK,CAAC,CAAC,CAAC;AACR,IAAI,MAAM,cAAc,GAAG,IAAI,YAAY,CAAC;AAC5C,MAAM,SAAS,EAAE,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;AAC7D,MAAM,WAAW,EAAE,EAAE,KAAK,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AAC/E,MAAM,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AAC1E,MAAM,iBAAiB,EAAE,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;AACrE,MAAM,WAAW,EAAE,EAAE,KAAK,EAAE,IAAI,YAAY,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AACvF,KAAK,CAAC,CAAC;AACP,IAAI,KAAK,CAAC;AACV,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,UAAU,EAAE,YAAY;AAC9B,MAAM,SAAS,EAAE;AACjB,QAAQ,aAAa,EAAE,IAAI,YAAY,CAAC;AACxC,UAAU,gBAAgB,EAAE,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;AACxE,UAAU,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AAC9E,UAAU,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;AAC3C,SAAS,CAAC;AACV,QAAQ,cAAc;AACtB,QAAQ,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM;AACtC,QAAQ,QAAQ,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK;AAC5C,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE;AACnE,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;AACzD,IAAI,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,CAAC;AACvC,IAAI,MAAM,aAAa,GAAG,OAAO,CAAC,MAAM,CAAC;AACzC,IAAI,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;AAChD,IAAI,MAAM,iBAAiB,GAAG,cAAc,CAAC,QAAQ,CAAC,iBAAiB,CAAC;AACxE,IAAI,iBAAiB,CAAC,GAAG;AACzB,MAAM,MAAM,CAAC,CAAC,GAAG,YAAY,GAAG,KAAK;AACrC,MAAM,MAAM,CAAC,CAAC,GAAG,YAAY,GAAG,MAAM;AACtC,MAAM,MAAM,CAAC,CAAC,GAAG,aAAa,GAAG,KAAK;AACtC,MAAM,MAAM,CAAC,CAAC,GAAG,aAAa,GAAG,MAAM;AACvC,MAAM,MAAM,CAAC,EAAE,GAAG,KAAK;AACvB,MAAM,MAAM,CAAC,EAAE,GAAG,MAAM;AACxB,KAAK,CAAC;AACN,IAAI,iBAAiB,CAAC,MAAM,EAAE,CAAC;AAC/B,IAAI,cAAc,CAAC,QAAQ,CAAC,SAAS,GAAG,aAAa,CAAC,QAAQ,CAAC;AAC/D,IAAI,cAAc,CAAC,QAAQ,CAAC,WAAW,GAAG,aAAa,CAAC,WAAW,CAAC;AACpE,IAAI,cAAc,CAAC,QAAQ,CAAC,YAAY,GAAG,aAAa,CAAC,YAAY,CAAC;AACtE,IAAI,cAAc,CAAC,QAAQ,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAClE,IAAI,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AACnD,IAAI,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;AACpD,IAAI,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;AACrD,IAAI,cAAc,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;AACrD,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC;AAC/C,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;AACrD,KAAK;AACL,GAAG;AACH,CAAC;AACD,MAAM,YAAY,SAAS,YAAY,CAAC;AACxC,EAAE,WAAW,GAAG;AAChB,IAAI,KAAK,CAAC;AACV,MAAM,SAAS,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3D,MAAM,GAAG,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrD,MAAM,OAAO,EAAE,IAAI,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAClD,KAAK,CAAC,CAAC;AACP,GAAG;AACH,CAAC;AACD,SAAS,YAAY,CAAC,YAAY,EAAE,SAAS,EAAE;AAC/C,EAAE,MAAM,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;AACxC,EAAE,MAAM,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;AACxC,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC;AAC/C,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC;AAChD,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,IAAI,YAAY,CAAC,KAAK,CAAC;AACpD,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC;AAChD,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,IAAI,YAAY,CAAC,KAAK,CAAC;AACpD,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,IAAI,YAAY,CAAC,MAAM,CAAC;AACrD,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,GAAG,YAAY,CAAC,KAAK,CAAC;AAC/C,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,IAAI,YAAY,CAAC,MAAM,CAAC;AACrD,CAAC;AACD,SAAS,WAAW,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AACpD,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC;AAChB,EAAE,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AACrC,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACrB,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACrB,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACrB,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC;AACrB,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AACvB,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC;AACvB,EAAE,MAAM,IAAI,MAAM,CAAC;AACnB,EAAE,OAAO,KAAK,GAAG,IAAI,EAAE;AACvB,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AAC5B,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAChC,IAAI,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AACvC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AAC3C,IAAI,MAAM,IAAI,MAAM,CAAC;AACrB,IAAI,KAAK,EAAE,CAAC;AACZ,GAAG;AACH,CAAC;AACD,SAAS,MAAM,CAAC,YAAY,EAAE,GAAG,EAAE;AACnC,EAAE,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;AACvC,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;AACpC,EAAE,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;AACtC,EAAE,IAAI,OAAO,GAAG,CAAC,CAAC;AAClB,EAAE,IAAI,OAAO,GAAG,CAAC,CAAC;AAClB,EAAE,IAAI,YAAY,CAAC,oBAAoB,EAAE;AACzC,IAAI,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;AACpC,IAAI,OAAO,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;AACpC,GAAG;AACH,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC;AAC7B,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC;AAChC,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC;AAC7B,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC;AAChC,EAAE,MAAM,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC;AACtC,EAAE,aAAa,CAAC,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAC7D,EAAE,aAAa,CAAC,EAAE,IAAI,YAAY,CAAC,KAAK,CAAC;AACzC,EAAE,aAAa,CAAC,EAAE,IAAI,YAAY,CAAC,MAAM,CAAC;AAC1C,EAAE,aAAa,CAAC,MAAM,EAAE,CAAC;AACzB,EAAE,aAAa,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,EAAE,YAAY,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;AAChF,EAAE,WAAW,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;AACxC,CAAC;AACD,MAAM,UAAU,GAAG,IAAI,YAAY,EAAE,CAAC;AACtC,MAAM,gBAAgB,CAAC;AACvB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC;AAClC,IAAI,IAAI,CAAC,qBAAqB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrE,IAAI,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrE,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,uBAAuB,CAAC,CAAC;AAC9E,GAAG;AACH,EAAE,kBAAkB,CAAC,UAAU,EAAE;AACjC,IAAI,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;AACnE,IAAI,MAAM,UAAU,GAAG,gBAAgB,CAAC,QAAQ,CAAC;AACjD,IAAI,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;AACrC,IAAI,MAAM,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,CAAC;AAC/C,IAAI,IAAI,QAAQ,IAAI,QAAQ,KAAK,UAAU,EAAE;AAC7C,MAAM,MAAM,EAAE,aAAa,EAAE,GAAG,gBAAgB,CAAC;AACjD,MAAM,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,qBAAqB;AAC1D,QAAQ,aAAa;AACrB,QAAQ,UAAU,CAAC,OAAO;AAC1B,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO,UAAU,KAAK,QAAQ,CAAC;AACnC,GAAG;AACH,EAAE,aAAa,CAAC,YAAY,EAAE,cAAc,EAAE;AAC9C,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC;AACrD,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;AACvC,IAAI,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;AACrE,IAAI,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC;AACpD,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,gBAAgB,CAAC,aAAa,KAAK,gBAAgB,CAAC,aAAa,GAAG,IAAI,aAAa,EAAE,CAAC,CAAC;AAC/F,MAAM,MAAM,aAAa,GAAG,gBAAgB,CAAC,aAAa,CAAC;AAC3D,MAAM,IAAI,YAAY,CAAC,aAAa,EAAE;AACtC,QAAQ,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;AAChD,QAAQ,aAAa,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC1C,QAAQ,aAAa,CAAC,UAAU,GAAG,YAAY,CAAC;AAChD,QAAQ,aAAa,CAAC,SAAS,GAAG,YAAY,CAAC,cAAc,CAAC;AAC9D,QAAQ,aAAa,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;AACxD,OAAO;AACP,MAAM,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,YAAY,CAAC,YAAY,CAAC;AAC1F,MAAM,OAAO,CAAC,UAAU,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;AACxD,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACpC,MAAM,gBAAgB,CAAC,MAAM,KAAK,gBAAgB,CAAC,MAAM,GAAG,IAAI,kBAAkB,EAAE,CAAC,CAAC;AACtF,MAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;AAC1C,MAAM,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AACvC,KAAK;AACL,GAAG;AACH,EAAE,OAAO,CAAC,YAAY,EAAE;AACxB,IAAI,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACpE,IAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC;AAC/D,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC;AAClE,IAAI,aAAa,CAAC,gBAAgB,GAAG,YAAY,CAAC,cAAc,CAAC;AACjE,IAAI,aAAa,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,YAAY,CAAC,YAAY,CAAC;AACnF,IAAI,mBAAmB;AACvB,MAAM,YAAY,CAAC,eAAe;AAClC,MAAM,aAAa,CAAC,MAAM;AAC1B,MAAM,CAAC;AACP,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,GAAG,yBAAyB,CAAC,YAAY,CAAC,cAAc,EAAE,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACjH,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC;AAChC,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,MAAM;AACZ,MAAM,KAAK,EAAE,IAAI,CAAC,MAAM;AACxB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,gBAAgB,CAAC,YAAY,EAAE;AACjC,IAAI,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;AACrE,IAAI,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC;AAC1C,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,MAAM,EAAE,aAAa,EAAE,GAAG,gBAAgB,CAAC;AACjD,MAAM,IAAI,YAAY,CAAC,aAAa;AACpC,QAAQ,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;AAChD,MAAM,aAAa,CAAC,QAAQ,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC1D,KAAK,MAAM,IAAI,YAAY,CAAC,aAAa,EAAE;AAC3C,MAAM,MAAM,EAAE,MAAM,EAAE,GAAG,gBAAgB,CAAC;AAC1C,MAAM,MAAM,CAAC,cAAc;AAC3B,QAAQ,YAAY,CAAC,KAAK;AAC1B,QAAQ,YAAY,CAAC,MAAM;AAC3B,QAAQ,YAAY,CAAC,cAAc,CAAC,MAAM;AAC1C,QAAQ,YAAY,CAAC,MAAM,CAAC,CAAC;AAC7B,QAAQ,YAAY,CAAC,MAAM,CAAC,CAAC;AAC7B,QAAQ,YAAY,CAAC,OAAO;AAC5B,OAAO,CAAC;AACR,KAAK;AACL,GAAG;AACH,EAAE,iBAAiB,CAAC,YAAY,EAAE;AAClC,IAAI,MAAM,gBAAgB,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;AACrE,IAAI,gBAAgB,CAAC,aAAa,GAAG,IAAI,CAAC;AAC1C,IAAI,gBAAgB,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC;AACvC,IAAI,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACxD,IAAI,YAAY,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AAChE,GAAG;AACH,EAAE,oBAAoB,CAAC,UAAU,EAAE;AACnC,IAAI,OAAO,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,qBAAqB,CAAC,UAAU,CAAC,CAAC;AAChG,GAAG;AACH,EAAE,qBAAqB,CAAC,YAAY,EAAE;AACtC,IAAI,MAAM,QAAQ,GAAG,IAAI,YAAY,CAAC;AACtC,MAAM,OAAO,EAAE,UAAU,CAAC,OAAO;AACjC,MAAM,SAAS,EAAE,UAAU,CAAC,SAAS,CAAC,KAAK,EAAE;AAC7C,MAAM,GAAG,EAAE,UAAU,CAAC,GAAG,CAAC,KAAK,EAAE;AACjC,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG;AACnD,MAAM,QAAQ,EAAE,IAAI;AACpB,MAAM,UAAU,EAAE,YAAY;AAC9B,MAAM,QAAQ;AACd,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AAC/D,IAAI,OAAO,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AACxD,GAAG;AACH,EAAE,oBAAoB,CAAC,YAAY,EAAE;AACrC,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;AACnE,IAAI,MAAM,EAAE,QAAQ,EAAE,GAAG,cAAc,CAAC;AACxC,IAAI,MAAM,KAAK,GAAG,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;AACpD,IAAI,IAAI,KAAK,CAAC,WAAW,KAAK,QAAQ,EAAE;AACxC,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC;AACnC,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;AACrB,KAAK;AACL,IAAI,MAAM,CAAC,YAAY,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;AACvC,IAAI,YAAY,CAAC,YAAY,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;AACnD,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,qBAAqB,EAAE;AAChD,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC;AACvE,KAAK;AACL,IAAI,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACtC,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,EAAE,eAAe,CAAC,YAAY,EAAE;AAChC,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,CAAC;AACnE,IAAI,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;AACzC,IAAI,IAAI,kBAAkB,GAAG,IAAI,CAAC;AAClC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,YAAY,CAAC,KAAK,EAAE;AACpD,MAAM,kBAAkB,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC;AAC7E,KAAK;AACL,IAAI,cAAc,CAAC,QAAQ,GAAG,OAAO,CAAC,aAAa,CAAC,QAAQ,KAAK,kBAAkB,IAAI,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AACpH,IAAI,OAAO,cAAc,CAAC,QAAQ,CAAC;AACnC,GAAG;AACH,CAAC;AACD,gBAAgB,CAAC,SAAS,GAAG;AAC7B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,UAAU;AAC5B,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,cAAc;AACtB,CAAC,CAAC;AACF,MAAM,mBAAmB,GAAG;AAC5B,EAAE,IAAI,EAAE,wBAAwB;AAChC,EAAE,MAAM,EAAE;AACV,IAAI,MAAM;AACV;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,IAAI,IAAI;AACR;AACA,MAAM,CAAC;AACP;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,IAAI,GAAG;AACP;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,MAAM;AACV;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,CAAC;AACV,KAAK;AACL,IAAI,IAAI;AACR;AACA,MAAM,CAAC;AACP;AACA,QAAQ,CAAC;AACT,KAAK;AACL,GAAG;AACH,CAAC,CAAC;AACF,MAAM,qBAAqB,GAAG;AAC9B,EAAE,IAAI,EAAE,wBAAwB;AAChC,EAAE,MAAM,EAAE;AACV,IAAI,MAAM;AACV;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,IAAI,IAAI;AACR;AACA,MAAM,CAAC;AACP;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,IAAI,GAAG;AACP;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,MAAM;AACV;AACA,MAAM,CAAC;AACP;AACA,SAAS,CAAC;AACV,KAAK;AACL,IAAI,IAAI;AACR;AACA,MAAM,CAAC;AACP;AACA,QAAQ,CAAC;AACT,KAAK;AACL,GAAG;AACH,CAAC,CAAC;AACF,MAAM,OAAO,GAAG;AAChB,EAAE,IAAI,EAAE,UAAU;AAClB,EAAE,QAAQ,EAAE;AACZ,IAAI,MAAM;AACV;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,GAAG;AACH,CAAC,CAAC;AACF,MAAM,SAAS,GAAG;AAClB,EAAE,IAAI,EAAE,UAAU;AAClB,EAAE,QAAQ,EAAE;AACZ,IAAI,MAAM;AACV;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,GAAG;AACH,CAAC,CAAC;AACF,IAAI,UAAU,CAAC;AACf,IAAI,SAAS,CAAC;AACd,MAAM,SAAS,SAAS,MAAM,CAAC;AAC/B,EAAE,WAAW,GAAG;AAChB,IAAI,MAAM,QAAQ,GAAG,IAAI,YAAY,CAAC;AACtC,MAAM,MAAM,EAAE,EAAE,KAAK,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AAC1E,MAAM,gBAAgB,EAAE,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;AACpE,MAAM,SAAS,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;AAC1C,MAAM,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;AACvC,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,WAAW,GAAG,sBAAsB,EAAE,CAAC;AACjD,IAAI,UAAU,KAAK,UAAU,GAAG,2BAA2B,CAAC;AAC5D,MAAM,IAAI,EAAE,YAAY;AACxB,MAAM,IAAI,EAAE;AACZ,QAAQ,QAAQ;AAChB,QAAQ,uBAAuB,CAAC,WAAW,CAAC;AAC5C,QAAQ,mBAAmB;AAC3B,QAAQ,OAAO;AACf,QAAQ,cAAc;AACtB,OAAO;AACP,KAAK,CAAC,CAAC,CAAC;AACR,IAAI,SAAS,KAAK,SAAS,GAAG,0BAA0B,CAAC;AACzD,MAAM,IAAI,EAAE,YAAY;AACxB,MAAM,IAAI,EAAE;AACZ,QAAQ,UAAU;AAClB,QAAQ,yBAAyB,CAAC,WAAW,CAAC;AAC9C,QAAQ,qBAAqB;AAC7B,QAAQ,SAAS;AACjB,QAAQ,gBAAgB;AACxB,OAAO;AACP,KAAK,CAAC,CAAC,CAAC;AACR,IAAI,KAAK,CAAC;AACV,MAAM,SAAS;AACf,MAAM,UAAU;AAChB,MAAM,SAAS,EAAE;AACjB,QAAQ,aAAa,EAAE,QAAQ;AAC/B,QAAQ,aAAa,EAAE,4BAA4B,CAAC,WAAW,CAAC;AAChE,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,CAAC;AACD,MAAM,cAAc,CAAC;AACrB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;AAC7B,IAAI,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrE,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;AACvE,GAAG;AACH,EAAE,kBAAkB,CAAC,UAAU,EAAE;AACjC,IAAI,MAAM,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;AAClE,IAAI,IAAI,UAAU,CAAC,cAAc,EAAE;AACnC,MAAM,UAAU,CAAC,cAAc,GAAG,KAAK,CAAC;AACxC,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;AAC1D,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AACtF,GAAG;AACH,EAAE,aAAa,CAAC,UAAU,EAAE,cAAc,EAAE;AAC5C,IAAI,MAAM,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;AAClE,IAAI,aAAa,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;AAClD,IAAI,IAAI,UAAU,CAAC,cAAc,EAAE;AACnC,MAAM,UAAU,CAAC,cAAc,GAAG,KAAK,CAAC;AACxC,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;AAC1D,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,aAAa,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;AAC1F,IAAI,IAAI,kBAAkB,CAAC,OAAO,CAAC,YAAY,EAAE;AACjD,MAAM,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;AAC5C,KAAK;AACL,GAAG;AACH,EAAE,iBAAiB,CAAC,UAAU,EAAE;AAChC,IAAI,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AAC9D,IAAI,IAAI,CAAC,uBAAuB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AACjD,GAAG;AACH,EAAE,uBAAuB,CAAC,aAAa,EAAE;AACzC,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC;AAC/D,IAAI,IAAI,OAAO,CAAC,YAAY,EAAE;AAC9B,MAAM,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAC3C,MAAM,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;AAClC,KAAK;AACL,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC;AACvD,IAAI,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,GAAG,IAAI,CAAC;AAC9C,GAAG;AACH,EAAE,gBAAgB,CAAC,UAAU,EAAE;AAC/B,IAAI,MAAM,kBAAkB,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;AAClE,IAAI,aAAa,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC;AAClD,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;AAC7E,IAAI,IAAI,kBAAkB,CAAC,OAAO,CAAC,YAAY,EAAE;AACjD,MAAM,IAAI,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;AAC5C,KAAK;AACL,GAAG;AACH,EAAE,cAAc,CAAC,UAAU,EAAE,aAAa,EAAE;AAC5C,IAAI,MAAM,EAAE,OAAO,EAAE,GAAG,aAAa,CAAC;AACtC,IAAI,MAAM,UAAU,GAAG,iBAAiB,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;AACrF,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;AACpB,IAAI,IAAI,UAAU,CAAC,aAAa,CAAC,IAAI,KAAK,MAAM,EAAE;AAClD,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE;AACjC,QAAQ,OAAO,CAAC,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AACtD,OAAO;AACP,KAAK;AACL,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAC9C,IAAI,MAAM,KAAK,GAAG,UAAU,CAAC,MAAM,CAAC;AACpC,IAAI,IAAI,QAAQ,GAAG,UAAU,CAAC,cAAc,CAAC;AAC7C,IAAI,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;AACjF,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;AAClB,IAAI,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AAClC,IAAI,MAAM,KAAK,GAAG,gBAAgB,CAAC,KAAK,CAAC;AACzC,IAAI,IAAI,EAAE,GAAG,gBAAgB,CAAC,KAAK,CAAC;AACpC,IAAI,IAAI,EAAE,GAAG,gBAAgB,CAAC,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC;AAChE,IAAI,IAAI,KAAK,CAAC,OAAO,EAAE;AACvB,MAAM,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AACxC,MAAM,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AACxC,KAAK;AACL,IAAI,OAAO,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,GAAG,OAAO,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACxH,IAAI,MAAM,IAAI,GAAG,UAAU,CAAC,eAAe,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC;AAC3E,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5D,MAAM,MAAM,IAAI,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7C,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1D,QAAQ,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;AACpC,QAAQ,MAAM,QAAQ,GAAG,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAChD,QAAQ,IAAI,QAAQ,EAAE,OAAO,EAAE;AAC/B,UAAU,OAAO,CAAC,OAAO;AACzB,YAAY,QAAQ,CAAC,OAAO;AAC5B,YAAY,IAAI,GAAG,IAAI,GAAG,OAAO;AACjC,YAAY,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC;AAChE,YAAY,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC;AACnD,WAAW,CAAC;AACZ,SAAS;AACT,OAAO;AACP,MAAM,QAAQ,IAAI,UAAU,CAAC,UAAU,CAAC;AACxC,KAAK;AACL,GAAG;AACH,EAAE,iBAAiB,CAAC,UAAU,EAAE;AAChC,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;AAC/E,GAAG;AACH,EAAE,WAAW,CAAC,UAAU,EAAE;AAC1B,IAAI,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AAClD,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC;AAC1D,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;AACrD,IAAI,UAAU,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AAC7D,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAC/C,GAAG;AACH,EAAE,oBAAoB,CAAC,UAAU,EAAE;AACnC,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC;AAC/D,IAAI,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC;AACpD,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC;AAC1D,IAAI,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC,cAAc,CAAC;AACrD,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACxC,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACxC,IAAI,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AACzD,IAAI,MAAM,SAAS,GAAG,WAAW,CAAC,oBAAoB,GAAG,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC;AACpF,IAAI,MAAM,QAAQ,GAAG,UAAU,GAAG,WAAW,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC;AACpF,IAAI,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC/E,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,cAAc,EAAE;AAC3C,MAAM,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;AACxC,KAAK;AACL,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,CAAC;AACD,cAAc,CAAC,SAAS,GAAG;AAC3B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,UAAU;AAC5B,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,YAAY;AACpB,CAAC,CAAC;AACF,SAAS,aAAa,CAAC,SAAS,EAAE,KAAK,EAAE;AACzC,EAAE,KAAK,CAAC,cAAc,GAAG,SAAS,CAAC,cAAc,CAAC;AAClD,EAAE,KAAK,CAAC,eAAe,GAAG,SAAS,CAAC,eAAe,CAAC;AACpD,EAAE,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;AAC1C,EAAE,KAAK,CAAC,cAAc,GAAG,SAAS,CAAC,cAAc,CAAC;AAClD,EAAE,KAAK,CAAC,mBAAmB,GAAG,SAAS,CAAC,mBAAmB,CAAC;AAC5D,EAAE,KAAK,CAAC,cAAc,GAAG,SAAS,CAAC,cAAc,CAAC;AAClD,EAAE,KAAK,CAAC,kBAAkB,GAAG,SAAS,CAAC,kBAAkB,CAAC;AAC1D,EAAE,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC;AAC1C,EAAE,KAAK,CAAC,YAAY,GAAG,SAAS,CAAC,YAAY,CAAC;AAC9C,CAAC;AACD,SAAS,gBAAgB,CAAC,eAAe,EAAE,IAAI,EAAE;AACjD,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,eAAe,CAAC;AAC9C,EAAE,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAClD,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AACtC,EAAE,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC;AACzB,EAAE,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC;AACzB,EAAE,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC;AACzB,EAAE,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC;AACzB,CAAC;AACD,MAAM,YAAY,CAAC;AACnB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,QAAQ,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACxD,IAAI,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrE,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACtD,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AACjE,GAAG;AACH,EAAE,gBAAgB,GAAG;AACrB,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnC,MAAM,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACvC,MAAM,IAAI,CAAC,OAAO;AAClB,QAAQ,SAAS;AACjB,MAAM,MAAM,IAAI,GAAG,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC;AACtD,MAAM,IAAI,IAAI,CAAC,eAAe,EAAE;AAChC,QAAQ,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;AACrD,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;AAC5B,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,kBAAkB,CAAC,QAAQ,EAAE;AAC/B,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC/C,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;AACtC,IAAI,IAAI,OAAO,CAAC,qBAAqB,EAAE;AACvC,MAAM,OAAO,CAAC,qBAAqB,GAAG,KAAK,CAAC;AAC5C,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,UAAU,KAAK,MAAM,EAAE;AACvC,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,aAAa,CAAC,QAAQ,EAAE,cAAc,EAAE;AAC1C,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC/C,IAAI,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;AACpD,IAAI,IAAI,QAAQ,CAAC,cAAc,EAAE;AACjC,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;AACjF,GAAG;AACH,EAAE,gBAAgB,CAAC,QAAQ,EAAE;AAC7B,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC/C,IAAI,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;AACpD,IAAI,IAAI,QAAQ,CAAC,cAAc,EAAE;AACjC,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;AAC5D,GAAG;AACH,EAAE,iBAAiB,CAAC,QAAQ,EAAE;AAC9B,IAAI,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AAC5D,IAAI,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAC9C,GAAG;AACH,EAAE,sBAAsB,CAAC,WAAW,EAAE;AACtC,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AAC/C,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,sBAAsB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACvE,IAAI,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;AACtC,GAAG;AACH,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;AACtC,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC/C,IAAI,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;AACpD,IAAI,IAAI,OAAO,CAAC,UAAU,KAAK,MAAM,EAAE;AACvC,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK;AACjD,QAAQ,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACzB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,QAAQ,CAAC,cAAc,GAAG,KAAK,CAAC;AACpC,IAAI,gBAAgB,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;AAChD,GAAG;AACH,EAAE,MAAM,cAAc,CAAC,QAAQ,EAAE;AACjC,IAAI,QAAQ,CAAC,cAAc,GAAG,KAAK,CAAC;AACpC,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AAC/C,IAAI,IAAI,OAAO,CAAC,iBAAiB;AACjC,MAAM,OAAO;AACb,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,EAAE,CAAC;AACtC,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,sBAAsB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACvE,IAAI,OAAO,CAAC,iBAAiB,GAAG,IAAI,CAAC;AACrC,IAAI,OAAO,CAAC,UAAU,GAAG,MAAM,CAAC;AAChC,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;AACxE,IAAI,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,iBAAiB;AACnE,MAAM,QAAQ,CAAC,IAAI;AACnB,MAAM,UAAU;AAChB,MAAM,QAAQ,CAAC,MAAM;AACrB,MAAM,QAAQ,CAAC,OAAO,EAAE;AACxB,KAAK,CAAC;AACN,IAAI,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;AACpD,IAAI,eAAe,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,GAAG,OAAO,CAAC;AACxD,IAAI,OAAO,CAAC,iBAAiB,GAAG,KAAK,CAAC;AACtC,IAAI,OAAO,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACzC,IAAI,QAAQ,CAAC,YAAY,EAAE,CAAC;AAC5B,IAAI,gBAAgB,CAAC,eAAe,EAAE,QAAQ,CAAC,CAAC;AAChD,GAAG;AACH,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACrE,GAAG;AACH,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,MAAM,WAAW,GAAG;AACxB,MAAM,OAAO,EAAE,OAAO,CAAC,KAAK;AAC5B,MAAM,UAAU,EAAE,IAAI;AACtB,MAAM,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;AACnD,MAAM,qBAAqB,EAAE,KAAK;AAClC,MAAM,iBAAiB,EAAE,KAAK;AAC9B,KAAK,CAAC;AACN,IAAI,MAAM,eAAe,GAAG,WAAW,CAAC,eAAe,CAAC;AACxD,IAAI,eAAe,CAAC,UAAU,GAAG,QAAQ,CAAC;AAC1C,IAAI,eAAe,CAAC,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC;AACxD,IAAI,eAAe,CAAC,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;AAC5C,IAAI,eAAe,CAAC,MAAM,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;AACpE,IAAI,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;AACtF,IAAI,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;AACtG,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;AAC9C,IAAI,QAAQ,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AAC3D,IAAI,OAAO,WAAW,CAAC;AACvB,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnC,MAAM,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;AACrC,KAAK;AACL,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,CAAC;AACD,YAAY,CAAC,SAAS,GAAG;AACzB,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,UAAU;AAC5B,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,UAAU;AAClB,CAAC,CAAC;AACF,SAAS,QAAQ,GAAG;AACpB,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,CAAC;AACxD,EAAE,OAAO,gCAAgC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC1D,CAAC;AACD,MAAM,UAAU,GAAG,IAAI,MAAM,EAAE,CAAC;AAChC,SAAS,uBAAuB,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE;AACnE,EAAE,MAAM,MAAM,GAAG,UAAU,CAAC;AAC5B,EAAE,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;AAClB,EAAE,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;AAClB,EAAE,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,GAAG,UAAU,GAAG,CAAC,CAAC;AAC7C,EAAE,MAAM,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,GAAG,UAAU,GAAG,CAAC,CAAC;AAC9C,EAAE,MAAM,OAAO,GAAG,WAAW,CAAC,iBAAiB;AAC/C,IAAI,MAAM,CAAC,KAAK;AAChB,IAAI,MAAM,CAAC,MAAM;AACjB,IAAI,UAAU;AACd,IAAI,KAAK;AACT,GAAG,CAAC;AACJ,EAAE,OAAO,CAAC,MAAM,CAAC,cAAc,GAAG,OAAO,CAAC;AAC1C,EAAE,OAAO,CAAC,MAAM,CAAC,QAAQ,GAAG,KAAK,CAAC;AAClC,EAAE,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,6BAA6B,CAAC;AAC3D,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,GAAG,UAAU,CAAC;AAC3C,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,GAAG,UAAU,CAAC;AAC7C,EAAE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AAChD,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC;AACtB,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC;AACD,SAAS,mBAAmB,CAAC,IAAI,EAAE,KAAK,EAAE;AAC1C,EAAE,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;AACtC,EAAE,MAAM,YAAY,GAAG,EAAE,CAAC;AAC1B,EAAE,MAAM,MAAM,GAAG,EAAE,CAAC;AACpB,EAAE,MAAM,KAAK,GAAG,yBAAyB,CAAC;AAC1C,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACpC,EAAE,SAAS,aAAa,CAAC,WAAW,EAAE;AACtC,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE;AAC9B,MAAM,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AACrC,MAAM,MAAM,CAAC,WAAW,CAAC,GAAG,IAAI,CAAC;AACjC,KAAK;AACL,GAAG;AACH,EAAE,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;AACjC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAChD,MAAM,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;AACnC,KAAK;AACL,GAAG,MAAM;AACT,IAAI,aAAa,CAAC,UAAU,CAAC,CAAC;AAC9B,GAAG;AACH,EAAE,IAAI,OAAO,EAAE;AACf,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AAC/B,MAAM,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;AACrD,MAAM,aAAa,CAAC,WAAW,CAAC,CAAC;AACjC,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,KAAK,MAAM,CAAC,IAAI,KAAK,CAAC,SAAS,EAAE;AACnC,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;AACtD,IAAI,aAAa,CAAC,WAAW,CAAC,CAAC;AAC/B,GAAG;AACH,EAAE,OAAO,YAAY,CAAC;AACtB,CAAC;AACD,eAAe,gBAAgB,CAAC,GAAG,EAAE;AACrC,EAAE,MAAM,QAAQ,GAAG,MAAM,UAAU,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACrD,EAAE,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;AACrC,EAAE,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;AAClC,EAAE,MAAM,OAAO,GAAG,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;AACzD,IAAI,MAAM,CAAC,SAAS,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACpD,IAAI,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC;AAC5B,IAAI,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AAC/B,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC;AACD,eAAe,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE;AACvC,EAAE,MAAM,OAAO,GAAG,MAAM,gBAAgB,CAAC,GAAG,CAAC,CAAC;AAC9C,EAAE,OAAO,CAAC;AACV,sBAAsB,EAAE,KAAK,CAAC,UAAU,CAAC;AACzC,kBAAkB,EAAE,OAAO,CAAC;AAC5B,qBAAqB,EAAE,KAAK,CAAC,UAAU,CAAC;AACxC,oBAAoB,EAAE,KAAK,CAAC,SAAS,CAAC;AACtC,KAAK,CAAC,CAAC;AACP,CAAC;AACD,MAAM,qBAAqB,mBAAmB,IAAI,GAAG,EAAE,CAAC;AACxD,eAAe,UAAU,CAAC,YAAY,EAAE,KAAK,EAAE,cAAc,EAAE;AAC/D,EAAE,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC,UAAU,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,KAAK;AACtH,IAAI,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;AAChD,MAAM,MAAM,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;AACzD,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;AACnB,QAAQ,qBAAqB,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC;AAC1D,UAAU,UAAU,EAAE,KAAK,CAAC,UAAU;AACtC,UAAU,SAAS,EAAE,KAAK,CAAC,SAAS;AACpC,UAAU,UAAU;AACpB,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;AACjB,OAAO,MAAM;AACb,QAAQ,qBAAqB,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC;AAC1D,UAAU,UAAU,EAAE,cAAc,CAAC,UAAU;AAC/C,UAAU,SAAS,EAAE,cAAc,CAAC,SAAS;AAC7C,UAAU,UAAU;AACpB,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC;AACjB,OAAO;AACP,KAAK;AACL,IAAI,OAAO,qBAAqB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;AACjD,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AACtD,CAAC;AACD,SAAS,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE;AACnE,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,EAAE,GAAG,YAAY,CAAC;AAC7D,EAAE,UAAU,CAAC,SAAS,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,QAAQ,CAAC,gCAAgC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;AACjG,EAAE,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,iBAAiB,EAAE,UAAU,CAAC,mDAAmD,CAAC,CAAC,CAAC;AACxH,EAAE,YAAY,CAAC,WAAW,GAAG,OAAO,CAAC;AACrC,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,YAAY,CAAC,KAAK,CAAC;AAC/C,EAAE,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC;AAClD,EAAE,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;AACpD,EAAE,OAAO,IAAI,aAAa,EAAE,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;AACxD,CAAC;AACD,SAAS,2BAA2B,CAAC,KAAK,EAAE,UAAU,EAAE;AACxD,EAAE,MAAM,gBAAgB,GAAG,UAAU,CAAC,0BAA0B;AAChE,IAAI,KAAK,CAAC,KAAK;AACf,IAAI,KAAK,CAAC,MAAM;AAChB,IAAI,UAAU;AACd,GAAG,CAAC;AACJ,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,gBAAgB,CAAC;AACvC,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;AACrD,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACjC,EAAE,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AACD,SAAS,YAAY,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;AACzC,EAAE,OAAO,IAAI,OAAO,CAAC,OAAO,OAAO,KAAK;AACxC,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,MAAM,IAAI,OAAO,CAAC,CAAC,QAAQ,KAAK,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;AACjE,KAAK;AACL,IAAI,KAAK,CAAC,MAAM,GAAG,MAAM;AACzB,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK,CAAC;AACN,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,gCAAgC,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC7E,IAAI,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;AACpC,GAAG,CAAC,CAAC;AACL,CAAC;AACD,MAAM,cAAc,CAAC;AACrB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,aAAa,GAAG,QAAQ,CAAC,IAAI,KAAK,YAAY,CAAC,MAAM,CAAC;AAC/D,GAAG;AACH,EAAE,UAAU,CAAC,OAAO,EAAE;AACtB,IAAI,OAAO,IAAI,CAAC,oBAAoB;AACpC,MAAM,OAAO,CAAC,IAAI;AAClB,MAAM,OAAO,CAAC,UAAU;AACxB,MAAM,OAAO,CAAC,KAAK;AACnB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,iBAAiB,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE;AACtD,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE;AACvC,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;AAC5C,MAAM,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;AACnD,KAAK;AACL,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,oBAAoB,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK;AACzF,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,OAAO,GAAG,OAAO,CAAC;AACtD,MAAM,OAAO,OAAO,CAAC;AACrB,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG;AACpC,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,OAAO;AACb,MAAM,UAAU,EAAE,CAAC;AACnB,KAAK,CAAC;AACN,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG;AACH,EAAE,MAAM,oBAAoB,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE;AACtD,IAAI,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AACzD,IAAI,MAAM,YAAY,GAAG,mBAAmB,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC1D,IAAI,MAAM,OAAO,GAAG,MAAM,UAAU;AACpC,MAAM,YAAY;AAClB,MAAM,KAAK;AACX,MAAM,aAAa,CAAC,gBAAgB;AACpC,KAAK,CAAC;AACN,IAAI,MAAM,QAAQ,GAAG,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;AACzE,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;AACrG,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;AACvG,IAAI,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC;AACrC,IAAI,MAAM,YAAY,GAAG,CAAC,CAAC;AAC3B,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,YAAY,CAAC;AAC7C,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,MAAM,GAAG,CAAC,IAAI,YAAY,CAAC;AAC/C,IAAI,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;AAC7E,IAAI,MAAM,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC7E,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC;AAC3B,IAAI,IAAI,gBAAgB,CAAC;AACzB,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE;AAC5B,MAAM,gBAAgB,GAAG,2BAA2B,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;AACxE,KAAK;AACL,IAAI,MAAM,OAAO,GAAG,uBAAuB;AAC3C,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,GAAG,QAAQ;AAC3D,MAAM,KAAK,CAAC,KAAK,GAAG,YAAY;AAChC,MAAM,KAAK,CAAC,MAAM,GAAG,YAAY;AACjC,MAAM,UAAU;AAChB,KAAK,CAAC;AACN,IAAI,IAAI,IAAI,CAAC,aAAa,EAAE;AAC5B,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACxD,MAAM,UAAU,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;AAC1D,KAAK;AACL,IAAI,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;AACjC,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG;AACH,EAAE,uBAAuB,CAAC,OAAO,EAAE;AACnC,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,CAAC;AAC/C,GAAG;AACH,EAAE,sBAAsB,CAAC,OAAO,EAAE;AAClC,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AACxD,IAAI,IAAI,CAAC,aAAa;AACtB,MAAM,OAAO;AACb,IAAI,aAAa,CAAC,UAAU,EAAE,CAAC;AAC/B,IAAI,IAAI,aAAa,CAAC,UAAU,KAAK,CAAC,EAAE;AACxC,MAAM,IAAI,aAAa,CAAC,OAAO,EAAE;AACjC,QAAQ,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;AACrC,OAAO,MAAM;AACb,QAAQ,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,KAAK;AAChD,UAAU,aAAa,CAAC,OAAO,GAAG,OAAO,CAAC;AAC1C,UAAU,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;AACvC,SAAS,CAAC,CAAC,KAAK,CAAC,MAAM;AACvB,UAAU,IAAI,CAAC,yCAAyC,CAAC,CAAC;AAC1D,SAAS,CAAC,CAAC;AACX,OAAO;AACP,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;AAC3C,KAAK;AACL,GAAG;AACH,EAAE,QAAQ,CAAC,aAAa,EAAE;AAC1B,IAAI,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACrD,IAAI,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;AACjD,IAAI,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC,cAAc,GAAG,SAAS,CAAC;AAC5D,GAAG;AACH,EAAE,iBAAiB,CAAC,OAAO,EAAE;AAC7B,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC;AACpD,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,GAAG;AACH,CAAC;AACD,cAAc,CAAC,SAAS,GAAG;AAC3B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,YAAY;AAC9B,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,UAAU;AAClB,CAAC,CAAC;AACF,cAAc,CAAC,kBAAkB,GAAG;AACpC,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,SAAS,EAAE,QAAQ;AACrB,EAAE,UAAU,EAAE,QAAQ;AACtB,CAAC,CAAC;AACF,MAAM,cAAc,CAAC;AACrB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,QAAQ,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACxD,IAAI,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrE,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AACtD,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AACjE,GAAG;AACH,EAAE,gBAAgB,GAAG;AACrB,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnC,MAAM,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACvC,MAAM,IAAI,CAAC,OAAO;AAClB,QAAQ,SAAS;AACjB,MAAM,MAAM,IAAI,GAAG,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC;AACtD,MAAM,IAAI,IAAI,CAAC,eAAe,EAAE;AAChC,QAAQ,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;AACrD,QAAQ,IAAI,CAAC,YAAY,EAAE,CAAC;AAC5B,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,kBAAkB,CAAC,IAAI,EAAE;AAC3B,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC3C,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;AAClC,IAAI,IAAI,OAAO,CAAC,UAAU,KAAK,MAAM,EAAE;AACvC,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,aAAa,CAAC,IAAI,EAAE,cAAc,EAAE;AACtC,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC3C,IAAI,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;AACpD,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;AAC7B,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC7B,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,eAAe,EAAE,cAAc,CAAC,CAAC;AACjF,GAAG;AACH,EAAE,gBAAgB,CAAC,IAAI,EAAE;AACzB,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC3C,IAAI,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;AACpD,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;AAC7B,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC7B,KAAK;AACL,IAAI,eAAe,CAAC,QAAQ,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;AAC5D,GAAG;AACH,EAAE,iBAAiB,CAAC,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AACxD,IAAI,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC1C,GAAG;AACH,EAAE,sBAAsB,CAAC,OAAO,EAAE;AAClC,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC3C,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,sBAAsB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AACzE,IAAI,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;AAClC,GAAG;AACH,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;AAClC,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC3C,IAAI,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;AACpD,IAAI,IAAI,OAAO,CAAC,UAAU,KAAK,MAAM,EAAE;AACvC,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAChC,IAAI,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;AAC5C,GAAG;AACH,EAAE,cAAc,CAAC,IAAI,EAAE;AACvB,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC3C,IAAI,MAAM,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;AACpD,IAAI,IAAI,OAAO,CAAC,OAAO,EAAE;AACzB,MAAM,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,sBAAsB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAC3E,KAAK;AACL,IAAI,OAAO,CAAC,OAAO,GAAG,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAClG,IAAI,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;AACxC,IAAI,eAAe,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;AAC9C,GAAG;AACH,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC7D,GAAG;AACH,EAAE,WAAW,CAAC,IAAI,EAAE;AACpB,IAAI,MAAM,WAAW,GAAG;AACxB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,IAAI;AACtB,MAAM,eAAe,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC;AACnD,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,eAAe,CAAC,UAAU,GAAG,IAAI,CAAC;AAClD,IAAI,WAAW,CAAC,eAAe,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC;AAChE,IAAI,WAAW,CAAC,eAAe,CAAC,MAAM,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;AAChF,IAAI,WAAW,CAAC,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;AAC9F,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,WAAW,CAAC;AAC1C,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AAC1F,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC3B,IAAI,IAAI,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AACvD,IAAI,OAAO,WAAW,CAAC;AACvB,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnC,MAAM,IAAI,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;AACrC,KAAK;AACL,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,CAAC;AACD,cAAc,CAAC,SAAS,GAAG;AAC3B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,UAAU;AAC5B,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,MAAM;AACd,CAAC,CAAC;AACF,SAAS,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;AAClC,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,KAAK,EAAE,EAAE,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE;AACrE,IAAI,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;AAC7B,MAAM,OAAO,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD,SAAS,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,MAAM,EAAE;AAClD,EAAE,MAAM,MAAM,GAAG,CAAC,GAAG,KAAK,CAAC;AAC3B,EAAE,KAAK,IAAI,CAAC,GAAG,GAAG,EAAE,KAAK,GAAG,GAAG,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,MAAM,EAAE,EAAE,CAAC,EAAE,KAAK,IAAI,MAAM,EAAE;AACrF,IAAI,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;AAC7B,MAAM,OAAO,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD,SAAS,oBAAoB,CAAC,MAAM,EAAE,UAAU,GAAG,CAAC,EAAE;AACtD,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;AACnC,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE;AAC1C,IAAI,kBAAkB,EAAE,IAAI;AAC5B,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,OAAO,KAAK,IAAI,EAAE;AACxB,IAAI,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC;AAC3D,GAAG;AACH,EAAE,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AAC9D,EAAE,MAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC;AAC9B,EAAE,IAAI,IAAI,GAAG,CAAC,CAAC;AACf,EAAE,IAAI,GAAG,GAAG,CAAC,CAAC;AACd,EAAE,IAAI,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC;AAC1B,EAAE,OAAO,GAAG,GAAG,MAAM,IAAI,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC;AACnD,IAAI,EAAE,GAAG,CAAC;AACV,EAAE,IAAI,GAAG,KAAK,MAAM;AACpB,IAAI,OAAO,SAAS,CAAC,KAAK,CAAC;AAC3B,EAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC;AACtC,IAAI,EAAE,MAAM,CAAC;AACb,EAAE,OAAO,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,CAAC;AACpD,IAAI,EAAE,IAAI,CAAC;AACX,EAAE,OAAO,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,CAAC;AACrD,IAAI,EAAE,KAAK,CAAC;AACZ,EAAE,EAAE,KAAK,CAAC;AACV,EAAE,EAAE,MAAM,CAAC;AACX,EAAE,OAAO,IAAI,SAAS,CAAC,IAAI,GAAG,UAAU,EAAE,GAAG,GAAG,UAAU,EAAE,CAAC,KAAK,GAAG,IAAI,IAAI,UAAU,EAAE,CAAC,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,CAAC;AACtH,CAAC;AACD,MAAM,gBAAgB,CAAC;AACvB,EAAE,WAAW,CAAC,SAAS,EAAE;AACzB,IAAI,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/B,GAAG;AACH,EAAE,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE;AAC1C,IAAI,MAAM,QAAQ,GAAG,iBAAiB,CAAC,WAAW,CAAC,IAAI,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;AACvE,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;AACnG,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;AACrG,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;AACpC,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC;AACtC,IAAI,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC5B,IAAI,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC9B,IAAI,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;AAC7B,GAAG;AACH,EAAE,UAAU,CAAC,OAAO,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE;AACnD,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACrC,MAAM,WAAW,CAAC,OAAO,EAAE,mFAAmF,CAAC,CAAC;AAChH,MAAM,OAAO,GAAG;AAChB,QAAQ,IAAI,EAAE,OAAO;AACrB,QAAQ,KAAK;AACb,QAAQ,UAAU;AAClB,OAAO,CAAC;AACR,KAAK;AACL,IAAI,IAAI,EAAE,OAAO,CAAC,KAAK,YAAY,SAAS,CAAC,EAAE;AAC/C,MAAM,OAAO,CAAC,KAAK,GAAG,IAAI,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,sBAAsB;AACrE,MAAM,OAAO;AACb,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AACvD,IAAI,UAAU,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,CAAC;AACxD,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG;AACH,EAAE,sBAAsB,CAAC,OAAO,EAAE;AAClC,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AACpC,IAAI,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;AACvE,IAAI,MAAM,QAAQ,GAAG,iBAAiB,CAAC,WAAW,CAAC,IAAI,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;AACvE,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;AACrG,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;AACvG,IAAI,MAAM,gBAAgB,GAAG,UAAU,CAAC,0BAA0B,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAClF,IAAI,MAAM,EAAE,MAAM,EAAE,GAAG,gBAAgB,CAAC;AACxC,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC;AACvE,IAAI,MAAM,OAAO,GAAG,uBAAuB,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AAC/E,IAAI,IAAI,KAAK,CAAC,IAAI,EAAE;AACpB,MAAM,MAAM,OAAO,GAAG,oBAAoB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AAC/D,MAAM,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACtC,MAAM,OAAO,CAAC,SAAS,EAAE,CAAC;AAC1B,KAAK;AACL,IAAI,OAAO,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC;AACzC,GAAG;AACH,EAAE,iBAAiB,CAAC,IAAI,EAAE;AAC1B,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AAC1F,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;AACnC,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,EAAE;AACvC,MAAM,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;AAC5C,MAAM,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC;AACnD,KAAK;AACL,IAAI,MAAM,EAAE,OAAO,EAAE,gBAAgB,EAAE,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;AAC5E,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG;AACpC,MAAM,gBAAgB;AACtB,MAAM,OAAO;AACb,MAAM,UAAU,EAAE,CAAC;AACnB,KAAK,CAAC;AACN,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG;AACH,EAAE,uBAAuB,CAAC,OAAO,EAAE;AACnC,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,CAAC;AAC/C,GAAG;AACH,EAAE,sBAAsB,CAAC,OAAO,EAAE;AAClC,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AACxD,IAAI,aAAa,CAAC,UAAU,EAAE,CAAC;AAC/B,IAAI,IAAI,aAAa,CAAC,UAAU,KAAK,CAAC,EAAE;AACxC,MAAM,UAAU,CAAC,sBAAsB,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;AACxE,MAAM,WAAW,CAAC,aAAa,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACvD,MAAM,MAAM,MAAM,GAAG,aAAa,CAAC,OAAO,CAAC,MAAM,CAAC;AAClD,MAAM,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC7B,MAAM,MAAM,CAAC,cAAc,GAAG,SAAS,CAAC;AACxC,MAAM,MAAM,CAAC,SAAS,GAAG,sBAAsB,CAAC;AAChD,MAAM,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;AAC3C,KAAK;AACL,GAAG;AACH,EAAE,iBAAiB,CAAC,OAAO,EAAE;AAC7B,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC;AACpD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,kBAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,gBAAgB,EAAE;AAChE,IAAI,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,gBAAgB,CAAC;AACjD,IAAI,MAAM,IAAI,GAAG,uBAAuB,CAAC,KAAK,CAAC,CAAC;AAChD,IAAI,MAAM,QAAQ,GAAG,iBAAiB,CAAC,WAAW,CAAC,IAAI,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;AACvE,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;AACjC,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;AAC3C,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,UAAU,CAAC;AAC3C,IAAI,MAAM,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;AAC/C,IAAI,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC;AACnD,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;AACjC,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;AAC7B,IAAI,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;AAC1C,IAAI,OAAO,CAAC,YAAY,GAAG,KAAK,CAAC,YAAY,CAAC;AAC9C,IAAI,IAAI,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE;AAC9B,MAAM,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC;AACxC,MAAM,OAAO,CAAC,SAAS,GAAG,WAAW,CAAC,KAAK,CAAC;AAC5C,MAAM,OAAO,CAAC,UAAU,GAAG,WAAW,CAAC,UAAU,CAAC;AAClD,MAAM,OAAO,CAAC,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC;AAC1C,MAAM,OAAO,CAAC,OAAO,GAAG,WAAW,CAAC,GAAG,CAAC;AACxC,KAAK;AACL,IAAI,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;AACxB,IAAI,IAAI,aAAa,CAAC;AACtB,IAAI,IAAI,aAAa,CAAC;AACtB,IAAI,MAAM,WAAW,GAAG,KAAK,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;AACjD,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,EAAE,CAAC,EAAE;AAC1C,MAAM,MAAM,YAAY,GAAG,KAAK,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,CAAC;AACvD,MAAM,MAAM,YAAY,GAAG,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACjG,MAAM,MAAM,cAAc,GAAG,YAAY,GAAG,UAAU,CAAC;AACvD,MAAM,IAAI,YAAY,EAAE;AACxB,QAAQ,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC;AACpC,QAAQ,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC;AACtC,QAAQ,MAAM,aAAa,GAAG,KAAK,CAAC,UAAU,CAAC;AAC/C,QAAQ,MAAM,eAAe,GAAG,aAAa,CAAC,KAAK,CAAC;AACpD,QAAQ,MAAM,eAAe,GAAG,aAAa,CAAC,KAAK,CAAC;AACpD,QAAQ,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,YAAY,EAAE,CAAC;AAC9G,QAAQ,MAAM,cAAc,GAAG,aAAa,CAAC,IAAI,GAAG,UAAU,CAAC;AAC/D,QAAQ,MAAM,kBAAkB,GAAG,aAAa,CAAC,QAAQ,GAAG,UAAU,CAAC;AACvE,QAAQ,OAAO,CAAC,UAAU,GAAG,cAAc,CAAC;AAC5C,QAAQ,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,kBAAkB,CAAC;AACnF,QAAQ,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,kBAAkB,GAAG,cAAc,CAAC;AACpG,OAAO,MAAM;AACb,QAAQ,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,GAAG,kBAAkB,CAAC,KAAK,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,IAAI,CAAC;AAC1F,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE;AAClC,UAAU,OAAO,CAAC,WAAW,GAAG,kBAAkB,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AAC3E,SAAS;AACT,QAAQ,OAAO,CAAC,WAAW,GAAG,OAAO,CAAC;AACtC,OAAO;AACP,MAAM,IAAI,kBAAkB,GAAG,CAAC,UAAU,GAAG,cAAc,CAAC,QAAQ,IAAI,CAAC,CAAC;AAC1E,MAAM,IAAI,UAAU,GAAG,cAAc,CAAC,QAAQ,GAAG,CAAC,EAAE;AACpD,QAAQ,kBAAkB,GAAG,CAAC,CAAC;AAC/B,OAAO;AACP,MAAM,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,EAAE,KAAK,IAAI,CAAC,CAAC;AACpD,MAAM,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AAChD,QAAQ,aAAa,GAAG,WAAW,GAAG,CAAC,CAAC;AACxC,QAAQ,aAAa,GAAG,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,UAAU,GAAG,cAAc,CAAC,MAAM,GAAG,kBAAkB,CAAC;AACvG,QAAQ,IAAI,KAAK,CAAC,KAAK,KAAK,OAAO,EAAE;AACrC,UAAU,aAAa,IAAI,YAAY,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;AACzD,SAAS,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,QAAQ,EAAE;AAC7C,UAAU,aAAa,IAAI,CAAC,YAAY,GAAG,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;AAC/D,SAAS;AACT,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE;AAClC,UAAU,IAAI,CAAC,kBAAkB;AACjC,YAAY,KAAK,CAAC,EAAE,CAAC;AACrB,YAAY,KAAK;AACjB,YAAY,gBAAgB;AAC5B,YAAY,aAAa,GAAG,KAAK,CAAC,OAAO;AACzC,YAAY,aAAa,GAAG,KAAK,CAAC,OAAO,GAAG,YAAY;AACxD,YAAY,IAAI;AAChB,WAAW,CAAC;AACZ,SAAS;AACT,QAAQ,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,EAAE;AACpC,UAAU,IAAI,CAAC,kBAAkB;AACjC,YAAY,KAAK,CAAC,EAAE,CAAC;AACrB,YAAY,KAAK;AACjB,YAAY,gBAAgB;AAC5B,YAAY,aAAa,GAAG,KAAK,CAAC,OAAO;AACzC,YAAY,aAAa,GAAG,KAAK,CAAC,OAAO,GAAG,YAAY;AACxD,WAAW,CAAC;AACZ,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,kBAAkB,CAAC,IAAI,EAAE,KAAK,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,GAAG,KAAK,EAAE;AAC5E,IAAI,MAAM,EAAE,OAAO,EAAE,GAAG,gBAAgB,CAAC;AACzC,IAAI,MAAM,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;AAC9C,IAAI,IAAI,4BAA4B,GAAG,KAAK,CAAC;AAC7C,IAAI,IAAI,iBAAiB,CAAC,kCAAkC,EAAE;AAC9D,MAAM,IAAI,iBAAiB,CAAC,yBAAyB,EAAE;AACvD,QAAQ,OAAO,CAAC,aAAa,GAAG,CAAC,EAAE,aAAa,CAAC,EAAE,CAAC,CAAC;AACrD,QAAQ,OAAO,CAAC,iBAAiB,GAAG,CAAC,EAAE,aAAa,CAAC,EAAE,CAAC,CAAC;AACzD,QAAQ,4BAA4B,GAAG,IAAI,CAAC;AAC5C,OAAO,MAAM;AACb,QAAQ,OAAO,CAAC,aAAa,GAAG,KAAK,CAAC;AACtC,QAAQ,OAAO,CAAC,iBAAiB,GAAG,KAAK,CAAC;AAC1C,OAAO;AACP,KAAK;AACL,IAAI,IAAI,aAAa,KAAK,CAAC,IAAI,4BAA4B,EAAE;AAC7D,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACvC,OAAO,MAAM;AACb,QAAQ,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrC,OAAO;AACP,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,eAAe,GAAG,CAAC,CAAC;AAC5B,IAAI,MAAM,WAAW,GAAG,iBAAiB,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAClE,IAAI,IAAI,aAAa,GAAG,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC;AACxD,IAAI,IAAI,YAAY,GAAG,CAAC,CAAC;AACzB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AACjD,MAAM,MAAM,WAAW,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AACzC,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,OAAO,CAAC,UAAU,CAAC,WAAW,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;AAC5D,OAAO,MAAM;AACb,QAAQ,OAAO,CAAC,QAAQ,CAAC,WAAW,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC;AAC1D,OAAO;AACP,MAAM,IAAI,OAAO,GAAG,EAAE,CAAC;AACvB,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;AACvD,QAAQ,OAAO,IAAI,WAAW,CAAC,CAAC,CAAC,CAAC;AAClC,OAAO;AACP,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC;AACxD,MAAM,eAAe,IAAI,aAAa,GAAG,YAAY,GAAG,aAAa,CAAC;AACtE,MAAM,aAAa,GAAG,YAAY,CAAC;AACnC,KAAK;AACL,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,GAAG;AACH,CAAC;AACD,gBAAgB,CAAC,SAAS,GAAG;AAC7B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,YAAY;AAC9B,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,YAAY;AACpB,CAAC,CAAC;AACF,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AAC7B,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AAC7B,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AAC7B,UAAU,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;AACtC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;AACzB,UAAU,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;AACxC,UAAU,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;AACzC,UAAU,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;AACjC,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AAC/B,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AAC/B,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AAC/B,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AAC7B,UAAU,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;AACjC,UAAU,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;AACpC,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;AAC7B,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC"}