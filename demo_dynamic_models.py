"""
DTrans动态模型功能演示
展示最新的动态模型获取和搜索功能
"""

import os
from utils.openrouter_client import OpenRouterClient
from dotenv import load_dotenv

def demo_latest_models():
    """演示最新模型获取"""
    print("🆕 最新高质量模型展示")
    print("=" * 60)
    
    load_dotenv()
    client = OpenRouterClient()
    
    try:
        models = client.get_available_models()
        
        print(f"📊 总共获取到 {len(models)} 个最新模型")
        print("\n🏆 推荐的顶级翻译模型 (前15个):")
        
        for i, model in enumerate(models[:15], 1):
            tags_str = ", ".join(model.get('tags', [])[:4])
            context_length = model.get('context_length', 0)
            
            if context_length >= 1000000:
                context_str = f"{context_length//1000000}M"
            elif context_length >= 1000:
                context_str = f"{context_length//1000}K"
            else:
                context_str = str(context_length) if context_length else "未知"
            
            print(f"  {i:2d}. {model['name']}")
            print(f"      ID: {model['id']}")
            print(f"      特性: [{tags_str}]")
            print(f"      上下文: {context_str} tokens")
            print()
        
    except Exception as e:
        print(f"❌ 获取模型失败: {e}")

def demo_provider_distribution():
    """演示提供商分布"""
    print("🏢 模型提供商分布")
    print("=" * 60)
    
    client = OpenRouterClient()
    models = client.get_available_models()
    
    # 统计提供商
    providers = {}
    for model in models:
        model_id = model['id']
        if '/' in model_id:
            provider = model_id.split('/')[0]
            if provider not in providers:
                providers[provider] = []
            providers[provider].append(model)
    
    # 显示主要提供商
    print("📈 主要AI模型提供商:")
    for provider, provider_models in sorted(providers.items(), key=lambda x: len(x[1]), reverse=True)[:10]:
        print(f"  {provider:15s}: {len(provider_models):3d} 个模型")
        
        # 显示该提供商的代表性模型
        top_models = provider_models[:3]
        for model in top_models:
            print(f"    └─ {model['name']}")
        
        if len(provider_models) > 3:
            print(f"    └─ ... 还有 {len(provider_models) - 3} 个模型")
        print()

def demo_search_scenarios():
    """演示不同搜索场景"""
    print("🔍 智能搜索场景演示")
    print("=" * 60)
    
    from app import DTransApp
    app = DTransApp()
    models = app.load_models()
    
    scenarios = [
        {
            "name": "寻找最新Claude模型",
            "query": "claude",
            "description": "获取Anthropic最新的Claude系列模型"
        },
        {
            "name": "寻找快速经济模型",
            "query": "快速 经济",
            "description": "适合大量文档快速翻译的经济型模型"
        },
        {
            "name": "寻找长上下文模型",
            "query": "长上下文",
            "description": "能处理长文档的大上下文模型"
        },
        {
            "name": "寻找开源模型",
            "query": "开源",
            "description": "开源的高质量翻译模型"
        },
        {
            "name": "寻找多模态模型",
            "query": "多模态",
            "description": "支持图文混合内容的模型"
        },
        {
            "name": "寻找推理专长模型",
            "query": "推理专长",
            "description": "在复杂推理任务中表现优秀的模型"
        }
    ]
    
    for scenario in scenarios:
        print(f"📋 场景: {scenario['name']}")
        print(f"   描述: {scenario['description']}")
        print(f"   搜索: \"{scenario['query']}\"")
        
        filtered = app.filter_models(scenario['query'], models)
        print(f"   结果: {len(filtered)} 个匹配模型")
        
        if filtered:
            print("   推荐:")
            for i, (model_display, model_id) in enumerate(filtered[:3], 1):
                print(f"     {i}. {model_display}")
        print()

def demo_model_comparison():
    """演示模型对比"""
    print("⚖️ 模型对比分析")
    print("=" * 60)
    
    client = OpenRouterClient()
    models = client.get_available_models()
    
    # 选择几个代表性模型进行对比
    comparison_models = []
    model_names = ["claude", "gpt-4", "gemini", "llama"]
    
    for name in model_names:
        for model in models:
            if name in model['id'].lower():
                comparison_models.append(model)
                break
    
    print("🔬 代表性模型对比:")
    print(f"{'模型名称':<30} {'上下文':<10} {'主要特性':<30}")
    print("-" * 80)
    
    for model in comparison_models:
        name = model['name'][:28]
        context = model.get('context_length', 0)
        if context >= 1000000:
            context_str = f"{context//1000000}M"
        elif context >= 1000:
            context_str = f"{context//1000}K"
        else:
            context_str = str(context) if context else "未知"
        
        tags = ", ".join(model.get('tags', [])[:3])
        print(f"{name:<30} {context_str:<10} {tags:<30}")

def demo_refresh_functionality():
    """演示刷新功能"""
    print("🔄 模型刷新功能演示")
    print("=" * 60)
    
    client = OpenRouterClient()
    
    print("1️⃣ 首次获取模型列表...")
    models1 = client.get_available_models()
    print(f"   获取到 {len(models1)} 个模型")
    
    print("\n2️⃣ 再次获取（应使用缓存）...")
    models2 = client.get_available_models()
    print(f"   获取到 {len(models2)} 个模型")
    
    print("\n3️⃣ 强制刷新模型列表...")
    models3 = client.refresh_models_cache()
    print(f"   刷新后获取到 {len(models3)} 个模型")
    
    print("\n✅ 刷新功能正常工作！")

def main():
    """主演示函数"""
    print("🌐 DTrans动态模型功能完整演示")
    print("=" * 70)
    print()
    
    try:
        # 演示最新模型
        demo_latest_models()
        
        # 演示提供商分布
        demo_provider_distribution()
        
        # 演示搜索场景
        demo_search_scenarios()
        
        # 演示模型对比
        demo_model_comparison()
        
        # 演示刷新功能
        demo_refresh_functionality()
        
        print("\n🎉 演示完成！")
        print("\n💡 核心优势:")
        print("- ✅ 动态获取319+个最新模型")
        print("- ✅ 智能缓存和刷新机制")
        print("- ✅ 强大的搜索和过滤功能")
        print("- ✅ 详细的模型信息和标签")
        print("- ✅ 智能排序和推荐")
        print("- ✅ 完整的错误处理和备用机制")
        print()
        print("🚀 启动完整应用: python app.py")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
