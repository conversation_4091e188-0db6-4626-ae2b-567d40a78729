# pylint: disable=too-many-lines
# coding=utf-8
# --------------------------------------------------------------------------
# Copyright (c) Microsoft Corporation. All rights reserved.
# Licensed under the MIT License. See License.txt in the project root for license information.
# Code generated by Microsoft (R) Python Code Generator.
# Changes may cause incorrect behavior and will be lost if the code is regenerated.
# --------------------------------------------------------------------------
# pylint: disable=useless-super-delegation

import datetime
from typing import Any, Dict, List, Literal, Mapping, Optional, TYPE_CHECKING, Union, overload

from .. import _model_base
from .._model_base import rest_discriminator, rest_field
from ._enums import OperationKind

if TYPE_CHECKING:
    from .. import models as _models


class AddressValue(_model_base.Model):
    """Address field value.

    :ivar house_number: House or building number.
    :vartype house_number: str
    :ivar po_box: Post office box number.
    :vartype po_box: str
    :ivar road: Street name.
    :vartype road: str
    :ivar city: Name of city, town, village, etc.
    :vartype city: str
    :ivar state: First-level administrative division.
    :vartype state: str
    :ivar postal_code: Postal code used for mail sorting.
    :vartype postal_code: str
    :ivar country_region: Country/region.
    :vartype country_region: str
    :ivar street_address: Street-level address, excluding city, state, countryRegion, and
     postalCode.
    :vartype street_address: str
    :ivar unit: Apartment or office number.
    :vartype unit: str
    :ivar city_district: Districts or boroughs within a city, such as Brooklyn in New York City or
     City
     of Westminster in London.
    :vartype city_district: str
    :ivar state_district: Second-level administrative division used in certain locales.
    :vartype state_district: str
    :ivar suburb: Unofficial neighborhood name, like Chinatown.
    :vartype suburb: str
    :ivar house: Build name, such as World Trade Center.
    :vartype house: str
    :ivar level: Floor number, such as 3F.
    :vartype level: str
    """

    house_number: Optional[str] = rest_field(name="houseNumber")
    """House or building number."""
    po_box: Optional[str] = rest_field(name="poBox")
    """Post office box number."""
    road: Optional[str] = rest_field()
    """Street name."""
    city: Optional[str] = rest_field()
    """Name of city, town, village, etc."""
    state: Optional[str] = rest_field()
    """First-level administrative division."""
    postal_code: Optional[str] = rest_field(name="postalCode")
    """Postal code used for mail sorting."""
    country_region: Optional[str] = rest_field(name="countryRegion")
    """Country/region."""
    street_address: Optional[str] = rest_field(name="streetAddress")
    """Street-level address, excluding city, state, countryRegion, and postalCode."""
    unit: Optional[str] = rest_field()
    """Apartment or office number."""
    city_district: Optional[str] = rest_field(name="cityDistrict")
    """Districts or boroughs within a city, such as Brooklyn in New York City or City
     of Westminster in London."""
    state_district: Optional[str] = rest_field(name="stateDistrict")
    """Second-level administrative division used in certain locales."""
    suburb: Optional[str] = rest_field()
    """Unofficial neighborhood name, like Chinatown."""
    house: Optional[str] = rest_field()
    """Build name, such as World Trade Center."""
    level: Optional[str] = rest_field()
    """Floor number, such as 3F."""

    @overload
    def __init__(
        self,
        *,
        house_number: Optional[str] = None,
        po_box: Optional[str] = None,
        road: Optional[str] = None,
        city: Optional[str] = None,
        state: Optional[str] = None,
        postal_code: Optional[str] = None,
        country_region: Optional[str] = None,
        street_address: Optional[str] = None,
        unit: Optional[str] = None,
        city_district: Optional[str] = None,
        state_district: Optional[str] = None,
        suburb: Optional[str] = None,
        house: Optional[str] = None,
        level: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AnalyzeBatchDocumentsRequest(_model_base.Model):
    """Batch document analysis parameters.

    All required parameters must be populated in order to send to server.

    :ivar azure_blob_source: Azure Blob Storage location containing the batch documents.  Either
     azureBlobSource or azureBlobFileListSource must be specified.
    :vartype azure_blob_source: ~azure.ai.documentintelligence.models.AzureBlobContentSource
    :ivar azure_blob_file_list_source: Azure Blob Storage file list specifying the batch documents.
     Either
     azureBlobSource or azureBlobFileListSource must be specified.
    :vartype azure_blob_file_list_source:
     ~azure.ai.documentintelligence.models.AzureBlobFileListContentSource
    :ivar result_container_url: Azure Blob Storage container URL where analyze result files will be
     stored. Required.
    :vartype result_container_url: str
    :ivar result_prefix: Blob name prefix of result files.
    :vartype result_prefix: str
    :ivar overwrite_existing: Overwrite existing analyze result files?.
    :vartype overwrite_existing: bool
    """

    azure_blob_source: Optional["_models.AzureBlobContentSource"] = rest_field(name="azureBlobSource")
    """Azure Blob Storage location containing the batch documents.  Either
     azureBlobSource or azureBlobFileListSource must be specified."""
    azure_blob_file_list_source: Optional["_models.AzureBlobFileListContentSource"] = rest_field(
        name="azureBlobFileListSource"
    )
    """Azure Blob Storage file list specifying the batch documents.  Either
     azureBlobSource or azureBlobFileListSource must be specified."""
    result_container_url: str = rest_field(name="resultContainerUrl")
    """Azure Blob Storage container URL where analyze result files will be stored. Required."""
    result_prefix: Optional[str] = rest_field(name="resultPrefix")
    """Blob name prefix of result files."""
    overwrite_existing: Optional[bool] = rest_field(name="overwriteExisting")
    """Overwrite existing analyze result files?."""

    @overload
    def __init__(
        self,
        *,
        result_container_url: str,
        azure_blob_source: Optional["_models.AzureBlobContentSource"] = None,
        azure_blob_file_list_source: Optional["_models.AzureBlobFileListContentSource"] = None,
        result_prefix: Optional[str] = None,
        overwrite_existing: Optional[bool] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AnalyzeBatchOperation(_model_base.Model):
    """Status and result of the analyze batch operation.


    :ivar result_id: Analyze batch operation result ID.
    :vartype result_id: str
    :ivar status: Operation status.  notStarted, running, succeeded, or failed. Required. Known
     values are: "notStarted", "running", "failed", "succeeded", "canceled", and "skipped".
    :vartype status: str or
     ~azure.ai.documentintelligence.models.DocumentIntelligenceOperationStatus
    :ivar created_date_time: Date and time (UTC) when the operation was submitted. Required.
    :vartype created_date_time: ~datetime.datetime
    :ivar last_updated_date_time: Date and time (UTC) when the status was last updated. Required.
    :vartype last_updated_date_time: ~datetime.datetime
    :ivar percent_completed: Operation progress (0-100).
    :vartype percent_completed: int
    :ivar error: Encountered error during batch document analysis.
    :vartype error: ~azure.ai.documentintelligence.models.DocumentIntelligenceError
    :ivar result: Batch document analysis result.
    :vartype result: ~azure.ai.documentintelligence.models.AnalyzeBatchResult
    """

    result_id: Optional[str] = rest_field(name="resultId")
    """Analyze batch operation result ID."""
    status: Union[str, "_models.DocumentIntelligenceOperationStatus"] = rest_field()
    """Operation status.  notStarted, running, succeeded, or failed. Required. Known values are:
     \"notStarted\", \"running\", \"failed\", \"succeeded\", \"canceled\", and \"skipped\"."""
    created_date_time: datetime.datetime = rest_field(name="createdDateTime", format="rfc3339")
    """Date and time (UTC) when the operation was submitted. Required."""
    last_updated_date_time: datetime.datetime = rest_field(name="lastUpdatedDateTime", format="rfc3339")
    """Date and time (UTC) when the status was last updated. Required."""
    percent_completed: Optional[int] = rest_field(name="percentCompleted")
    """Operation progress (0-100)."""
    error: Optional["_models.DocumentIntelligenceError"] = rest_field()
    """Encountered error during batch document analysis."""
    result: Optional["_models.AnalyzeBatchResult"] = rest_field()
    """Batch document analysis result."""

    @overload
    def __init__(
        self,
        *,
        status: Union[str, "_models.DocumentIntelligenceOperationStatus"],
        created_date_time: datetime.datetime,
        last_updated_date_time: datetime.datetime,
        result_id: Optional[str] = None,
        percent_completed: Optional[int] = None,
        error: Optional["_models.DocumentIntelligenceError"] = None,
        result: Optional["_models.AnalyzeBatchResult"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AnalyzeBatchOperationDetail(_model_base.Model):
    """Operation detail for a document in a batch analysis.


    :ivar status: Analyze status.  succeeded, failed, or skipped. Required. Known values are:
     "notStarted", "running", "failed", "succeeded", "canceled", and "skipped".
    :vartype status: str or
     ~azure.ai.documentintelligence.models.DocumentIntelligenceOperationStatus
    :ivar source_url: URL of the source document. Required.
    :vartype source_url: str
    :ivar result_url: URL of the analyze result JSON.
    :vartype result_url: str
    :ivar error: Encountered error.
    :vartype error: ~azure.ai.documentintelligence.models.DocumentIntelligenceError
    """

    status: Union[str, "_models.DocumentIntelligenceOperationStatus"] = rest_field()
    """Analyze status.  succeeded, failed, or skipped. Required. Known values are: \"notStarted\",
     \"running\", \"failed\", \"succeeded\", \"canceled\", and \"skipped\"."""
    source_url: str = rest_field(name="sourceUrl")
    """URL of the source document. Required."""
    result_url: Optional[str] = rest_field(name="resultUrl")
    """URL of the analyze result JSON."""
    error: Optional["_models.DocumentIntelligenceError"] = rest_field()
    """Encountered error."""

    @overload
    def __init__(
        self,
        *,
        status: Union[str, "_models.DocumentIntelligenceOperationStatus"],
        source_url: str,
        result_url: Optional[str] = None,
        error: Optional["_models.DocumentIntelligenceError"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AnalyzeBatchResult(_model_base.Model):
    """Batch document analysis result.


    :ivar succeeded_count: Number of documents that completed with status succeeded. Required.
    :vartype succeeded_count: int
    :ivar failed_count: Number of documents that completed with status failed. Required.
    :vartype failed_count: int
    :ivar skipped_count: Number of documents that completed with status skipped. Required.
    :vartype skipped_count: int
    :ivar details: Operation detail for each document in the batch.
    :vartype details: list[~azure.ai.documentintelligence.models.AnalyzeBatchOperationDetail]
    """

    succeeded_count: int = rest_field(name="succeededCount")
    """Number of documents that completed with status succeeded. Required."""
    failed_count: int = rest_field(name="failedCount")
    """Number of documents that completed with status failed. Required."""
    skipped_count: int = rest_field(name="skippedCount")
    """Number of documents that completed with status skipped. Required."""
    details: Optional[List["_models.AnalyzeBatchOperationDetail"]] = rest_field()
    """Operation detail for each document in the batch."""

    @overload
    def __init__(
        self,
        *,
        succeeded_count: int,
        failed_count: int,
        skipped_count: int,
        details: Optional[List["_models.AnalyzeBatchOperationDetail"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AnalyzedDocument(_model_base.Model):
    """An object describing the location and semantic content of a document.


    :ivar doc_type: Document type. Required.
    :vartype doc_type: str
    :ivar bounding_regions: Bounding regions covering the document.
    :vartype bounding_regions: list[~azure.ai.documentintelligence.models.BoundingRegion]
    :ivar spans: Location of the document in the reading order concatenated content. Required.
    :vartype spans: list[~azure.ai.documentintelligence.models.DocumentSpan]
    :ivar fields: Dictionary of named field values.
    :vartype fields: dict[str, ~azure.ai.documentintelligence.models.DocumentField]
    :ivar confidence: Confidence of correctly extracting the document. Required.
    :vartype confidence: float
    """

    doc_type: str = rest_field(name="docType")
    """Document type. Required."""
    bounding_regions: Optional[List["_models.BoundingRegion"]] = rest_field(name="boundingRegions")
    """Bounding regions covering the document."""
    spans: List["_models.DocumentSpan"] = rest_field()
    """Location of the document in the reading order concatenated content. Required."""
    fields: Optional[Dict[str, "_models.DocumentField"]] = rest_field()
    """Dictionary of named field values."""
    confidence: float = rest_field()
    """Confidence of correctly extracting the document. Required."""

    @overload
    def __init__(
        self,
        *,
        doc_type: str,
        spans: List["_models.DocumentSpan"],
        confidence: float,
        bounding_regions: Optional[List["_models.BoundingRegion"]] = None,
        fields: Optional[Dict[str, "_models.DocumentField"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AnalyzeDocumentRequest(_model_base.Model):
    """Document analysis parameters.

    :ivar url_source: Document URL to analyze.  Either urlSource or base64Source must be specified.
    :vartype url_source: str
    :ivar bytes_source: Base64 encoding of the document to analyze.  Either urlSource or
     base64Source
     must be specified.
    :vartype bytes_source: bytes
    """

    url_source: Optional[str] = rest_field(name="urlSource")
    """Document URL to analyze.  Either urlSource or base64Source must be specified."""
    bytes_source: Optional[bytes] = rest_field(name="base64Source", format="base64")
    """Base64 encoding of the document to analyze.  Either urlSource or base64Source
     must be specified."""

    @overload
    def __init__(
        self,
        *,
        url_source: Optional[str] = None,
        bytes_source: Optional[bytes] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AnalyzeResult(_model_base.Model):
    """Document analysis result.


    :ivar api_version: API version used to produce this result. Required.
    :vartype api_version: str
    :ivar model_id: Document model ID used to produce this result. Required.
    :vartype model_id: str
    :ivar string_index_type: Method used to compute string offset and length. Required. Known
     values are: "textElements", "unicodeCodePoint", and "utf16CodeUnit".
    :vartype string_index_type: str or ~azure.ai.documentintelligence.models.StringIndexType
    :ivar content_format: Format of the analyze result top-level content. Known values are: "text"
     and "markdown".
    :vartype content_format: str or ~azure.ai.documentintelligence.models.DocumentContentFormat
    :ivar content: Concatenate string representation of all textual and visual elements in reading
     order. Required.
    :vartype content: str
    :ivar pages: Analyzed pages. Required.
    :vartype pages: list[~azure.ai.documentintelligence.models.DocumentPage]
    :ivar paragraphs: Extracted paragraphs.
    :vartype paragraphs: list[~azure.ai.documentintelligence.models.DocumentParagraph]
    :ivar tables: Extracted tables.
    :vartype tables: list[~azure.ai.documentintelligence.models.DocumentTable]
    :ivar figures: Extracted figures.
    :vartype figures: list[~azure.ai.documentintelligence.models.DocumentFigure]
    :ivar sections: Extracted sections.
    :vartype sections: list[~azure.ai.documentintelligence.models.DocumentSection]
    :ivar key_value_pairs: Extracted key-value pairs.
    :vartype key_value_pairs: list[~azure.ai.documentintelligence.models.DocumentKeyValuePair]
    :ivar styles: Extracted font styles.
    :vartype styles: list[~azure.ai.documentintelligence.models.DocumentStyle]
    :ivar languages: Detected languages.
    :vartype languages: list[~azure.ai.documentintelligence.models.DocumentLanguage]
    :ivar documents: Extracted documents.
    :vartype documents: list[~azure.ai.documentintelligence.models.AnalyzedDocument]
    :ivar warnings: List of warnings encountered.
    :vartype warnings: list[~azure.ai.documentintelligence.models.DocumentIntelligenceWarning]
    """

    api_version: str = rest_field(name="apiVersion")
    """API version used to produce this result. Required."""
    model_id: str = rest_field(name="modelId")
    """Document model ID used to produce this result. Required."""
    string_index_type: Union[str, "_models.StringIndexType"] = rest_field(name="stringIndexType")
    """Method used to compute string offset and length. Required. Known values are: \"textElements\",
     \"unicodeCodePoint\", and \"utf16CodeUnit\"."""
    content_format: Optional[Union[str, "_models.DocumentContentFormat"]] = rest_field(name="contentFormat")
    """Format of the analyze result top-level content. Known values are: \"text\" and \"markdown\"."""
    content: str = rest_field()
    """Concatenate string representation of all textual and visual elements in reading
     order. Required."""
    pages: List["_models.DocumentPage"] = rest_field()
    """Analyzed pages. Required."""
    paragraphs: Optional[List["_models.DocumentParagraph"]] = rest_field()
    """Extracted paragraphs."""
    tables: Optional[List["_models.DocumentTable"]] = rest_field()
    """Extracted tables."""
    figures: Optional[List["_models.DocumentFigure"]] = rest_field()
    """Extracted figures."""
    sections: Optional[List["_models.DocumentSection"]] = rest_field()
    """Extracted sections."""
    key_value_pairs: Optional[List["_models.DocumentKeyValuePair"]] = rest_field(name="keyValuePairs")
    """Extracted key-value pairs."""
    styles: Optional[List["_models.DocumentStyle"]] = rest_field()
    """Extracted font styles."""
    languages: Optional[List["_models.DocumentLanguage"]] = rest_field()
    """Detected languages."""
    documents: Optional[List["_models.AnalyzedDocument"]] = rest_field()
    """Extracted documents."""
    warnings: Optional[List["_models.DocumentIntelligenceWarning"]] = rest_field()
    """List of warnings encountered."""

    @overload
    def __init__(
        self,
        *,
        api_version: str,
        model_id: str,
        string_index_type: Union[str, "_models.StringIndexType"],
        content: str,
        pages: List["_models.DocumentPage"],
        content_format: Optional[Union[str, "_models.DocumentContentFormat"]] = None,
        paragraphs: Optional[List["_models.DocumentParagraph"]] = None,
        tables: Optional[List["_models.DocumentTable"]] = None,
        figures: Optional[List["_models.DocumentFigure"]] = None,
        sections: Optional[List["_models.DocumentSection"]] = None,
        key_value_pairs: Optional[List["_models.DocumentKeyValuePair"]] = None,
        styles: Optional[List["_models.DocumentStyle"]] = None,
        languages: Optional[List["_models.DocumentLanguage"]] = None,
        documents: Optional[List["_models.AnalyzedDocument"]] = None,
        warnings: Optional[List["_models.DocumentIntelligenceWarning"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AuthorizeClassifierCopyRequest(_model_base.Model):
    """Request body to authorize document classifier copy.

    All required parameters must be populated in order to send to server.

    :ivar classifier_id: Unique document classifier name. Required.
    :vartype classifier_id: str
    :ivar description: Document classifier description.
    :vartype description: str
    :ivar tags: List of key-value tag attributes associated with the document classifier.
    :vartype tags: dict[str, str]
    """

    classifier_id: str = rest_field(name="classifierId")
    """Unique document classifier name. Required."""
    description: Optional[str] = rest_field()
    """Document classifier description."""
    tags: Optional[Dict[str, str]] = rest_field()
    """List of key-value tag attributes associated with the document classifier."""

    @overload
    def __init__(
        self,
        *,
        classifier_id: str,
        description: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AuthorizeCopyRequest(_model_base.Model):
    """Request body to authorize document model copy.

    All required parameters must be populated in order to send to server.

    :ivar model_id: Unique document model name. Required.
    :vartype model_id: str
    :ivar description: Document model description.
    :vartype description: str
    :ivar tags: List of key-value tag attributes associated with the document model.
    :vartype tags: dict[str, str]
    """

    model_id: str = rest_field(name="modelId")
    """Unique document model name. Required."""
    description: Optional[str] = rest_field()
    """Document model description."""
    tags: Optional[Dict[str, str]] = rest_field()
    """List of key-value tag attributes associated with the document model."""

    @overload
    def __init__(
        self,
        *,
        model_id: str,
        description: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AzureBlobContentSource(_model_base.Model):
    """Azure Blob Storage content.


    :ivar container_url: Azure Blob Storage container URL. Required.
    :vartype container_url: str
    :ivar prefix: Blob name prefix.
    :vartype prefix: str
    """

    container_url: str = rest_field(name="containerUrl")
    """Azure Blob Storage container URL. Required."""
    prefix: Optional[str] = rest_field()
    """Blob name prefix."""

    @overload
    def __init__(
        self,
        *,
        container_url: str,
        prefix: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class AzureBlobFileListContentSource(_model_base.Model):
    """File list in Azure Blob Storage.


    :ivar container_url: Azure Blob Storage container URL. Required.
    :vartype container_url: str
    :ivar file_list: Path to a JSONL file within the container specifying a subset of documents.
     Required.
    :vartype file_list: str
    """

    container_url: str = rest_field(name="containerUrl")
    """Azure Blob Storage container URL. Required."""
    file_list: str = rest_field(name="fileList")
    """Path to a JSONL file within the container specifying a subset of documents. Required."""

    @overload
    def __init__(
        self,
        *,
        container_url: str,
        file_list: str,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class BoundingRegion(_model_base.Model):
    """Bounding polygon on a specific page of the input.


    :ivar page_number: 1-based page number of page containing the bounding region. Required.
    :vartype page_number: int
    :ivar polygon: Bounding polygon on the page, or the entire page if not specified.
     Coordinates specified relative to the top-left of the page. The numbers
     represent the x, y values of the polygon vertices, clockwise from the left
     (-180 degrees inclusive) relative to the element orientation. Required.
    :vartype polygon: list[float]
    """

    page_number: int = rest_field(name="pageNumber")
    """1-based page number of page containing the bounding region. Required."""
    polygon: List[float] = rest_field()
    """Bounding polygon on the page, or the entire page if not specified.
     Coordinates specified relative to the top-left of the page. The numbers
     represent the x, y values of the polygon vertices, clockwise from the left
     (-180 degrees inclusive) relative to the element orientation. Required."""

    @overload
    def __init__(
        self,
        *,
        page_number: int,
        polygon: List[float],
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class BuildDocumentClassifierRequest(_model_base.Model):
    """Request body to build a new custom document classifier.

    All required parameters must be populated in order to send to server.

    :ivar classifier_id: Unique document classifier name. Required.
    :vartype classifier_id: str
    :ivar description: Document classifier description.
    :vartype description: str
    :ivar base_classifier_id: Base classifierId on top of which to train the classifier.
    :vartype base_classifier_id: str
    :ivar doc_types: List of document types to classify against. Required.
    :vartype doc_types: dict[str,
     ~azure.ai.documentintelligence.models.ClassifierDocumentTypeDetails]
    :ivar allow_overwrite: Allow overwriting an existing classifier with the same name.
    :vartype allow_overwrite: bool
    """

    classifier_id: str = rest_field(name="classifierId")
    """Unique document classifier name. Required."""
    description: Optional[str] = rest_field()
    """Document classifier description."""
    base_classifier_id: Optional[str] = rest_field(name="baseClassifierId")
    """Base classifierId on top of which to train the classifier."""
    doc_types: Dict[str, "_models.ClassifierDocumentTypeDetails"] = rest_field(name="docTypes")
    """List of document types to classify against. Required."""
    allow_overwrite: Optional[bool] = rest_field(name="allowOverwrite")
    """Allow overwriting an existing classifier with the same name."""

    @overload
    def __init__(
        self,
        *,
        classifier_id: str,
        doc_types: Dict[str, "_models.ClassifierDocumentTypeDetails"],
        description: Optional[str] = None,
        base_classifier_id: Optional[str] = None,
        allow_overwrite: Optional[bool] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class BuildDocumentModelRequest(_model_base.Model):
    """Request body to build a new custom document model.

    All required parameters must be populated in order to send to server.

    :ivar model_id: Unique document model name. Required.
    :vartype model_id: str
    :ivar description: Document model description.
    :vartype description: str
    :ivar build_mode: Custom document model build mode. Required. Known values are: "template" and
     "neural".
    :vartype build_mode: str or ~azure.ai.documentintelligence.models.DocumentBuildMode
    :ivar azure_blob_source: Azure Blob Storage location containing the training data.  Either
     azureBlobSource or azureBlobFileListSource must be specified.
    :vartype azure_blob_source: ~azure.ai.documentintelligence.models.AzureBlobContentSource
    :ivar azure_blob_file_list_source: Azure Blob Storage file list specifying the training data.
     Either
     azureBlobSource or azureBlobFileListSource must be specified.
    :vartype azure_blob_file_list_source:
     ~azure.ai.documentintelligence.models.AzureBlobFileListContentSource
    :ivar tags: List of key-value tag attributes associated with the document model.
    :vartype tags: dict[str, str]
    :ivar max_training_hours: Max number of V100-equivalent GPU hours to use for model training.
     Default=0.5.
    :vartype max_training_hours: float
    :ivar allow_overwrite: Allow overwriting an existing model with the same name.
    :vartype allow_overwrite: bool
    """

    model_id: str = rest_field(name="modelId")
    """Unique document model name. Required."""
    description: Optional[str] = rest_field()
    """Document model description."""
    build_mode: Union[str, "_models.DocumentBuildMode"] = rest_field(name="buildMode")
    """Custom document model build mode. Required. Known values are: \"template\" and \"neural\"."""
    azure_blob_source: Optional["_models.AzureBlobContentSource"] = rest_field(name="azureBlobSource")
    """Azure Blob Storage location containing the training data.  Either
     azureBlobSource or azureBlobFileListSource must be specified."""
    azure_blob_file_list_source: Optional["_models.AzureBlobFileListContentSource"] = rest_field(
        name="azureBlobFileListSource"
    )
    """Azure Blob Storage file list specifying the training data.  Either
     azureBlobSource or azureBlobFileListSource must be specified."""
    tags: Optional[Dict[str, str]] = rest_field()
    """List of key-value tag attributes associated with the document model."""
    max_training_hours: Optional[float] = rest_field(name="maxTrainingHours")
    """Max number of V100-equivalent GPU hours to use for model training.  Default=0.5."""
    allow_overwrite: Optional[bool] = rest_field(name="allowOverwrite")
    """Allow overwriting an existing model with the same name."""

    @overload
    def __init__(
        self,
        *,
        model_id: str,
        build_mode: Union[str, "_models.DocumentBuildMode"],
        description: Optional[str] = None,
        azure_blob_source: Optional["_models.AzureBlobContentSource"] = None,
        azure_blob_file_list_source: Optional["_models.AzureBlobFileListContentSource"] = None,
        tags: Optional[Dict[str, str]] = None,
        max_training_hours: Optional[float] = None,
        allow_overwrite: Optional[bool] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class ClassifierCopyAuthorization(_model_base.Model):
    """Authorization to copy a document classifier to the specified target resource and
    classifierId.


    :ivar target_resource_id: ID of the target Azure resource where the document classifier should
     be copied to. Required.
    :vartype target_resource_id: str
    :ivar target_resource_region: Location of the target Azure resource where the document
     classifier should be copied
     to. Required.
    :vartype target_resource_region: str
    :ivar target_classifier_id: Identifier of the target document classifier. Required.
    :vartype target_classifier_id: str
    :ivar target_classifier_location: URL of the copied document classifier in the target account.
     Required.
    :vartype target_classifier_location: str
    :ivar access_token: Token used to authorize the request. Required.
    :vartype access_token: str
    :ivar expiration_date_time: Date/time when the access token expires. Required.
    :vartype expiration_date_time: ~datetime.datetime
    """

    target_resource_id: str = rest_field(name="targetResourceId")
    """ID of the target Azure resource where the document classifier should be copied to. Required."""
    target_resource_region: str = rest_field(name="targetResourceRegion")
    """Location of the target Azure resource where the document classifier should be copied
     to. Required."""
    target_classifier_id: str = rest_field(name="targetClassifierId")
    """Identifier of the target document classifier. Required."""
    target_classifier_location: str = rest_field(name="targetClassifierLocation")
    """URL of the copied document classifier in the target account. Required."""
    access_token: str = rest_field(name="accessToken")
    """Token used to authorize the request. Required."""
    expiration_date_time: datetime.datetime = rest_field(name="expirationDateTime", format="rfc3339")
    """Date/time when the access token expires. Required."""

    @overload
    def __init__(
        self,
        *,
        target_resource_id: str,
        target_resource_region: str,
        target_classifier_id: str,
        target_classifier_location: str,
        access_token: str,
        expiration_date_time: datetime.datetime,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class ClassifierDocumentTypeDetails(_model_base.Model):
    """Classifier document type info.

    :ivar source_kind: Type of training data source. Known values are: "url", "base64",
     "azureBlob", and "azureBlobFileList".
    :vartype source_kind: str or ~azure.ai.documentintelligence.models.ContentSourceKind
    :ivar azure_blob_source: Azure Blob Storage location containing the training data for a
     classifier
     document type.  Either azureBlobSource or azureBlobFileListSource must be
     specified.
    :vartype azure_blob_source: ~azure.ai.documentintelligence.models.AzureBlobContentSource
    :ivar azure_blob_file_list_source: Azure Blob Storage file list specifying the training data
     for a classifier
     document type.  Either azureBlobSource or azureBlobFileListSource must be
     specified.
    :vartype azure_blob_file_list_source:
     ~azure.ai.documentintelligence.models.AzureBlobFileListContentSource
    """

    source_kind: Optional[Union[str, "_models.ContentSourceKind"]] = rest_field(name="sourceKind")
    """Type of training data source. Known values are: \"url\", \"base64\", \"azureBlob\", and
     \"azureBlobFileList\"."""
    azure_blob_source: Optional["_models.AzureBlobContentSource"] = rest_field(name="azureBlobSource")
    """Azure Blob Storage location containing the training data for a classifier
     document type.  Either azureBlobSource or azureBlobFileListSource must be
     specified."""
    azure_blob_file_list_source: Optional["_models.AzureBlobFileListContentSource"] = rest_field(
        name="azureBlobFileListSource"
    )
    """Azure Blob Storage file list specifying the training data for a classifier
     document type.  Either azureBlobSource or azureBlobFileListSource must be
     specified."""

    @overload
    def __init__(
        self,
        *,
        source_kind: Optional[Union[str, "_models.ContentSourceKind"]] = None,
        azure_blob_source: Optional["_models.AzureBlobContentSource"] = None,
        azure_blob_file_list_source: Optional["_models.AzureBlobFileListContentSource"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class ClassifyDocumentRequest(_model_base.Model):
    """Document classification parameters.

    :ivar url_source: Document URL to classify.  Either urlSource or base64Source must be
     specified.
    :vartype url_source: str
    :ivar bytes_source: Base64 encoding of the document to classify.  Either urlSource or
     base64Source
     must be specified.
    :vartype bytes_source: bytes
    """

    url_source: Optional[str] = rest_field(name="urlSource")
    """Document URL to classify.  Either urlSource or base64Source must be specified."""
    bytes_source: Optional[bytes] = rest_field(name="base64Source", format="base64")
    """Base64 encoding of the document to classify.  Either urlSource or base64Source
     must be specified."""

    @overload
    def __init__(
        self,
        *,
        url_source: Optional[str] = None,
        bytes_source: Optional[bytes] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class ComposeDocumentModelRequest(_model_base.Model):
    """Request body to create a composed document model from component document models.

    All required parameters must be populated in order to send to server.

    :ivar model_id: Unique document model name. Required.
    :vartype model_id: str
    :ivar description: Document model description.
    :vartype description: str
    :ivar classifier_id: Custom classifier to split and classify the input file. Required.
    :vartype classifier_id: str
    :ivar split: File splitting behavior. Known values are: "auto", "none", and "perPage".
    :vartype split: str or ~azure.ai.documentintelligence.models.SplitMode
    :ivar doc_types: Dictionary mapping supported docTypes to the corresponding document models.
     Required.
    :vartype doc_types: dict[str, ~azure.ai.documentintelligence.models.DocumentTypeDetails]
    :ivar tags: List of key-value tag attributes associated with the document model.
    :vartype tags: dict[str, str]
    """

    model_id: str = rest_field(name="modelId")
    """Unique document model name. Required."""
    description: Optional[str] = rest_field()
    """Document model description."""
    classifier_id: str = rest_field(name="classifierId")
    """Custom classifier to split and classify the input file. Required."""
    split: Optional[Union[str, "_models.SplitMode"]] = rest_field()
    """File splitting behavior. Known values are: \"auto\", \"none\", and \"perPage\"."""
    doc_types: Dict[str, "_models.DocumentTypeDetails"] = rest_field(name="docTypes")
    """Dictionary mapping supported docTypes to the corresponding document models. Required."""
    tags: Optional[Dict[str, str]] = rest_field()
    """List of key-value tag attributes associated with the document model."""

    @overload
    def __init__(
        self,
        *,
        model_id: str,
        classifier_id: str,
        doc_types: Dict[str, "_models.DocumentTypeDetails"],
        description: Optional[str] = None,
        split: Optional[Union[str, "_models.SplitMode"]] = None,
        tags: Optional[Dict[str, str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class CurrencyValue(_model_base.Model):
    """Currency field value.


    :ivar amount: Currency amount. Required.
    :vartype amount: float
    :ivar currency_symbol: Currency symbol label, if any.
    :vartype currency_symbol: str
    :ivar currency_code: Resolved currency code (ISO 4217), if any.
    :vartype currency_code: str
    """

    amount: float = rest_field()
    """Currency amount. Required."""
    currency_symbol: Optional[str] = rest_field(name="currencySymbol")
    """Currency symbol label, if any."""
    currency_code: Optional[str] = rest_field(name="currencyCode")
    """Resolved currency code (ISO 4217), if any."""

    @overload
    def __init__(
        self,
        *,
        amount: float,
        currency_symbol: Optional[str] = None,
        currency_code: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class CustomDocumentModelsDetails(_model_base.Model):
    """Details regarding custom document models.


    :ivar count: Number of custom document models in the current resource. Required.
    :vartype count: int
    :ivar limit: Maximum number of custom document models supported in the current resource.
     Required.
    :vartype limit: int
    """

    count: int = rest_field()
    """Number of custom document models in the current resource. Required."""
    limit: int = rest_field()
    """Maximum number of custom document models supported in the current resource. Required."""

    @overload
    def __init__(
        self,
        *,
        count: int,
        limit: int,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentBarcode(_model_base.Model):
    """A barcode object.


    :ivar kind: Barcode kind. Required. Known values are: "QRCode", "PDF417", "UPCA", "UPCE",
     "Code39", "Code128", "EAN8", "EAN13", "DataBar", "Code93", "Codabar", "DataBarExpanded", "ITF",
     "MicroQRCode", "Aztec", "DataMatrix", and "MaxiCode".
    :vartype kind: str or ~azure.ai.documentintelligence.models.DocumentBarcodeKind
    :ivar value: Barcode value. Required.
    :vartype value: str
    :ivar polygon: Bounding polygon of the barcode, with coordinates specified relative to the
     top-left of the page. The numbers represent the x, y values of the polygon
     vertices, clockwise from the left (-180 degrees inclusive) relative to the
     element orientation.
    :vartype polygon: list[float]
    :ivar span: Location of the barcode in the reading order concatenated content. Required.
    :vartype span: ~azure.ai.documentintelligence.models.DocumentSpan
    :ivar confidence: Confidence of correctly extracting the barcode. Required.
    :vartype confidence: float
    """

    kind: Union[str, "_models.DocumentBarcodeKind"] = rest_field()
    """Barcode kind. Required. Known values are: \"QRCode\", \"PDF417\", \"UPCA\", \"UPCE\",
     \"Code39\", \"Code128\", \"EAN8\", \"EAN13\", \"DataBar\", \"Code93\", \"Codabar\",
     \"DataBarExpanded\", \"ITF\", \"MicroQRCode\", \"Aztec\", \"DataMatrix\", and \"MaxiCode\"."""
    value: str = rest_field()
    """Barcode value. Required."""
    polygon: Optional[List[float]] = rest_field()
    """Bounding polygon of the barcode, with coordinates specified relative to the
     top-left of the page. The numbers represent the x, y values of the polygon
     vertices, clockwise from the left (-180 degrees inclusive) relative to the
     element orientation."""
    span: "_models.DocumentSpan" = rest_field()
    """Location of the barcode in the reading order concatenated content. Required."""
    confidence: float = rest_field()
    """Confidence of correctly extracting the barcode. Required."""

    @overload
    def __init__(
        self,
        *,
        kind: Union[str, "_models.DocumentBarcodeKind"],
        value: str,
        span: "_models.DocumentSpan",
        confidence: float,
        polygon: Optional[List[float]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentCaption(_model_base.Model):
    """A caption object describing a table or figure.


    :ivar content: Content of the caption. Required.
    :vartype content: str
    :ivar bounding_regions: Bounding regions covering the caption.
    :vartype bounding_regions: list[~azure.ai.documentintelligence.models.BoundingRegion]
    :ivar spans: Location of the caption in the reading order concatenated content. Required.
    :vartype spans: list[~azure.ai.documentintelligence.models.DocumentSpan]
    :ivar elements: Child elements of the caption.
    :vartype elements: list[str]
    """

    content: str = rest_field()
    """Content of the caption. Required."""
    bounding_regions: Optional[List["_models.BoundingRegion"]] = rest_field(name="boundingRegions")
    """Bounding regions covering the caption."""
    spans: List["_models.DocumentSpan"] = rest_field()
    """Location of the caption in the reading order concatenated content. Required."""
    elements: Optional[List[str]] = rest_field()
    """Child elements of the caption."""

    @overload
    def __init__(
        self,
        *,
        content: str,
        spans: List["_models.DocumentSpan"],
        bounding_regions: Optional[List["_models.BoundingRegion"]] = None,
        elements: Optional[List[str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentIntelligenceOperationDetails(_model_base.Model):
    """Operation info.

    You probably want to use the sub-classes and not this class directly. Known sub-classes are:
    DocumentClassifierBuildOperationDetails, DocumentClassifierCopyToOperationDetails,
    DocumentModelBuildOperationDetails, DocumentModelComposeOperationDetails,
    DocumentModelCopyToOperationDetails


    :ivar operation_id: Operation ID. Required.
    :vartype operation_id: str
    :ivar status: Operation status.  notStarted, running, completed, or failed. Required. Known
     values are: "notStarted", "running", "failed", "succeeded", "canceled", and "skipped".
    :vartype status: str or
     ~azure.ai.documentintelligence.models.DocumentIntelligenceOperationStatus
    :ivar percent_completed: Operation progress (0-100).
    :vartype percent_completed: int
    :ivar created_date_time: Date and time (UTC) when the operation was created. Required.
    :vartype created_date_time: ~datetime.datetime
    :ivar last_updated_date_time: Date and time (UTC) when the status was last updated. Required.
    :vartype last_updated_date_time: ~datetime.datetime
    :ivar kind: Type of operation. Required. Known values are: "documentModelBuild",
     "documentModelCompose", "documentModelCopyTo", "documentClassifierCopyTo", and
     "documentClassifierBuild".
    :vartype kind: str or ~azure.ai.documentintelligence.models.OperationKind
    :ivar resource_location: URL of the resource targeted by this operation. Required.
    :vartype resource_location: str
    :ivar api_version: API version used to create this operation.
    :vartype api_version: str
    :ivar tags: List of key-value tag attributes associated with the document model.
    :vartype tags: dict[str, str]
    :ivar error: Encountered error.
    :vartype error: ~azure.ai.documentintelligence.models.DocumentIntelligenceError
    """

    __mapping__: Dict[str, _model_base.Model] = {}
    operation_id: str = rest_field(name="operationId", visibility=["read", "create"])
    """Operation ID. Required."""
    status: Union[str, "_models.DocumentIntelligenceOperationStatus"] = rest_field()
    """Operation status.  notStarted, running, completed, or failed. Required. Known values are:
     \"notStarted\", \"running\", \"failed\", \"succeeded\", \"canceled\", and \"skipped\"."""
    percent_completed: Optional[int] = rest_field(name="percentCompleted")
    """Operation progress (0-100)."""
    created_date_time: datetime.datetime = rest_field(name="createdDateTime", format="rfc3339")
    """Date and time (UTC) when the operation was created. Required."""
    last_updated_date_time: datetime.datetime = rest_field(name="lastUpdatedDateTime", format="rfc3339")
    """Date and time (UTC) when the status was last updated. Required."""
    kind: str = rest_discriminator(name="kind")
    """Type of operation. Required. Known values are: \"documentModelBuild\",
     \"documentModelCompose\", \"documentModelCopyTo\", \"documentClassifierCopyTo\", and
     \"documentClassifierBuild\"."""
    resource_location: str = rest_field(name="resourceLocation")
    """URL of the resource targeted by this operation. Required."""
    api_version: Optional[str] = rest_field(name="apiVersion")
    """API version used to create this operation."""
    tags: Optional[Dict[str, str]] = rest_field()
    """List of key-value tag attributes associated with the document model."""
    error: Optional["_models.DocumentIntelligenceError"] = rest_field()
    """Encountered error."""

    @overload
    def __init__(
        self,
        *,
        operation_id: str,
        status: Union[str, "_models.DocumentIntelligenceOperationStatus"],
        created_date_time: datetime.datetime,
        last_updated_date_time: datetime.datetime,
        kind: str,
        resource_location: str,
        percent_completed: Optional[int] = None,
        api_version: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
        error: Optional["_models.DocumentIntelligenceError"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentClassifierBuildOperationDetails(
    DocumentIntelligenceOperationDetails, discriminator="documentClassifierBuild"
):
    """Get Operation response object.


    :ivar operation_id: Operation ID. Required.
    :vartype operation_id: str
    :ivar status: Operation status.  notStarted, running, completed, or failed. Required. Known
     values are: "notStarted", "running", "failed", "succeeded", "canceled", and "skipped".
    :vartype status: str or
     ~azure.ai.documentintelligence.models.DocumentIntelligenceOperationStatus
    :ivar percent_completed: Operation progress (0-100).
    :vartype percent_completed: int
    :ivar created_date_time: Date and time (UTC) when the operation was created. Required.
    :vartype created_date_time: ~datetime.datetime
    :ivar last_updated_date_time: Date and time (UTC) when the status was last updated. Required.
    :vartype last_updated_date_time: ~datetime.datetime
    :ivar resource_location: URL of the resource targeted by this operation. Required.
    :vartype resource_location: str
    :ivar api_version: API version used to create this operation.
    :vartype api_version: str
    :ivar tags: List of key-value tag attributes associated with the document model.
    :vartype tags: dict[str, str]
    :ivar error: Encountered error.
    :vartype error: ~azure.ai.documentintelligence.models.DocumentIntelligenceError
    :ivar result: Operation result upon success.
    :vartype result: ~azure.ai.documentintelligence.models.DocumentClassifierDetails
    :ivar kind: Type of operation. Required. Build a new custom classifier model.
    :vartype kind: str or ~azure.ai.documentintelligence.models.DOCUMENT_CLASSIFIER_BUILD
    """

    result: Optional["_models.DocumentClassifierDetails"] = rest_field()
    """Operation result upon success."""
    kind: Literal[OperationKind.DOCUMENT_CLASSIFIER_BUILD] = rest_discriminator(name="kind")  # type: ignore
    """Type of operation. Required. Build a new custom classifier model."""

    @overload
    def __init__(
        self,
        *,
        operation_id: str,
        status: Union[str, "_models.DocumentIntelligenceOperationStatus"],
        created_date_time: datetime.datetime,
        last_updated_date_time: datetime.datetime,
        resource_location: str,
        percent_completed: Optional[int] = None,
        api_version: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
        error: Optional["_models.DocumentIntelligenceError"] = None,
        result: Optional["_models.DocumentClassifierDetails"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, kind=OperationKind.DOCUMENT_CLASSIFIER_BUILD, **kwargs)


class DocumentClassifierCopyToOperationDetails(
    DocumentIntelligenceOperationDetails, discriminator="documentClassifierCopyTo"
):
    """Get Operation response object.


    :ivar operation_id: Operation ID. Required.
    :vartype operation_id: str
    :ivar status: Operation status.  notStarted, running, completed, or failed. Required. Known
     values are: "notStarted", "running", "failed", "succeeded", "canceled", and "skipped".
    :vartype status: str or
     ~azure.ai.documentintelligence.models.DocumentIntelligenceOperationStatus
    :ivar percent_completed: Operation progress (0-100).
    :vartype percent_completed: int
    :ivar created_date_time: Date and time (UTC) when the operation was created. Required.
    :vartype created_date_time: ~datetime.datetime
    :ivar last_updated_date_time: Date and time (UTC) when the status was last updated. Required.
    :vartype last_updated_date_time: ~datetime.datetime
    :ivar resource_location: URL of the resource targeted by this operation. Required.
    :vartype resource_location: str
    :ivar api_version: API version used to create this operation.
    :vartype api_version: str
    :ivar tags: List of key-value tag attributes associated with the document model.
    :vartype tags: dict[str, str]
    :ivar error: Encountered error.
    :vartype error: ~azure.ai.documentintelligence.models.DocumentIntelligenceError
    :ivar result: Operation result upon success.
    :vartype result: ~azure.ai.documentintelligence.models.DocumentClassifierDetails
    :ivar kind: Type of operation. Required. Copy an existing document classifier to potentially a
     different resource, region, or
     subscription.
    :vartype kind: str or ~azure.ai.documentintelligence.models.DOCUMENT_CLASSIFIER_COPY_TO
    """

    result: Optional["_models.DocumentClassifierDetails"] = rest_field()
    """Operation result upon success."""
    kind: Literal[OperationKind.DOCUMENT_CLASSIFIER_COPY_TO] = rest_discriminator(name="kind")  # type: ignore
    """Type of operation. Required. Copy an existing document classifier to potentially a different
     resource, region, or
     subscription."""

    @overload
    def __init__(
        self,
        *,
        operation_id: str,
        status: Union[str, "_models.DocumentIntelligenceOperationStatus"],
        created_date_time: datetime.datetime,
        last_updated_date_time: datetime.datetime,
        resource_location: str,
        percent_completed: Optional[int] = None,
        api_version: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
        error: Optional["_models.DocumentIntelligenceError"] = None,
        result: Optional["_models.DocumentClassifierDetails"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, kind=OperationKind.DOCUMENT_CLASSIFIER_COPY_TO, **kwargs)


class DocumentClassifierDetails(_model_base.Model):
    """Document classifier info.

    Readonly variables are only populated by the server, and will be ignored when sending a request.


    :ivar classifier_id: Unique document classifier name. Required.
    :vartype classifier_id: str
    :ivar description: Document classifier description.
    :vartype description: str
    :ivar created_date_time: Date and time (UTC) when the document classifier was created.
     Required.
    :vartype created_date_time: ~datetime.datetime
    :ivar expiration_date_time: Date and time (UTC) when the document classifier will expire.
    :vartype expiration_date_time: ~datetime.datetime
    :ivar modified_date_time: Date and time (UTC) when the document model was last modified.
    :vartype modified_date_time: ~datetime.datetime
    :ivar api_version: API version used to create this document classifier. Required.
    :vartype api_version: str
    :ivar base_classifier_id: Base classifierId on top of which the classifier was trained.
    :vartype base_classifier_id: str
    :ivar doc_types: List of document types to classify against. Required.
    :vartype doc_types: dict[str,
     ~azure.ai.documentintelligence.models.ClassifierDocumentTypeDetails]
    :ivar warnings: List of warnings encountered while building the classifier.
    :vartype warnings: list[~azure.ai.documentintelligence.models.DocumentIntelligenceWarning]
    """

    classifier_id: str = rest_field(name="classifierId", visibility=["read", "create"])
    """Unique document classifier name. Required."""
    description: Optional[str] = rest_field()
    """Document classifier description."""
    created_date_time: datetime.datetime = rest_field(name="createdDateTime", format="rfc3339")
    """Date and time (UTC) when the document classifier was created. Required."""
    expiration_date_time: Optional[datetime.datetime] = rest_field(name="expirationDateTime", format="rfc3339")
    """Date and time (UTC) when the document classifier will expire."""
    modified_date_time: Optional[datetime.datetime] = rest_field(
        name="modifiedDateTime", visibility=["read"], format="rfc3339"
    )
    """Date and time (UTC) when the document model was last modified."""
    api_version: str = rest_field(name="apiVersion")
    """API version used to create this document classifier. Required."""
    base_classifier_id: Optional[str] = rest_field(name="baseClassifierId")
    """Base classifierId on top of which the classifier was trained."""
    doc_types: Dict[str, "_models.ClassifierDocumentTypeDetails"] = rest_field(name="docTypes")
    """List of document types to classify against. Required."""
    warnings: Optional[List["_models.DocumentIntelligenceWarning"]] = rest_field()
    """List of warnings encountered while building the classifier."""

    @overload
    def __init__(
        self,
        *,
        classifier_id: str,
        created_date_time: datetime.datetime,
        api_version: str,
        doc_types: Dict[str, "_models.ClassifierDocumentTypeDetails"],
        description: Optional[str] = None,
        expiration_date_time: Optional[datetime.datetime] = None,
        base_classifier_id: Optional[str] = None,
        warnings: Optional[List["_models.DocumentIntelligenceWarning"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentField(_model_base.Model):
    """An object representing the content and location of a field value.


    :ivar type: Data type of the field value. Required. Known values are: "string", "date", "time",
     "phoneNumber", "number", "integer", "selectionMark", "countryRegion", "signature", "array",
     "object", "currency", "address", "boolean", and "selectionGroup".
    :vartype type: str or ~azure.ai.documentintelligence.models.DocumentFieldType
    :ivar value_string: String value.
    :vartype value_string: str
    :ivar value_date: Date value in YYYY-MM-DD format (ISO 8601).
    :vartype value_date: ~datetime.date
    :ivar value_time: Time value in hh:mm:ss format (ISO 8601).
    :vartype value_time: ~datetime.time
    :ivar value_phone_number: Phone number value in E.164 format (ex. +19876543210).
    :vartype value_phone_number: str
    :ivar value_number: Floating point value.
    :vartype value_number: float
    :ivar value_integer: Integer value.
    :vartype value_integer: int
    :ivar value_selection_mark: Selection mark value. Known values are: "selected" and
     "unselected".
    :vartype value_selection_mark: str or
     ~azure.ai.documentintelligence.models.DocumentSelectionMarkState
    :ivar value_signature: Presence of signature. Known values are: "signed" and "unsigned".
    :vartype value_signature: str or ~azure.ai.documentintelligence.models.DocumentSignatureType
    :ivar value_country_region: 3-letter country code value (ISO 3166-1 alpha-3).
    :vartype value_country_region: str
    :ivar value_array: Array of field values.
    :vartype value_array: list[~azure.ai.documentintelligence.models.DocumentField]
    :ivar value_object: Dictionary of named field values.
    :vartype value_object: dict[str, ~azure.ai.documentintelligence.models.DocumentField]
    :ivar value_currency: Currency value.
    :vartype value_currency: ~azure.ai.documentintelligence.models.CurrencyValue
    :ivar value_address: Address value.
    :vartype value_address: ~azure.ai.documentintelligence.models.AddressValue
    :ivar value_boolean: Boolean value.
    :vartype value_boolean: bool
    :ivar value_selection_group: Selection group value.
    :vartype value_selection_group: list[str]
    :ivar content: Field content.
    :vartype content: str
    :ivar bounding_regions: Bounding regions covering the field.
    :vartype bounding_regions: list[~azure.ai.documentintelligence.models.BoundingRegion]
    :ivar spans: Location of the field in the reading order concatenated content.
    :vartype spans: list[~azure.ai.documentintelligence.models.DocumentSpan]
    :ivar confidence: Confidence of correctly extracting the field.
    :vartype confidence: float
    """

    type: Union[str, "_models.DocumentFieldType"] = rest_field()
    """Data type of the field value. Required. Known values are: \"string\", \"date\", \"time\",
     \"phoneNumber\", \"number\", \"integer\", \"selectionMark\", \"countryRegion\", \"signature\",
     \"array\", \"object\", \"currency\", \"address\", \"boolean\", and \"selectionGroup\"."""
    value_string: Optional[str] = rest_field(name="valueString")
    """String value."""
    value_date: Optional[datetime.date] = rest_field(name="valueDate")
    """Date value in YYYY-MM-DD format (ISO 8601)."""
    value_time: Optional[datetime.time] = rest_field(name="valueTime")
    """Time value in hh:mm:ss format (ISO 8601)."""
    value_phone_number: Optional[str] = rest_field(name="valuePhoneNumber")
    """Phone number value in E.164 format (ex. +19876543210)."""
    value_number: Optional[float] = rest_field(name="valueNumber")
    """Floating point value."""
    value_integer: Optional[int] = rest_field(name="valueInteger")
    """Integer value."""
    value_selection_mark: Optional[Union[str, "_models.DocumentSelectionMarkState"]] = rest_field(
        name="valueSelectionMark"
    )
    """Selection mark value. Known values are: \"selected\" and \"unselected\"."""
    value_signature: Optional[Union[str, "_models.DocumentSignatureType"]] = rest_field(name="valueSignature")
    """Presence of signature. Known values are: \"signed\" and \"unsigned\"."""
    value_country_region: Optional[str] = rest_field(name="valueCountryRegion")
    """3-letter country code value (ISO 3166-1 alpha-3)."""
    value_array: Optional[List["_models.DocumentField"]] = rest_field(name="valueArray")
    """Array of field values."""
    value_object: Optional[Dict[str, "_models.DocumentField"]] = rest_field(name="valueObject")
    """Dictionary of named field values."""
    value_currency: Optional["_models.CurrencyValue"] = rest_field(name="valueCurrency")
    """Currency value."""
    value_address: Optional["_models.AddressValue"] = rest_field(name="valueAddress")
    """Address value."""
    value_boolean: Optional[bool] = rest_field(name="valueBoolean")
    """Boolean value."""
    value_selection_group: Optional[List[str]] = rest_field(name="valueSelectionGroup")
    """Selection group value."""
    content: Optional[str] = rest_field()
    """Field content."""
    bounding_regions: Optional[List["_models.BoundingRegion"]] = rest_field(name="boundingRegions")
    """Bounding regions covering the field."""
    spans: Optional[List["_models.DocumentSpan"]] = rest_field()
    """Location of the field in the reading order concatenated content."""
    confidence: Optional[float] = rest_field()
    """Confidence of correctly extracting the field."""

    @overload
    def __init__(
        self,
        *,
        type: Union[str, "_models.DocumentFieldType"],
        value_string: Optional[str] = None,
        value_date: Optional[datetime.date] = None,
        value_time: Optional[datetime.time] = None,
        value_phone_number: Optional[str] = None,
        value_number: Optional[float] = None,
        value_integer: Optional[int] = None,
        value_selection_mark: Optional[Union[str, "_models.DocumentSelectionMarkState"]] = None,
        value_signature: Optional[Union[str, "_models.DocumentSignatureType"]] = None,
        value_country_region: Optional[str] = None,
        value_array: Optional[List["_models.DocumentField"]] = None,
        value_object: Optional[Dict[str, "_models.DocumentField"]] = None,
        value_currency: Optional["_models.CurrencyValue"] = None,
        value_address: Optional["_models.AddressValue"] = None,
        value_boolean: Optional[bool] = None,
        value_selection_group: Optional[List[str]] = None,
        content: Optional[str] = None,
        bounding_regions: Optional[List["_models.BoundingRegion"]] = None,
        spans: Optional[List["_models.DocumentSpan"]] = None,
        confidence: Optional[float] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentFieldSchema(_model_base.Model):
    """Description of the field semantic schema using a JSON Schema style syntax.


    :ivar type: Semantic data type of the field value. Required. Known values are: "string",
     "date", "time", "phoneNumber", "number", "integer", "selectionMark", "countryRegion",
     "signature", "array", "object", "currency", "address", "boolean", and "selectionGroup".
    :vartype type: str or ~azure.ai.documentintelligence.models.DocumentFieldType
    :ivar description: Field description.
    :vartype description: str
    :ivar example: Example field content.
    :vartype example: str
    :ivar items_schema: Field type schema of each array element.
    :vartype items_schema: ~azure.ai.documentintelligence.models.DocumentFieldSchema
    :ivar properties: Named sub-fields of the object field.
    :vartype properties: dict[str, ~azure.ai.documentintelligence.models.DocumentFieldSchema]
    """

    type: Union[str, "_models.DocumentFieldType"] = rest_field()
    """Semantic data type of the field value. Required. Known values are: \"string\", \"date\",
     \"time\", \"phoneNumber\", \"number\", \"integer\", \"selectionMark\", \"countryRegion\",
     \"signature\", \"array\", \"object\", \"currency\", \"address\", \"boolean\", and
     \"selectionGroup\"."""
    description: Optional[str] = rest_field()
    """Field description."""
    example: Optional[str] = rest_field()
    """Example field content."""
    items_schema: Optional["_models.DocumentFieldSchema"] = rest_field(name="items")
    """Field type schema of each array element."""
    properties: Optional[Dict[str, "_models.DocumentFieldSchema"]] = rest_field()
    """Named sub-fields of the object field."""

    @overload
    def __init__(
        self,
        *,
        type: Union[str, "_models.DocumentFieldType"],
        description: Optional[str] = None,
        example: Optional[str] = None,
        items_schema: Optional["_models.DocumentFieldSchema"] = None,
        properties: Optional[Dict[str, "_models.DocumentFieldSchema"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentFigure(_model_base.Model):
    """An object representing a figure in the document.


    :ivar bounding_regions: Bounding regions covering the figure.
    :vartype bounding_regions: list[~azure.ai.documentintelligence.models.BoundingRegion]
    :ivar spans: Location of the figure in the reading order concatenated content. Required.
    :vartype spans: list[~azure.ai.documentintelligence.models.DocumentSpan]
    :ivar elements: Child elements of the figure, excluding any caption or footnotes.
    :vartype elements: list[str]
    :ivar caption: Caption associated with the figure.
    :vartype caption: ~azure.ai.documentintelligence.models.DocumentCaption
    :ivar footnotes: List of footnotes associated with the figure.
    :vartype footnotes: list[~azure.ai.documentintelligence.models.DocumentFootnote]
    :ivar id: Figure ID.
    :vartype id: str
    """

    bounding_regions: Optional[List["_models.BoundingRegion"]] = rest_field(name="boundingRegions")
    """Bounding regions covering the figure."""
    spans: List["_models.DocumentSpan"] = rest_field()
    """Location of the figure in the reading order concatenated content. Required."""
    elements: Optional[List[str]] = rest_field()
    """Child elements of the figure, excluding any caption or footnotes."""
    caption: Optional["_models.DocumentCaption"] = rest_field()
    """Caption associated with the figure."""
    footnotes: Optional[List["_models.DocumentFootnote"]] = rest_field()
    """List of footnotes associated with the figure."""
    id: Optional[str] = rest_field()
    """Figure ID."""

    @overload
    def __init__(
        self,
        *,
        spans: List["_models.DocumentSpan"],
        bounding_regions: Optional[List["_models.BoundingRegion"]] = None,
        elements: Optional[List[str]] = None,
        caption: Optional["_models.DocumentCaption"] = None,
        footnotes: Optional[List["_models.DocumentFootnote"]] = None,
        id: Optional[str] = None,  # pylint: disable=redefined-builtin
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentFootnote(_model_base.Model):
    """A footnote object describing a table or figure.


    :ivar content: Content of the footnote. Required.
    :vartype content: str
    :ivar bounding_regions: Bounding regions covering the footnote.
    :vartype bounding_regions: list[~azure.ai.documentintelligence.models.BoundingRegion]
    :ivar spans: Location of the footnote in the reading order concatenated content. Required.
    :vartype spans: list[~azure.ai.documentintelligence.models.DocumentSpan]
    :ivar elements: Child elements of the footnote.
    :vartype elements: list[str]
    """

    content: str = rest_field()
    """Content of the footnote. Required."""
    bounding_regions: Optional[List["_models.BoundingRegion"]] = rest_field(name="boundingRegions")
    """Bounding regions covering the footnote."""
    spans: List["_models.DocumentSpan"] = rest_field()
    """Location of the footnote in the reading order concatenated content. Required."""
    elements: Optional[List[str]] = rest_field()
    """Child elements of the footnote."""

    @overload
    def __init__(
        self,
        *,
        content: str,
        spans: List["_models.DocumentSpan"],
        bounding_regions: Optional[List["_models.BoundingRegion"]] = None,
        elements: Optional[List[str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentFormula(_model_base.Model):
    """A formula object.


    :ivar kind: Formula kind. Required. Known values are: "inline" and "display".
    :vartype kind: str or ~azure.ai.documentintelligence.models.DocumentFormulaKind
    :ivar value: LaTex expression describing the formula. Required.
    :vartype value: str
    :ivar polygon: Bounding polygon of the formula, with coordinates specified relative to the
     top-left of the page. The numbers represent the x, y values of the polygon
     vertices, clockwise from the left (-180 degrees inclusive) relative to the
     element orientation.
    :vartype polygon: list[float]
    :ivar span: Location of the formula in the reading order concatenated content. Required.
    :vartype span: ~azure.ai.documentintelligence.models.DocumentSpan
    :ivar confidence: Confidence of correctly extracting the formula. Required.
    :vartype confidence: float
    """

    kind: Union[str, "_models.DocumentFormulaKind"] = rest_field()
    """Formula kind. Required. Known values are: \"inline\" and \"display\"."""
    value: str = rest_field()
    """LaTex expression describing the formula. Required."""
    polygon: Optional[List[float]] = rest_field()
    """Bounding polygon of the formula, with coordinates specified relative to the
     top-left of the page. The numbers represent the x, y values of the polygon
     vertices, clockwise from the left (-180 degrees inclusive) relative to the
     element orientation."""
    span: "_models.DocumentSpan" = rest_field()
    """Location of the formula in the reading order concatenated content. Required."""
    confidence: float = rest_field()
    """Confidence of correctly extracting the formula. Required."""

    @overload
    def __init__(
        self,
        *,
        kind: Union[str, "_models.DocumentFormulaKind"],
        value: str,
        span: "_models.DocumentSpan",
        confidence: float,
        polygon: Optional[List[float]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentIntelligenceError(_model_base.Model):
    """The error object.


    :ivar code: One of a server-defined set of error codes. Required.
    :vartype code: str
    :ivar message: A human-readable representation of the error. Required.
    :vartype message: str
    :ivar target: The target of the error.
    :vartype target: str
    :ivar details: An array of details about specific errors that led to this reported error.
    :vartype details: list[~azure.ai.documentintelligence.models.DocumentIntelligenceError]
    :ivar innererror: An object containing more specific information than the current object about
     the error.
    :vartype innererror: ~azure.ai.documentintelligence.models.DocumentIntelligenceInnerError
    """

    code: str = rest_field()
    """One of a server-defined set of error codes. Required."""
    message: str = rest_field()
    """A human-readable representation of the error. Required."""
    target: Optional[str] = rest_field()
    """The target of the error."""
    details: Optional[List["_models.DocumentIntelligenceError"]] = rest_field()
    """An array of details about specific errors that led to this reported error."""
    innererror: Optional["_models.DocumentIntelligenceInnerError"] = rest_field()
    """An object containing more specific information than the current object about the error."""

    @overload
    def __init__(
        self,
        *,
        code: str,
        message: str,
        target: Optional[str] = None,
        details: Optional[List["_models.DocumentIntelligenceError"]] = None,
        innererror: Optional["_models.DocumentIntelligenceInnerError"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentIntelligenceErrorResponse(_model_base.Model):
    """Error response object.

    All required parameters must be populated in order to send to server.

    :ivar error: Error info. Required.
    :vartype error: ~azure.ai.documentintelligence.models.DocumentIntelligenceError
    """

    error: "_models.DocumentIntelligenceError" = rest_field()
    """Error info. Required."""

    @overload
    def __init__(
        self,
        *,
        error: "_models.DocumentIntelligenceError",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentIntelligenceInnerError(_model_base.Model):
    """An object containing more specific information about the error.

    :ivar code: One of a server-defined set of error codes.
    :vartype code: str
    :ivar message: A human-readable representation of the error.
    :vartype message: str
    :ivar innererror: Inner error.
    :vartype innererror: ~azure.ai.documentintelligence.models.DocumentIntelligenceInnerError
    """

    code: Optional[str] = rest_field()
    """One of a server-defined set of error codes."""
    message: Optional[str] = rest_field()
    """A human-readable representation of the error."""
    innererror: Optional["_models.DocumentIntelligenceInnerError"] = rest_field()
    """Inner error."""

    @overload
    def __init__(
        self,
        *,
        code: Optional[str] = None,
        message: Optional[str] = None,
        innererror: Optional["_models.DocumentIntelligenceInnerError"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentIntelligenceResourceDetails(_model_base.Model):
    """General information regarding the current resource.


    :ivar custom_document_models: Details regarding custom document models. Required.
    :vartype custom_document_models:
     ~azure.ai.documentintelligence.models.CustomDocumentModelsDetails
    """

    custom_document_models: "_models.CustomDocumentModelsDetails" = rest_field(name="customDocumentModels")
    """Details regarding custom document models. Required."""

    @overload
    def __init__(
        self,
        *,
        custom_document_models: "_models.CustomDocumentModelsDetails",
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentIntelligenceWarning(_model_base.Model):
    """The error object.


    :ivar code: One of a server-defined set of warning codes. Required.
    :vartype code: str
    :ivar message: A human-readable representation of the warning. Required.
    :vartype message: str
    :ivar target: The target of the error.
    :vartype target: str
    """

    code: str = rest_field()
    """One of a server-defined set of warning codes. Required."""
    message: str = rest_field()
    """A human-readable representation of the warning. Required."""
    target: Optional[str] = rest_field()
    """The target of the error."""

    @overload
    def __init__(
        self,
        *,
        code: str,
        message: str,
        target: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentKeyValueElement(_model_base.Model):
    """An object representing the field key or value in a key-value pair.


    :ivar content: Concatenated content of the key-value element in reading order. Required.
    :vartype content: str
    :ivar bounding_regions: Bounding regions covering the key-value element.
    :vartype bounding_regions: list[~azure.ai.documentintelligence.models.BoundingRegion]
    :ivar spans: Location of the key-value element in the reading order concatenated content.
     Required.
    :vartype spans: list[~azure.ai.documentintelligence.models.DocumentSpan]
    """

    content: str = rest_field()
    """Concatenated content of the key-value element in reading order. Required."""
    bounding_regions: Optional[List["_models.BoundingRegion"]] = rest_field(name="boundingRegions")
    """Bounding regions covering the key-value element."""
    spans: List["_models.DocumentSpan"] = rest_field()
    """Location of the key-value element in the reading order concatenated content. Required."""

    @overload
    def __init__(
        self,
        *,
        content: str,
        spans: List["_models.DocumentSpan"],
        bounding_regions: Optional[List["_models.BoundingRegion"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentKeyValuePair(_model_base.Model):
    """An object representing a form field with distinct field label (key) and field
    value (may be empty).


    :ivar key: Field label of the key-value pair. Required.
    :vartype key: ~azure.ai.documentintelligence.models.DocumentKeyValueElement
    :ivar value: Field value of the key-value pair.
    :vartype value: ~azure.ai.documentintelligence.models.DocumentKeyValueElement
    :ivar confidence: Confidence of correctly extracting the key-value pair. Required.
    :vartype confidence: float
    """

    key: "_models.DocumentKeyValueElement" = rest_field()
    """Field label of the key-value pair. Required."""
    value: Optional["_models.DocumentKeyValueElement"] = rest_field()
    """Field value of the key-value pair."""
    confidence: float = rest_field()
    """Confidence of correctly extracting the key-value pair. Required."""

    @overload
    def __init__(
        self,
        *,
        key: "_models.DocumentKeyValueElement",
        confidence: float,
        value: Optional["_models.DocumentKeyValueElement"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentLanguage(_model_base.Model):
    """An object representing the detected language for a given text span.


    :ivar locale: Detected language.  Value may an ISO 639-1 language code (ex. "en", "fr")
     or BCP 47 language tag (ex. "zh-Hans"). Required.
    :vartype locale: str
    :ivar spans: Location of the text elements in the concatenated content the language applies
     to. Required.
    :vartype spans: list[~azure.ai.documentintelligence.models.DocumentSpan]
    :ivar confidence: Confidence of correctly identifying the language. Required.
    :vartype confidence: float
    """

    locale: str = rest_field()
    """Detected language.  Value may an ISO 639-1 language code (ex. \"en\", \"fr\")
     or BCP 47 language tag (ex. \"zh-Hans\"). Required."""
    spans: List["_models.DocumentSpan"] = rest_field()
    """Location of the text elements in the concatenated content the language applies
     to. Required."""
    confidence: float = rest_field()
    """Confidence of correctly identifying the language. Required."""

    @overload
    def __init__(
        self,
        *,
        locale: str,
        spans: List["_models.DocumentSpan"],
        confidence: float,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentLine(_model_base.Model):
    """A content line object consisting of an adjacent sequence of content elements,
    such as words and selection marks.


    :ivar content: Concatenated content of the contained elements in reading order. Required.
    :vartype content: str
    :ivar polygon: Bounding polygon of the line, with coordinates specified relative to the
     top-left of the page. The numbers represent the x, y values of the polygon
     vertices, clockwise from the left (-180 degrees inclusive) relative to the
     element orientation.
    :vartype polygon: list[float]
    :ivar spans: Location of the line in the reading order concatenated content. Required.
    :vartype spans: list[~azure.ai.documentintelligence.models.DocumentSpan]
    """

    content: str = rest_field()
    """Concatenated content of the contained elements in reading order. Required."""
    polygon: Optional[List[float]] = rest_field()
    """Bounding polygon of the line, with coordinates specified relative to the
     top-left of the page. The numbers represent the x, y values of the polygon
     vertices, clockwise from the left (-180 degrees inclusive) relative to the
     element orientation."""
    spans: List["_models.DocumentSpan"] = rest_field()
    """Location of the line in the reading order concatenated content. Required."""

    @overload
    def __init__(
        self,
        *,
        content: str,
        spans: List["_models.DocumentSpan"],
        polygon: Optional[List[float]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentModelBuildOperationDetails(DocumentIntelligenceOperationDetails, discriminator="documentModelBuild"):
    """Get Operation response object.


    :ivar operation_id: Operation ID. Required.
    :vartype operation_id: str
    :ivar status: Operation status.  notStarted, running, completed, or failed. Required. Known
     values are: "notStarted", "running", "failed", "succeeded", "canceled", and "skipped".
    :vartype status: str or
     ~azure.ai.documentintelligence.models.DocumentIntelligenceOperationStatus
    :ivar percent_completed: Operation progress (0-100).
    :vartype percent_completed: int
    :ivar created_date_time: Date and time (UTC) when the operation was created. Required.
    :vartype created_date_time: ~datetime.datetime
    :ivar last_updated_date_time: Date and time (UTC) when the status was last updated. Required.
    :vartype last_updated_date_time: ~datetime.datetime
    :ivar resource_location: URL of the resource targeted by this operation. Required.
    :vartype resource_location: str
    :ivar api_version: API version used to create this operation.
    :vartype api_version: str
    :ivar tags: List of key-value tag attributes associated with the document model.
    :vartype tags: dict[str, str]
    :ivar error: Encountered error.
    :vartype error: ~azure.ai.documentintelligence.models.DocumentIntelligenceError
    :ivar result: Operation result upon success.
    :vartype result: ~azure.ai.documentintelligence.models.DocumentModelDetails
    :ivar kind: Type of operation. Required. Build a new custom document model.
    :vartype kind: str or ~azure.ai.documentintelligence.models.DOCUMENT_MODEL_BUILD
    """

    result: Optional["_models.DocumentModelDetails"] = rest_field()
    """Operation result upon success."""
    kind: Literal[OperationKind.DOCUMENT_MODEL_BUILD] = rest_discriminator(name="kind")  # type: ignore
    """Type of operation. Required. Build a new custom document model."""

    @overload
    def __init__(
        self,
        *,
        operation_id: str,
        status: Union[str, "_models.DocumentIntelligenceOperationStatus"],
        created_date_time: datetime.datetime,
        last_updated_date_time: datetime.datetime,
        resource_location: str,
        percent_completed: Optional[int] = None,
        api_version: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
        error: Optional["_models.DocumentIntelligenceError"] = None,
        result: Optional["_models.DocumentModelDetails"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, kind=OperationKind.DOCUMENT_MODEL_BUILD, **kwargs)


class DocumentModelComposeOperationDetails(DocumentIntelligenceOperationDetails, discriminator="documentModelCompose"):
    """Get Operation response object.


    :ivar operation_id: Operation ID. Required.
    :vartype operation_id: str
    :ivar status: Operation status.  notStarted, running, completed, or failed. Required. Known
     values are: "notStarted", "running", "failed", "succeeded", "canceled", and "skipped".
    :vartype status: str or
     ~azure.ai.documentintelligence.models.DocumentIntelligenceOperationStatus
    :ivar percent_completed: Operation progress (0-100).
    :vartype percent_completed: int
    :ivar created_date_time: Date and time (UTC) when the operation was created. Required.
    :vartype created_date_time: ~datetime.datetime
    :ivar last_updated_date_time: Date and time (UTC) when the status was last updated. Required.
    :vartype last_updated_date_time: ~datetime.datetime
    :ivar resource_location: URL of the resource targeted by this operation. Required.
    :vartype resource_location: str
    :ivar api_version: API version used to create this operation.
    :vartype api_version: str
    :ivar tags: List of key-value tag attributes associated with the document model.
    :vartype tags: dict[str, str]
    :ivar error: Encountered error.
    :vartype error: ~azure.ai.documentintelligence.models.DocumentIntelligenceError
    :ivar result: Operation result upon success.
    :vartype result: ~azure.ai.documentintelligence.models.DocumentModelDetails
    :ivar kind: Type of operation. Required. Compose a new custom document model from existing
     models.
    :vartype kind: str or ~azure.ai.documentintelligence.models.DOCUMENT_MODEL_COMPOSE
    """

    result: Optional["_models.DocumentModelDetails"] = rest_field()
    """Operation result upon success."""
    kind: Literal[OperationKind.DOCUMENT_MODEL_COMPOSE] = rest_discriminator(name="kind")  # type: ignore
    """Type of operation. Required. Compose a new custom document model from existing models."""

    @overload
    def __init__(
        self,
        *,
        operation_id: str,
        status: Union[str, "_models.DocumentIntelligenceOperationStatus"],
        created_date_time: datetime.datetime,
        last_updated_date_time: datetime.datetime,
        resource_location: str,
        percent_completed: Optional[int] = None,
        api_version: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
        error: Optional["_models.DocumentIntelligenceError"] = None,
        result: Optional["_models.DocumentModelDetails"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, kind=OperationKind.DOCUMENT_MODEL_COMPOSE, **kwargs)


class DocumentModelCopyToOperationDetails(DocumentIntelligenceOperationDetails, discriminator="documentModelCopyTo"):
    """Get Operation response object.


    :ivar operation_id: Operation ID. Required.
    :vartype operation_id: str
    :ivar status: Operation status.  notStarted, running, completed, or failed. Required. Known
     values are: "notStarted", "running", "failed", "succeeded", "canceled", and "skipped".
    :vartype status: str or
     ~azure.ai.documentintelligence.models.DocumentIntelligenceOperationStatus
    :ivar percent_completed: Operation progress (0-100).
    :vartype percent_completed: int
    :ivar created_date_time: Date and time (UTC) when the operation was created. Required.
    :vartype created_date_time: ~datetime.datetime
    :ivar last_updated_date_time: Date and time (UTC) when the status was last updated. Required.
    :vartype last_updated_date_time: ~datetime.datetime
    :ivar resource_location: URL of the resource targeted by this operation. Required.
    :vartype resource_location: str
    :ivar api_version: API version used to create this operation.
    :vartype api_version: str
    :ivar tags: List of key-value tag attributes associated with the document model.
    :vartype tags: dict[str, str]
    :ivar error: Encountered error.
    :vartype error: ~azure.ai.documentintelligence.models.DocumentIntelligenceError
    :ivar result: Operation result upon success.
    :vartype result: ~azure.ai.documentintelligence.models.DocumentModelDetails
    :ivar kind: Type of operation. Required. Copy an existing document model to potentially a
     different resource, region, or
     subscription.
    :vartype kind: str or ~azure.ai.documentintelligence.models.DOCUMENT_MODEL_COPY_TO
    """

    result: Optional["_models.DocumentModelDetails"] = rest_field()
    """Operation result upon success."""
    kind: Literal[OperationKind.DOCUMENT_MODEL_COPY_TO] = rest_discriminator(name="kind")  # type: ignore
    """Type of operation. Required. Copy an existing document model to potentially a different
     resource, region, or
     subscription."""

    @overload
    def __init__(
        self,
        *,
        operation_id: str,
        status: Union[str, "_models.DocumentIntelligenceOperationStatus"],
        created_date_time: datetime.datetime,
        last_updated_date_time: datetime.datetime,
        resource_location: str,
        percent_completed: Optional[int] = None,
        api_version: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
        error: Optional["_models.DocumentIntelligenceError"] = None,
        result: Optional["_models.DocumentModelDetails"] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, kind=OperationKind.DOCUMENT_MODEL_COPY_TO, **kwargs)


class DocumentModelDetails(_model_base.Model):
    """Document model info.

    Readonly variables are only populated by the server, and will be ignored when sending a request.


    :ivar model_id: Unique document model name. Required.
    :vartype model_id: str
    :ivar description: Document model description.
    :vartype description: str
    :ivar created_date_time: Date and time (UTC) when the document model was created. Required.
    :vartype created_date_time: ~datetime.datetime
    :ivar expiration_date_time: Date and time (UTC) when the document model will expire.
    :vartype expiration_date_time: ~datetime.datetime
    :ivar modified_date_time: Date and time (UTC) when the document model was last modified.
    :vartype modified_date_time: ~datetime.datetime
    :ivar api_version: API version used to create this document model.
    :vartype api_version: str
    :ivar tags: List of key-value tag attributes associated with the document model.
    :vartype tags: dict[str, str]
    :ivar build_mode: Custom document model build mode. Known values are: "template" and "neural".
    :vartype build_mode: str or ~azure.ai.documentintelligence.models.DocumentBuildMode
    :ivar azure_blob_source: Azure Blob Storage location containing the training data.  Either
     azureBlobSource or azureBlobFileListSource must be specified.
    :vartype azure_blob_source: ~azure.ai.documentintelligence.models.AzureBlobContentSource
    :ivar azure_blob_file_list_source: Azure Blob Storage file list specifying the training data.
     Either
     azureBlobSource or azureBlobFileListSource must be specified.
    :vartype azure_blob_file_list_source:
     ~azure.ai.documentintelligence.models.AzureBlobFileListContentSource
    :ivar classifier_id: For composed models, the custom classifier to split and classify the input
     file.
    :vartype classifier_id: str
    :ivar split: For composed models, the file splitting behavior. Known values are: "auto",
     "none", and "perPage".
    :vartype split: str or ~azure.ai.documentintelligence.models.SplitMode
    :ivar doc_types: Supported document types.
    :vartype doc_types: dict[str, ~azure.ai.documentintelligence.models.DocumentTypeDetails]
    :ivar warnings: List of warnings encountered while building the model.
    :vartype warnings: list[~azure.ai.documentintelligence.models.DocumentIntelligenceWarning]
    :ivar training_hours: Number of V100-equivalent GPU hours consumed for model training.
    :vartype training_hours: float
    """

    model_id: str = rest_field(name="modelId", visibility=["read", "create"])
    """Unique document model name. Required."""
    description: Optional[str] = rest_field()
    """Document model description."""
    created_date_time: datetime.datetime = rest_field(name="createdDateTime", visibility=["read"], format="rfc3339")
    """Date and time (UTC) when the document model was created. Required."""
    expiration_date_time: Optional[datetime.datetime] = rest_field(
        name="expirationDateTime", visibility=["read"], format="rfc3339"
    )
    """Date and time (UTC) when the document model will expire."""
    modified_date_time: Optional[datetime.datetime] = rest_field(
        name="modifiedDateTime", visibility=["read"], format="rfc3339"
    )
    """Date and time (UTC) when the document model was last modified."""
    api_version: Optional[str] = rest_field(name="apiVersion", visibility=["read"])
    """API version used to create this document model."""
    tags: Optional[Dict[str, str]] = rest_field()
    """List of key-value tag attributes associated with the document model."""
    build_mode: Optional[Union[str, "_models.DocumentBuildMode"]] = rest_field(name="buildMode", visibility=["read"])
    """Custom document model build mode. Known values are: \"template\" and \"neural\"."""
    azure_blob_source: Optional["_models.AzureBlobContentSource"] = rest_field(
        name="azureBlobSource", visibility=["read"]
    )
    """Azure Blob Storage location containing the training data.  Either
     azureBlobSource or azureBlobFileListSource must be specified."""
    azure_blob_file_list_source: Optional["_models.AzureBlobFileListContentSource"] = rest_field(
        name="azureBlobFileListSource", visibility=["read"]
    )
    """Azure Blob Storage file list specifying the training data.  Either
     azureBlobSource or azureBlobFileListSource must be specified."""
    classifier_id: Optional[str] = rest_field(name="classifierId")
    """For composed models, the custom classifier to split and classify the input file."""
    split: Optional[Union[str, "_models.SplitMode"]] = rest_field()
    """For composed models, the file splitting behavior. Known values are: \"auto\", \"none\", and
     \"perPage\"."""
    doc_types: Optional[Dict[str, "_models.DocumentTypeDetails"]] = rest_field(name="docTypes", visibility=["read"])
    """Supported document types."""
    warnings: Optional[List["_models.DocumentIntelligenceWarning"]] = rest_field(visibility=["read"])
    """List of warnings encountered while building the model."""
    training_hours: Optional[float] = rest_field(name="trainingHours", visibility=["read"])
    """Number of V100-equivalent GPU hours consumed for model training."""

    @overload
    def __init__(
        self,
        *,
        model_id: str,
        description: Optional[str] = None,
        tags: Optional[Dict[str, str]] = None,
        classifier_id: Optional[str] = None,
        split: Optional[Union[str, "_models.SplitMode"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentPage(_model_base.Model):
    """Content and layout elements extracted from a page from the input.


    :ivar page_number: 1-based page number in the input document. Required.
    :vartype page_number: int
    :ivar angle: The general orientation of the content in clockwise direction, measured in
     degrees between (-180, 180].
    :vartype angle: float
    :ivar width: The width of the image/PDF in pixels/inches, respectively.
    :vartype width: float
    :ivar height: The height of the image/PDF in pixels/inches, respectively.
    :vartype height: float
    :ivar unit: The unit used by the width, height, and polygon properties. For images, the
     unit is "pixel". For PDF, the unit is "inch". Known values are: "pixel" and "inch".
    :vartype unit: str or ~azure.ai.documentintelligence.models.LengthUnit
    :ivar spans: Location of the page in the reading order concatenated content. Required.
    :vartype spans: list[~azure.ai.documentintelligence.models.DocumentSpan]
    :ivar words: Extracted words from the page.
    :vartype words: list[~azure.ai.documentintelligence.models.DocumentWord]
    :ivar selection_marks: Extracted selection marks from the page.
    :vartype selection_marks: list[~azure.ai.documentintelligence.models.DocumentSelectionMark]
    :ivar lines: Extracted lines from the page, potentially containing both textual and visual
     elements.
    :vartype lines: list[~azure.ai.documentintelligence.models.DocumentLine]
    :ivar barcodes: Extracted barcodes from the page.
    :vartype barcodes: list[~azure.ai.documentintelligence.models.DocumentBarcode]
    :ivar formulas: Extracted formulas from the page.
    :vartype formulas: list[~azure.ai.documentintelligence.models.DocumentFormula]
    """

    page_number: int = rest_field(name="pageNumber")
    """1-based page number in the input document. Required."""
    angle: Optional[float] = rest_field()
    """The general orientation of the content in clockwise direction, measured in
     degrees between (-180, 180]."""
    width: Optional[float] = rest_field()
    """The width of the image/PDF in pixels/inches, respectively."""
    height: Optional[float] = rest_field()
    """The height of the image/PDF in pixels/inches, respectively."""
    unit: Optional[Union[str, "_models.LengthUnit"]] = rest_field()
    """The unit used by the width, height, and polygon properties. For images, the
     unit is \"pixel\". For PDF, the unit is \"inch\". Known values are: \"pixel\" and \"inch\"."""
    spans: List["_models.DocumentSpan"] = rest_field()
    """Location of the page in the reading order concatenated content. Required."""
    words: Optional[List["_models.DocumentWord"]] = rest_field()
    """Extracted words from the page."""
    selection_marks: Optional[List["_models.DocumentSelectionMark"]] = rest_field(name="selectionMarks")
    """Extracted selection marks from the page."""
    lines: Optional[List["_models.DocumentLine"]] = rest_field()
    """Extracted lines from the page, potentially containing both textual and visual
     elements."""
    barcodes: Optional[List["_models.DocumentBarcode"]] = rest_field()
    """Extracted barcodes from the page."""
    formulas: Optional[List["_models.DocumentFormula"]] = rest_field()
    """Extracted formulas from the page."""

    @overload
    def __init__(
        self,
        *,
        page_number: int,
        spans: List["_models.DocumentSpan"],
        angle: Optional[float] = None,
        width: Optional[float] = None,
        height: Optional[float] = None,
        unit: Optional[Union[str, "_models.LengthUnit"]] = None,
        words: Optional[List["_models.DocumentWord"]] = None,
        selection_marks: Optional[List["_models.DocumentSelectionMark"]] = None,
        lines: Optional[List["_models.DocumentLine"]] = None,
        barcodes: Optional[List["_models.DocumentBarcode"]] = None,
        formulas: Optional[List["_models.DocumentFormula"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentParagraph(_model_base.Model):
    """A paragraph object consisting with contiguous lines generally with common
    alignment and spacing.


    :ivar role: Semantic role of the paragraph. Known values are: "pageHeader", "pageFooter",
     "pageNumber", "title", "sectionHeading", "footnote", and "formulaBlock".
    :vartype role: str or ~azure.ai.documentintelligence.models.ParagraphRole
    :ivar content: Concatenated content of the paragraph in reading order. Required.
    :vartype content: str
    :ivar bounding_regions: Bounding regions covering the paragraph.
    :vartype bounding_regions: list[~azure.ai.documentintelligence.models.BoundingRegion]
    :ivar spans: Location of the paragraph in the reading order concatenated content. Required.
    :vartype spans: list[~azure.ai.documentintelligence.models.DocumentSpan]
    """

    role: Optional[Union[str, "_models.ParagraphRole"]] = rest_field()
    """Semantic role of the paragraph. Known values are: \"pageHeader\", \"pageFooter\",
     \"pageNumber\", \"title\", \"sectionHeading\", \"footnote\", and \"formulaBlock\"."""
    content: str = rest_field()
    """Concatenated content of the paragraph in reading order. Required."""
    bounding_regions: Optional[List["_models.BoundingRegion"]] = rest_field(name="boundingRegions")
    """Bounding regions covering the paragraph."""
    spans: List["_models.DocumentSpan"] = rest_field()
    """Location of the paragraph in the reading order concatenated content. Required."""

    @overload
    def __init__(
        self,
        *,
        content: str,
        spans: List["_models.DocumentSpan"],
        role: Optional[Union[str, "_models.ParagraphRole"]] = None,
        bounding_regions: Optional[List["_models.BoundingRegion"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentSection(_model_base.Model):
    """An object representing a section in the document.


    :ivar spans: Location of the section in the reading order concatenated content. Required.
    :vartype spans: list[~azure.ai.documentintelligence.models.DocumentSpan]
    :ivar elements: Child elements of the section.
    :vartype elements: list[str]
    """

    spans: List["_models.DocumentSpan"] = rest_field()
    """Location of the section in the reading order concatenated content. Required."""
    elements: Optional[List[str]] = rest_field()
    """Child elements of the section."""

    @overload
    def __init__(
        self,
        *,
        spans: List["_models.DocumentSpan"],
        elements: Optional[List[str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentSelectionMark(_model_base.Model):
    """A selection mark object representing check boxes, radio buttons, and other
    elements indicating a selection.


    :ivar state: State of the selection mark. Required. Known values are: "selected" and
     "unselected".
    :vartype state: str or ~azure.ai.documentintelligence.models.DocumentSelectionMarkState
    :ivar polygon: Bounding polygon of the selection mark, with coordinates specified relative
     to the top-left of the page. The numbers represent the x, y values of the
     polygon vertices, clockwise from the left (-180 degrees inclusive) relative
     to the element orientation.
    :vartype polygon: list[float]
    :ivar span: Location of the selection mark in the reading order concatenated content. Required.
    :vartype span: ~azure.ai.documentintelligence.models.DocumentSpan
    :ivar confidence: Confidence of correctly extracting the selection mark. Required.
    :vartype confidence: float
    """

    state: Union[str, "_models.DocumentSelectionMarkState"] = rest_field()
    """State of the selection mark. Required. Known values are: \"selected\" and \"unselected\"."""
    polygon: Optional[List[float]] = rest_field()
    """Bounding polygon of the selection mark, with coordinates specified relative
     to the top-left of the page. The numbers represent the x, y values of the
     polygon vertices, clockwise from the left (-180 degrees inclusive) relative
     to the element orientation."""
    span: "_models.DocumentSpan" = rest_field()
    """Location of the selection mark in the reading order concatenated content. Required."""
    confidence: float = rest_field()
    """Confidence of correctly extracting the selection mark. Required."""

    @overload
    def __init__(
        self,
        *,
        state: Union[str, "_models.DocumentSelectionMarkState"],
        span: "_models.DocumentSpan",
        confidence: float,
        polygon: Optional[List[float]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentSpan(_model_base.Model):
    """Contiguous region of the concatenated content property, specified as an offset
    and length.


    :ivar offset: Zero-based index of the content represented by the span. Required.
    :vartype offset: int
    :ivar length: Number of characters in the content represented by the span. Required.
    :vartype length: int
    """

    offset: int = rest_field()
    """Zero-based index of the content represented by the span. Required."""
    length: int = rest_field()
    """Number of characters in the content represented by the span. Required."""

    @overload
    def __init__(
        self,
        *,
        offset: int,
        length: int,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentStyle(_model_base.Model):
    """An object representing observed text styles.


    :ivar is_handwritten: Is content handwritten?.
    :vartype is_handwritten: bool
    :ivar similar_font_family: Visually most similar font from among the set of supported font
     families, with
     fallback fonts following CSS convention (ex. 'Arial, sans-serif').
    :vartype similar_font_family: str
    :ivar font_style: Font style. Known values are: "normal" and "italic".
    :vartype font_style: str or ~azure.ai.documentintelligence.models.DocumentFontStyle
    :ivar font_weight: Font weight. Known values are: "normal" and "bold".
    :vartype font_weight: str or ~azure.ai.documentintelligence.models.DocumentFontWeight
    :ivar color: Foreground color in #rrggbb hexadecimal format.
    :vartype color: str
    :ivar background_color: Background color in #rrggbb hexadecimal format..
    :vartype background_color: str
    :ivar spans: Location of the text elements in the concatenated content the style applies to.
     Required.
    :vartype spans: list[~azure.ai.documentintelligence.models.DocumentSpan]
    :ivar confidence: Confidence of correctly identifying the style. Required.
    :vartype confidence: float
    """

    is_handwritten: Optional[bool] = rest_field(name="isHandwritten")
    """Is content handwritten?."""
    similar_font_family: Optional[str] = rest_field(name="similarFontFamily")
    """Visually most similar font from among the set of supported font families, with
     fallback fonts following CSS convention (ex. 'Arial, sans-serif')."""
    font_style: Optional[Union[str, "_models.DocumentFontStyle"]] = rest_field(name="fontStyle")
    """Font style. Known values are: \"normal\" and \"italic\"."""
    font_weight: Optional[Union[str, "_models.DocumentFontWeight"]] = rest_field(name="fontWeight")
    """Font weight. Known values are: \"normal\" and \"bold\"."""
    color: Optional[str] = rest_field()
    """Foreground color in #rrggbb hexadecimal format."""
    background_color: Optional[str] = rest_field(name="backgroundColor")
    """Background color in #rrggbb hexadecimal format.."""
    spans: List["_models.DocumentSpan"] = rest_field()
    """Location of the text elements in the concatenated content the style applies to. Required."""
    confidence: float = rest_field()
    """Confidence of correctly identifying the style. Required."""

    @overload
    def __init__(
        self,
        *,
        spans: List["_models.DocumentSpan"],
        confidence: float,
        is_handwritten: Optional[bool] = None,
        similar_font_family: Optional[str] = None,
        font_style: Optional[Union[str, "_models.DocumentFontStyle"]] = None,
        font_weight: Optional[Union[str, "_models.DocumentFontWeight"]] = None,
        color: Optional[str] = None,
        background_color: Optional[str] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentTable(_model_base.Model):
    """A table object consisting table cells arranged in a rectangular layout.


    :ivar row_count: Number of rows in the table. Required.
    :vartype row_count: int
    :ivar column_count: Number of columns in the table. Required.
    :vartype column_count: int
    :ivar cells: Cells contained within the table. Required.
    :vartype cells: list[~azure.ai.documentintelligence.models.DocumentTableCell]
    :ivar bounding_regions: Bounding regions covering the table.
    :vartype bounding_regions: list[~azure.ai.documentintelligence.models.BoundingRegion]
    :ivar spans: Location of the table in the reading order concatenated content. Required.
    :vartype spans: list[~azure.ai.documentintelligence.models.DocumentSpan]
    :ivar caption: Caption associated with the table.
    :vartype caption: ~azure.ai.documentintelligence.models.DocumentCaption
    :ivar footnotes: List of footnotes associated with the table.
    :vartype footnotes: list[~azure.ai.documentintelligence.models.DocumentFootnote]
    """

    row_count: int = rest_field(name="rowCount")
    """Number of rows in the table. Required."""
    column_count: int = rest_field(name="columnCount")
    """Number of columns in the table. Required."""
    cells: List["_models.DocumentTableCell"] = rest_field()
    """Cells contained within the table. Required."""
    bounding_regions: Optional[List["_models.BoundingRegion"]] = rest_field(name="boundingRegions")
    """Bounding regions covering the table."""
    spans: List["_models.DocumentSpan"] = rest_field()
    """Location of the table in the reading order concatenated content. Required."""
    caption: Optional["_models.DocumentCaption"] = rest_field()
    """Caption associated with the table."""
    footnotes: Optional[List["_models.DocumentFootnote"]] = rest_field()
    """List of footnotes associated with the table."""

    @overload
    def __init__(
        self,
        *,
        row_count: int,
        column_count: int,
        cells: List["_models.DocumentTableCell"],
        spans: List["_models.DocumentSpan"],
        bounding_regions: Optional[List["_models.BoundingRegion"]] = None,
        caption: Optional["_models.DocumentCaption"] = None,
        footnotes: Optional[List["_models.DocumentFootnote"]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentTableCell(_model_base.Model):
    """An object representing the location and content of a table cell.


    :ivar kind: Table cell kind. Known values are: "content", "rowHeader", "columnHeader",
     "stubHead", and "description".
    :vartype kind: str or ~azure.ai.documentintelligence.models.DocumentTableCellKind
    :ivar row_index: Row index of the cell. Required.
    :vartype row_index: int
    :ivar column_index: Column index of the cell. Required.
    :vartype column_index: int
    :ivar row_span: Number of rows spanned by this cell.
    :vartype row_span: int
    :ivar column_span: Number of columns spanned by this cell.
    :vartype column_span: int
    :ivar content: Concatenated content of the table cell in reading order. Required.
    :vartype content: str
    :ivar bounding_regions: Bounding regions covering the table cell.
    :vartype bounding_regions: list[~azure.ai.documentintelligence.models.BoundingRegion]
    :ivar spans: Location of the table cell in the reading order concatenated content. Required.
    :vartype spans: list[~azure.ai.documentintelligence.models.DocumentSpan]
    :ivar elements: Child elements of the table cell.
    :vartype elements: list[str]
    """

    kind: Optional[Union[str, "_models.DocumentTableCellKind"]] = rest_field()
    """Table cell kind. Known values are: \"content\", \"rowHeader\", \"columnHeader\", \"stubHead\",
     and \"description\"."""
    row_index: int = rest_field(name="rowIndex")
    """Row index of the cell. Required."""
    column_index: int = rest_field(name="columnIndex")
    """Column index of the cell. Required."""
    row_span: Optional[int] = rest_field(name="rowSpan")
    """Number of rows spanned by this cell."""
    column_span: Optional[int] = rest_field(name="columnSpan")
    """Number of columns spanned by this cell."""
    content: str = rest_field()
    """Concatenated content of the table cell in reading order. Required."""
    bounding_regions: Optional[List["_models.BoundingRegion"]] = rest_field(name="boundingRegions")
    """Bounding regions covering the table cell."""
    spans: List["_models.DocumentSpan"] = rest_field()
    """Location of the table cell in the reading order concatenated content. Required."""
    elements: Optional[List[str]] = rest_field()
    """Child elements of the table cell."""

    @overload
    def __init__(
        self,
        *,
        row_index: int,
        column_index: int,
        content: str,
        spans: List["_models.DocumentSpan"],
        kind: Optional[Union[str, "_models.DocumentTableCellKind"]] = None,
        row_span: Optional[int] = None,
        column_span: Optional[int] = None,
        bounding_regions: Optional[List["_models.BoundingRegion"]] = None,
        elements: Optional[List[str]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentTypeDetails(_model_base.Model):
    """Document type info.

    :ivar description: Document model description.
    :vartype description: str
    :ivar build_mode: Custom document model build mode. Known values are: "template" and "neural".
    :vartype build_mode: str or ~azure.ai.documentintelligence.models.DocumentBuildMode
    :ivar field_schema: Description of the document semantic schema using a JSON Schema style
     syntax.
    :vartype field_schema: dict[str, ~azure.ai.documentintelligence.models.DocumentFieldSchema]
    :ivar field_confidence: Estimated confidence for each field.
    :vartype field_confidence: dict[str, float]
    :ivar model_id: Document model to use for analyzing documents with specified type.
    :vartype model_id: str
    :ivar confidence_threshold: Only perform analysis if docType confidence is above threshold.
    :vartype confidence_threshold: float
    :ivar features: List of optional analysis features.
    :vartype features: list[str or ~azure.ai.documentintelligence.models.DocumentAnalysisFeature]
    :ivar query_fields: List of additional fields to extract.  Ex. "NumberOfGuests,StoreNumber".
    :vartype query_fields: list[str]
    :ivar max_documents_to_analyze: Maximum number of documents of specified type to analyze.
     Default=all.
    :vartype max_documents_to_analyze: int
    """

    description: Optional[str] = rest_field()
    """Document model description."""
    build_mode: Optional[Union[str, "_models.DocumentBuildMode"]] = rest_field(name="buildMode")
    """Custom document model build mode. Known values are: \"template\" and \"neural\"."""
    field_schema: Optional[Dict[str, "_models.DocumentFieldSchema"]] = rest_field(name="fieldSchema")
    """Description of the document semantic schema using a JSON Schema style syntax."""
    field_confidence: Optional[Dict[str, float]] = rest_field(name="fieldConfidence")
    """Estimated confidence for each field."""
    model_id: Optional[str] = rest_field(name="modelId")
    """Document model to use for analyzing documents with specified type."""
    confidence_threshold: Optional[float] = rest_field(name="confidenceThreshold")
    """Only perform analysis if docType confidence is above threshold."""
    features: Optional[List[Union[str, "_models.DocumentAnalysisFeature"]]] = rest_field()
    """List of optional analysis features."""
    query_fields: Optional[List[str]] = rest_field(name="queryFields")
    """List of additional fields to extract.  Ex. \"NumberOfGuests,StoreNumber\"."""
    max_documents_to_analyze: Optional[int] = rest_field(name="maxDocumentsToAnalyze")
    """Maximum number of documents of specified type to analyze.  Default=all."""

    @overload
    def __init__(
        self,
        *,
        description: Optional[str] = None,
        build_mode: Optional[Union[str, "_models.DocumentBuildMode"]] = None,
        field_schema: Optional[Dict[str, "_models.DocumentFieldSchema"]] = None,
        field_confidence: Optional[Dict[str, float]] = None,
        model_id: Optional[str] = None,
        confidence_threshold: Optional[float] = None,
        features: Optional[List[Union[str, "_models.DocumentAnalysisFeature"]]] = None,
        query_fields: Optional[List[str]] = None,
        max_documents_to_analyze: Optional[int] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class DocumentWord(_model_base.Model):
    """A word object consisting of a contiguous sequence of characters.  For non-space
    delimited languages, such as Chinese, Japanese, and Korean, each character is
    represented as its own word.


    :ivar content: Text content of the word. Required.
    :vartype content: str
    :ivar polygon: Bounding polygon of the word, with coordinates specified relative to the
     top-left of the page. The numbers represent the x, y values of the polygon
     vertices, clockwise from the left (-180 degrees inclusive) relative to the
     element orientation.
    :vartype polygon: list[float]
    :ivar span: Location of the word in the reading order concatenated content. Required.
    :vartype span: ~azure.ai.documentintelligence.models.DocumentSpan
    :ivar confidence: Confidence of correctly extracting the word. Required.
    :vartype confidence: float
    """

    content: str = rest_field()
    """Text content of the word. Required."""
    polygon: Optional[List[float]] = rest_field()
    """Bounding polygon of the word, with coordinates specified relative to the
     top-left of the page. The numbers represent the x, y values of the polygon
     vertices, clockwise from the left (-180 degrees inclusive) relative to the
     element orientation."""
    span: "_models.DocumentSpan" = rest_field()
    """Location of the word in the reading order concatenated content. Required."""
    confidence: float = rest_field()
    """Confidence of correctly extracting the word. Required."""

    @overload
    def __init__(
        self,
        *,
        content: str,
        span: "_models.DocumentSpan",
        confidence: float,
        polygon: Optional[List[float]] = None,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)


class ModelCopyAuthorization(_model_base.Model):
    """Authorization to copy a document model to the specified target resource and
    modelId.


    :ivar target_resource_id: ID of the target Azure resource where the document model should be
     copied to. Required.
    :vartype target_resource_id: str
    :ivar target_resource_region: Location of the target Azure resource where the document model
     should be copied
     to. Required.
    :vartype target_resource_region: str
    :ivar target_model_id: Identifier of the target document model. Required.
    :vartype target_model_id: str
    :ivar target_model_location: URL of the copied document model in the target account. Required.
    :vartype target_model_location: str
    :ivar access_token: Token used to authorize the request. Required.
    :vartype access_token: str
    :ivar expiration_date_time: Date/time when the access token expires. Required.
    :vartype expiration_date_time: ~datetime.datetime
    """

    target_resource_id: str = rest_field(name="targetResourceId")
    """ID of the target Azure resource where the document model should be copied to. Required."""
    target_resource_region: str = rest_field(name="targetResourceRegion")
    """Location of the target Azure resource where the document model should be copied
     to. Required."""
    target_model_id: str = rest_field(name="targetModelId")
    """Identifier of the target document model. Required."""
    target_model_location: str = rest_field(name="targetModelLocation")
    """URL of the copied document model in the target account. Required."""
    access_token: str = rest_field(name="accessToken")
    """Token used to authorize the request. Required."""
    expiration_date_time: datetime.datetime = rest_field(name="expirationDateTime", format="rfc3339")
    """Date/time when the access token expires. Required."""

    @overload
    def __init__(
        self,
        *,
        target_resource_id: str,
        target_resource_region: str,
        target_model_id: str,
        target_model_location: str,
        access_token: str,
        expiration_date_time: datetime.datetime,
    ) -> None: ...

    @overload
    def __init__(self, mapping: Mapping[str, Any]) -> None:
        """
        :param mapping: raw JSON to initialize the model.
        :type mapping: Mapping[str, Any]
        """

    def __init__(self, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
