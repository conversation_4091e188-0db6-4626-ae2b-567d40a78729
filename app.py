"""
DTrans - 香港上市公司披露文件智能翻译应用
基于Gradio的Web界面，支持中英文互译
"""

import os
import gradio as gr
import tempfile
from typing import Optional, Tuple, List
from utils.translator import DocumentTranslator
from utils.openrouter_client import OpenRouterClient
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


class DTransApp:
    def __init__(self):
        self.translator = DocumentTranslator()
        self.client = OpenRouterClient()
        self.reference_analysis = None

        # 默认系统提示词
        self.default_system_message = """你是一个专业的香港上市公司文件翻译专家。请根据提供的参考翻译对照和术语表，准确翻译以下文档内容。

翻译要求：
1. 保持专业术语的一致性
2. 遵循香港上市公司披露文件的正式语言风格
3. 确保翻译准确、流畅、符合相关法规要求
4. 保持原文的格式和结构
5. 对于专业术语，优先使用参考文件中的对应翻译

请只返回翻译结果，不要包含其他说明文字。"""

    def load_models(self) -> List[dict]:
        """加载可用模型列表"""
        try:
            models = self.client.get_available_models()
            # 返回完整的模型信息，包含搜索需要的字段
            return models
        except Exception as e:
            print(f"加载模型失败: {e}")
            return [
                {
                    "id": "anthropic/claude-3-sonnet-20240229",
                    "name": "Claude 3 Sonnet",
                    "description": "高质量的对话和文本生成模型",
                    "context_length": 200000,
                    "pricing": {},
                },
                {
                    "id": "openai/gpt-4-turbo",
                    "name": "GPT-4 Turbo",
                    "description": "OpenAI的高性能模型",
                    "context_length": 128000,
                    "pricing": {},
                },
                {
                    "id": "openai/gpt-3.5-turbo",
                    "name": "GPT-3.5 Turbo",
                    "description": "快速且经济的模型",
                    "context_length": 16000,
                    "pricing": {},
                },
            ]

    def filter_models(
        self, search_query: str, all_models: List[dict]
    ) -> List[Tuple[str, str]]:
        """根据搜索查询过滤模型"""
        if not search_query.strip():
            # 如果没有搜索查询，返回所有模型
            return [
                (f"{model['name']} ({model['id']})", model["id"])
                for model in all_models
            ]

        search_query = search_query.lower().strip()
        filtered_models = []

        for model in all_models:
            # 在模型名称、ID、描述和标签中搜索
            searchable_text = (
                f"{model['name']} {model['id']} {model.get('description', '')}".lower()
            )

            # 添加标签到搜索文本
            if model.get("tags"):
                tags_text = " ".join(model["tags"]).lower()
                searchable_text += f" {tags_text}"

            # 支持多个关键词搜索
            search_terms = search_query.split()
            if all(term in searchable_text for term in search_terms):
                filtered_models.append(model)

        # 按相关性排序（名称匹配优先）
        def relevance_score(model):
            name_match = search_query in model["name"].lower()
            id_match = search_query in model["id"].lower()
            return (name_match * 2) + (id_match * 1)

        filtered_models.sort(key=relevance_score, reverse=True)

        return [
            (f"{model['name']} ({model['id']})", model["id"])
            for model in filtered_models
        ]

    def analyze_reference_files(
        self, ref_file1, ref_file2, progress=gr.Progress()
    ) -> str:
        """分析参考文件"""
        try:
            if not ref_file1 or not ref_file2:
                return "❌ 请上传两个参考文件"

            progress(0.1, "开始分析参考文件...")

            # 保存上传的文件到临时目录
            ref1_path = self._save_uploaded_file(ref_file1)
            ref2_path = self._save_uploaded_file(ref_file2)

            progress(0.5, "正在提取翻译对应关系...")

            # 分析参考文件
            self.reference_analysis = self.translator.analyze_reference_files(
                ref1_path, ref2_path
            )

            progress(1.0, "分析完成")

            # 生成分析摘要
            summary = self.translator.get_translation_summary(self.reference_analysis)

            return f"✅ 参考文件分析完成\n\n{summary}"

        except Exception as e:
            return f"❌ 分析失败: {str(e)}"

    def translate_document(
        self, target_file, model_id: str, system_message: str, progress=gr.Progress()
    ) -> Tuple[str, Optional[str]]:
        """翻译目标文档"""
        try:
            if not target_file:
                return "❌ 请上传目标文件", None

            if not self.reference_analysis:
                return "❌ 请先分析参考文件", None

            if not model_id:
                return "❌ 请选择翻译模型", None

            progress(0.1, "开始翻译文档...")

            # 保存目标文件
            target_path = self._save_uploaded_file(target_file)

            # 使用系统提示词（如果为空则使用默认值）
            final_system_message = system_message.strip() or self.default_system_message

            # 执行翻译
            def progress_callback(ratio, message):
                progress(0.1 + ratio * 0.8, message)

            translated_file_path = self.translator.translate_document(
                target_file_path=target_path,
                reference_analysis=self.reference_analysis,
                model_id=model_id,
                system_message=final_system_message,
                progress_callback=progress_callback,
            )

            progress(1.0, "翻译完成")

            return "✅ 翻译完成！请下载翻译后的文档。", translated_file_path

        except Exception as e:
            return f"❌ 翻译失败: {str(e)}", None

    def _save_uploaded_file(self, uploaded_file) -> str:
        """保存上传的文件到临时目录"""
        if hasattr(uploaded_file, "name"):
            # Gradio文件对象
            temp_path = os.path.join(
                tempfile.gettempdir(), os.path.basename(uploaded_file.name)
            )
            with open(temp_path, "wb") as f:
                with open(uploaded_file.name, "rb") as src:
                    f.write(src.read())
            return temp_path
        else:
            # 直接是文件路径
            return uploaded_file

    def create_interface(self):
        """创建Gradio界面"""

        # 加载模型列表
        all_models = self.load_models()
        initial_model_choices = self.filter_models("", all_models)

        with gr.Blocks(
            title="DTrans - 香港上市公司文件智能翻译",
            theme=gr.themes.Soft(),
            css="""
            .main-header { text-align: center; margin-bottom: 2rem; }
            .upload-section { border: 2px dashed #ccc; padding: 1rem; margin: 1rem 0; }
            .status-box { padding: 1rem; margin: 1rem 0; border-radius: 5px; }
            """,
        ) as interface:

            gr.HTML(
                """
            <div class="main-header">
                <h1>🌐 DTrans - 香港上市公司文件智能翻译</h1>
                <p>基于AI的专业文档翻译工具，支持中英文互译</p>
            </div>
            """
            )

            with gr.Tab("📁 文件上传与分析"):
                gr.Markdown("### 第一步：上传参考文件")
                gr.Markdown("请上传两个互为翻译对照的参考文件（支持.docx和.pdf格式）")

                with gr.Row():
                    ref_file1 = gr.File(
                        label="参考文件1（如：中文版）", file_types=[".docx", ".pdf"]
                    )
                    ref_file2 = gr.File(
                        label="参考文件2（如：英文版）", file_types=[".docx", ".pdf"]
                    )

                analyze_btn = gr.Button("🔍 分析参考文件", variant="primary")
                analysis_status = gr.Textbox(
                    label="分析状态",
                    placeholder="等待分析参考文件...",
                    interactive=False,
                    lines=5,
                )

            with gr.Tab("🚀 翻译设置与执行"):
                gr.Markdown("### 第二步：配置翻译参数")

                with gr.Row():
                    with gr.Column(scale=1):
                        target_file = gr.File(
                            label="目标文件（待翻译）", file_types=[".docx", ".pdf"]
                        )

                        # 模型搜索框
                        model_search = gr.Textbox(
                            label="搜索模型",
                            placeholder="输入关键词搜索: claude, gpt-4, 快速, 经济, 长上下文...",
                            interactive=True,
                        )

                        # 模型选择下拉菜单
                        model_dropdown = gr.Dropdown(
                            choices=initial_model_choices,
                            label="选择翻译模型",
                            value=(
                                initial_model_choices[0][1]
                                if initial_model_choices
                                else None
                            ),
                            interactive=True,
                            allow_custom_value=False,
                        )

                        # 刷新模型列表按钮
                        refresh_models_btn = gr.Button(
                            "🔄 刷新模型列表", variant="secondary", size="sm"
                        )

                        # 显示模型信息
                        model_info = gr.Markdown(
                            value="💡 **提示**: 在上方搜索框中输入关键词来快速找到合适的模型",
                            visible=True,
                        )

                    with gr.Column(scale=2):
                        system_message = gr.Textbox(
                            label="系统提示词（可自定义翻译指令）",
                            value=self.default_system_message,
                            lines=8,
                            placeholder="输入自定义的翻译指令...",
                        )

                translate_btn = gr.Button("🌐 开始翻译", variant="primary", size="lg")

                with gr.Row():
                    translation_status = gr.Textbox(
                        label="翻译状态",
                        placeholder="等待开始翻译...",
                        interactive=False,
                        lines=3,
                    )

                    download_file = gr.File(label="下载翻译结果", interactive=False)

            with gr.Tab("ℹ️ 使用说明"):
                gr.Markdown(
                    """
                ## 使用指南

                ### 1. 准备文件
                - **参考文件**: 两个互为翻译对照的文档（如同一份文件的中英文版本）
                - **目标文件**: 需要翻译的文档
                - **支持格式**: .docx 和 .pdf

                ### 2. 操作步骤
                1. 在"文件上传与分析"标签页上传两个参考文件
                2. 点击"分析参考文件"按钮，系统将提取翻译规则和术语对照
                3. 在"翻译设置与执行"标签页上传目标文件
                4. 选择合适的翻译模型
                5. 根据需要调整系统提示词
                6. 点击"开始翻译"执行翻译任务
                7. 翻译完成后下载结果文档

                ### 3. 注意事项
                - 确保参考文件质量良好，翻译对应关系清晰
                - 选择适合的模型（Claude和GPT-4通常效果较好）
                - 可根据具体需求调整系统提示词
                - 翻译时间取决于文档长度和模型响应速度

                ### 4. API配置
                请确保在.env文件中配置了有效的OpenRouter API密钥：
                ```
                OPENROUTER_API_KEY=your_api_key_here
                ```
                """
                )

            # 搜索功能事件处理
            def update_model_choices(search_query):
                """根据搜索查询更新模型选择"""
                filtered_choices = self.filter_models(search_query, all_models)

                # 更新模型信息显示
                if search_query.strip():
                    info_text = f"🔍 找到 {len(filtered_choices)} 个匹配的模型"
                    if len(filtered_choices) == 0:
                        info_text += "\n💡 **建议**: 尝试使用更简单的关键词，如 'claude', 'gpt', 'gemini' 等"
                else:
                    info_text = (
                        "💡 **提示**: 在上方搜索框中输入关键词来快速找到合适的模型"
                    )

                # 返回更新后的选择和默认值
                new_value = filtered_choices[0][1] if filtered_choices else None
                return gr.Dropdown(choices=filtered_choices, value=new_value), info_text

            def refresh_model_list():
                """刷新模型列表"""
                try:
                    # 强制刷新模型列表
                    nonlocal all_models
                    all_models = self.client.refresh_models_cache()

                    # 重新过滤模型
                    new_choices = self.filter_models("", all_models)
                    new_value = new_choices[0][1] if new_choices else None

                    info_text = f"✅ 模型列表已刷新！共 {len(all_models)} 个可用模型"

                    return (
                        gr.Dropdown(choices=new_choices, value=new_value),
                        info_text,
                        "",  # 清空搜索框
                    )
                except Exception as e:
                    error_text = f"❌ 刷新失败: {str(e)}"
                    return (gr.Dropdown(), error_text, "")  # 保持原有选择  # 清空搜索框

            def get_model_details(selected_model_id):
                """获取选中模型的详细信息"""
                if not selected_model_id:
                    return "请选择一个模型"

                # 查找模型详细信息
                selected_model = None
                for model in all_models:
                    if model["id"] == selected_model_id:
                        selected_model = model
                        break

                if not selected_model:
                    return "模型信息不可用"

                # 格式化模型信息
                info_parts = [
                    f"**{selected_model['name']}**",
                    f"🆔 ID: `{selected_model['id']}`",
                ]

                if selected_model.get("description"):
                    info_parts.append(f"📝 描述: {selected_model['description']}")

                if selected_model.get("context_length"):
                    context_length = selected_model["context_length"]
                    if context_length >= 1000:
                        context_str = f"{context_length//1000}K"
                    else:
                        context_str = str(context_length)
                    info_parts.append(f"📏 上下文长度: {context_str} tokens")

                # 显示模型标签
                if selected_model.get("tags"):
                    tags_str = " ".join([f"`{tag}`" for tag in selected_model["tags"]])
                    info_parts.append(f"🏷️ 特性: {tags_str}")

                # 添加使用建议
                model_id_lower = selected_model["id"].lower()
                if "claude" in model_id_lower:
                    info_parts.append(
                        "💡 **推荐用途**: 复杂文档翻译、专业术语处理、长文本翻译"
                    )
                elif "gpt-4" in model_id_lower:
                    info_parts.append(
                        "💡 **推荐用途**: 高质量翻译、多语言处理、复杂格式文档"
                    )
                elif "gpt-3.5" in model_id_lower:
                    info_parts.append(
                        "💡 **推荐用途**: 快速翻译、日常文档、成本敏感场景"
                    )

                return "\n\n".join(info_parts)

            # 事件绑定
            model_search.change(
                fn=update_model_choices,
                inputs=[model_search],
                outputs=[model_dropdown, model_info],
            )

            refresh_models_btn.click(
                fn=refresh_model_list,
                inputs=[],
                outputs=[model_dropdown, model_info, model_search],
            )

            model_dropdown.change(
                fn=get_model_details, inputs=[model_dropdown], outputs=[model_info]
            )

            analyze_btn.click(
                fn=self.analyze_reference_files,
                inputs=[ref_file1, ref_file2],
                outputs=[analysis_status],
            )

            translate_btn.click(
                fn=self.translate_document,
                inputs=[target_file, model_dropdown, system_message],
                outputs=[translation_status, download_file],
            )

        return interface


def main():
    """主函数"""
    # 检查API密钥
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        print("⚠️  警告: 未找到OPENROUTER_API_KEY环境变量")
        print("请创建.env文件并设置您的OpenRouter API密钥")
        print("参考.env.example文件")

    # 创建应用
    app = DTransApp()
    interface = app.create_interface()

    # 启动应用
    interface.launch(server_name="0.0.0.0", server_port=7860, share=False, debug=True)


if __name__ == "__main__":
    main()
