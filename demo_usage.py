"""
DTrans应用使用演示脚本
展示如何通过代码直接使用翻译功能（不通过Web界面）
"""

import os
import tempfile
from utils.translator import DocumentTranslator
from utils.openrouter_client import OpenRouterClient
from dotenv import load_dotenv

def demo_translation():
    """演示翻译功能"""
    
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    api_key = os.getenv("OPENROUTER_API_KEY")
    if not api_key:
        print("❌ 请在.env文件中设置OPENROUTER_API_KEY")
        return
    
    print("🚀 DTrans翻译功能演示")
    print("=" * 50)
    
    # 初始化翻译器
    translator = DocumentTranslator(api_key)
    client = OpenRouterClient(api_key)
    
    # 获取可用模型
    print("📋 获取可用模型...")
    try:
        models = client.get_available_models()
        print(f"✅ 找到 {len(models)} 个可用模型")
        
        # 显示前5个模型
        print("\n前5个可用模型:")
        for i, model in enumerate(models[:5], 1):
            print(f"  {i}. {model['name']} ({model['id']})")
        
        # 选择第一个模型进行演示
        selected_model = models[0]['id']
        print(f"\n🎯 使用模型: {selected_model}")
        
    except Exception as e:
        print(f"❌ 获取模型失败: {e}")
        return
    
    # 演示文本翻译
    print("\n📝 演示文本翻译...")
    
    # 示例文本
    sample_text = """
    根据香港联合交易所有限公司证券上市规则，本公司董事会宣布截至二零二四年十二月三十一日止年度的业绩。
    本集团于回顾年度内录得收入港币十亿元，较去年同期增长百分之十五。
    """
    
    # 系统提示词
    system_message = """
    你是专业的香港上市公司文件翻译专家。请将以下中文内容翻译成英文，
    保持专业术语的准确性和正式文档的语言风格。
    """
    
    try:
        print("🔄 正在翻译...")
        translated_text = client.translate_text(
            text=sample_text.strip(),
            model_id=selected_model,
            system_message=system_message,
            reference_context=""
        )
        
        print("✅ 翻译完成!")
        print("\n原文:")
        print("-" * 30)
        print(sample_text.strip())
        
        print("\n译文:")
        print("-" * 30)
        print(translated_text)
        
    except Exception as e:
        print(f"❌ 翻译失败: {e}")
        return
    
    print("\n" + "=" * 50)
    print("🎉 演示完成!")
    print("\n💡 提示:")
    print("- 要使用完整功能，请运行 start_app.bat 启动Web界面")
    print("- Web界面支持文档上传、参考文件分析等高级功能")
    print("- 访问 http://localhost:7860 使用图形界面")

def demo_document_parser():
    """演示文档解析功能"""
    print("\n📄 文档解析功能演示")
    print("-" * 30)
    
    from utils.document_parser import DocumentParser
    parser = DocumentParser()
    
    print("✅ 支持的文件格式:", parser.supported_formats)
    print("✅ 文档解析器初始化成功")
    
    # 创建示例文档内容
    sample_paragraphs = [
        {
            'index': 0,
            'text': '这是第一段示例文本。',
            'style': 'Normal',
            'alignment': None
        },
        {
            'index': 1,
            'text': '这是第二段示例文本，展示文档结构。',
            'style': 'Normal',
            'alignment': None
        }
    ]
    
    try:
        # 创建示例DOCX文件
        temp_file = os.path.join(tempfile.gettempdir(), "demo_output.docx")
        output_path = parser.create_docx_from_paragraphs(sample_paragraphs, temp_file)
        print(f"✅ 成功创建示例文档: {output_path}")
        
        # 清理临时文件
        if os.path.exists(output_path):
            os.remove(output_path)
            print("🧹 清理临时文件完成")
            
    except Exception as e:
        print(f"❌ 文档处理失败: {e}")

def main():
    """主演示函数"""
    print("🌐 DTrans - 香港上市公司文件智能翻译")
    print("演示脚本")
    print("=" * 60)
    
    # 演示文档解析
    demo_document_parser()
    
    # 演示翻译功能
    demo_translation()

if __name__ == "__main__":
    main()
