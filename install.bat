@echo off
echo 🚀 DTrans应用自动安装脚本
echo ================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ 检测到Python版本:
python --version
echo.

REM 创建虚拟环境
echo 📦 创建虚拟环境...
if exist dtrans_env (
    echo ⚠️  虚拟环境已存在，跳过创建
) else (
    python -m venv dtrans_env
    if errorlevel 1 (
        echo ❌ 创建虚拟环境失败
        pause
        exit /b 1
    )
    echo ✅ 虚拟环境创建成功
)
echo.

REM 激活虚拟环境并安装依赖
echo 📥 安装依赖包...
call dtrans_env\Scripts\activate.bat && pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 安装依赖失败
    pause
    exit /b 1
)
echo ✅ 依赖安装完成
echo.

REM 创建.env文件
echo 🔧 配置环境变量...
if exist .env (
    echo ⚠️  .env文件已存在，跳过创建
) else (
    copy .env.example .env >nul
    echo ✅ 已创建.env文件
    echo ⚠️  请编辑.env文件并设置您的OpenRouter API密钥
)
echo.

REM 运行测试
echo 🧪 运行测试...
call dtrans_env\Scripts\activate.bat && python test_app.py
echo.

echo 🎉 安装完成！
echo.
echo 📝 下一步操作：
echo 1. 编辑.env文件，设置您的OpenRouter API密钥
echo 2. 运行start_app.bat启动应用
echo.
pause
