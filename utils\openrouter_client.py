"""
OpenRouter API客户端
用于获取可用模型列表和执行翻译任务
动态获取最新、最完整的模型列表
"""

import os
import requests
import time
from typing import List, Dict, Optional
from dotenv import load_dotenv

load_dotenv()


class OpenRouterClient:
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        self.base_url = "https://openrouter.ai/api/v1"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/dtrans-app",
            "X-Title": "DTrans - Document Translation App",
        }
        self._models_cache = None
        self._cache_timestamp = 0
        self._cache_duration = 300  # 5分钟缓存

    def get_available_models(self, force_refresh: bool = False) -> List[Dict]:
        """
        获取所有可用的模型列表

        Args:
            force_refresh: 是否强制刷新模型列表

        Returns:
            完整的模型列表，包含所有可用模型
        """
        # 检查缓存
        current_time = time.time()
        if (
            not force_refresh
            and self._models_cache
            and current_time - self._cache_timestamp < self._cache_duration
        ):
            print("📋 使用缓存的模型列表")
            return self._models_cache

        try:
            print("🔄 正在获取最新的模型列表...")

            response = requests.get(
                f"{self.base_url}/models",
                headers=self.headers,
                timeout=60,  # 增加超时时间
            )
            response.raise_for_status()
            models_data = response.json()

            raw_models = models_data.get("data", [])
            print(f"✅ 成功获取 {len(raw_models)} 个原始模型")

            # 处理所有模型，只过滤掉明显不适合的类型
            all_models = []
            for model in raw_models:
                model_id = model.get("id", "")
                model_name = model.get("name", model_id)

                # 跳过明显不适合翻译的模型类型
                skip_keywords = [
                    "embedding",
                    "whisper",
                    "tts",
                    "dall-e",
                    "midjourney",
                    "stable-diffusion",
                    "flux",
                    "imagen",
                    "playground",
                    "rerank",
                    "clip",
                    "bark",
                ]

                if any(keyword in model_id.lower() for keyword in skip_keywords):
                    continue

                # 增强模型信息
                model_info = {
                    "id": model_id,
                    "name": model_name,
                    "description": model.get("description", ""),
                    "context_length": model.get("context_length", 0),
                    "pricing": model.get("pricing", {}),
                    "top_provider": model.get("top_provider", {}),
                    "per_request_limits": model.get("per_request_limits", {}),
                    "architecture": model.get("architecture", {}),
                    "modality": model.get("modality", "text"),
                    "created": model.get("created", 0),
                }

                # 智能标签分配
                model_info["tags"] = self._assign_model_tags(
                    model_id, model_name, model.get("description", "")
                )

                all_models.append(model_info)

            # 智能排序
            sorted_models = self._sort_models_by_relevance(all_models)

            # 更新缓存
            self._models_cache = sorted_models
            self._cache_timestamp = current_time

            print(f"📊 处理后可用模型: {len(sorted_models)} 个")
            return sorted_models

        except requests.exceptions.Timeout:
            print("⚠️ 获取模型列表超时，使用备用模型")
            return self._get_fallback_models()
        except requests.exceptions.RequestException as e:
            print(f"⚠️ 网络请求失败: {e}")
            return self._get_fallback_models()
        except Exception as e:
            print(f"❌ 获取模型列表失败: {e}")
            return self._get_fallback_models()

    def _assign_model_tags(
        self, model_id: str, model_name: str, description: str
    ) -> List[str]:
        """智能分配模型标签"""
        tags = []
        model_id_lower = model_id.lower()
        model_name_lower = model_name.lower()
        description_lower = description.lower()

        # 基于模型ID和名称的标签
        if "claude" in model_id_lower:
            tags.extend(["高质量", "长上下文", "推理能力强"])
            if "opus" in model_id_lower:
                tags.append("旗舰模型")
            elif "sonnet" in model_id_lower:
                tags.append("平衡性能")
            elif "haiku" in model_id_lower:
                tags.append("快速响应")

        elif "gpt-4" in model_id_lower:
            tags.extend(["多模态", "代码能力", "推理能力"])
            if "turbo" in model_id_lower:
                tags.append("优化版本")
            if "vision" in model_id_lower or "4o" in model_id_lower:
                tags.append("视觉能力")

        elif "gpt-3.5" in model_id_lower:
            tags.extend(["快速", "经济", "通用"])
            if "turbo" in model_id_lower:
                tags.append("优化版本")

        elif "gemini" in model_id_lower:
            tags.extend(["多模态", "长上下文", "Google"])
            if "pro" in model_id_lower:
                tags.append("专业版")
            elif "flash" in model_id_lower:
                tags.append("快速版")

        elif "llama" in model_id_lower:
            tags.extend(["开源", "Meta", "通用"])
            if "instruct" in model_id_lower:
                tags.append("指令优化")
            if "chat" in model_id_lower:
                tags.append("对话优化")

        elif "mistral" in model_id_lower:
            tags.extend(["开源", "欧洲", "通用"])
            if "large" in model_id_lower:
                tags.append("大模型")
            elif "medium" in model_id_lower:
                tags.append("中等模型")
            elif "small" in model_id_lower:
                tags.append("小模型")

        elif "cohere" in model_id_lower:
            tags.extend(["企业级", "Cohere", "通用"])
            if "command" in model_id_lower:
                tags.append("指令优化")

        elif "ai21" in model_id_lower:
            tags.extend(["企业级", "AI21", "通用"])

        # 基于描述的额外标签
        if "multilingual" in description_lower or "多语言" in description_lower:
            tags.append("多语言")

        if "reasoning" in description_lower or "推理" in description_lower:
            tags.append("推理专长")

        if "code" in description_lower or "代码" in description_lower:
            tags.append("代码能力")

        if "fast" in description_lower or "quick" in description_lower:
            tags.append("快速")

        if "efficient" in description_lower or "efficient" in model_name_lower:
            tags.append("高效")

        # 如果没有标签，添加通用标签
        if not tags:
            tags.append("通用")

        return list(set(tags))  # 去重

    def _sort_models_by_relevance(self, models: List[Dict]) -> List[Dict]:
        """按相关性和质量排序模型"""

        def model_priority(model):
            model_id = model["id"].lower()

            # 第一优先级：最新的高质量模型
            if any(
                x in model_id
                for x in ["claude-3.7", "claude-3.5", "gpt-4.1", "gpt-4o", "gemini-2"]
            ):
                return 1

            # 第二优先级：经典高质量模型
            elif any(x in model_id for x in ["claude-3", "gpt-4", "gemini-pro"]):
                return 2

            # 第三优先级：快速经济模型
            elif any(
                x in model_id for x in ["gpt-3.5", "gemini-flash", "claude-haiku"]
            ):
                return 3

            # 第四优先级：开源模型
            elif any(x in model_id for x in ["llama", "mistral", "mixtral"]):
                return 4

            # 第五优先级：其他模型
            else:
                return 5

        return sorted(models, key=model_priority)

    def _get_fallback_models(self) -> List[Dict]:
        """获取备用模型列表（当API不可用时）"""
        print("📋 使用备用模型列表")
        return [
            {
                "id": "anthropic/claude-3-sonnet-20240229",
                "name": "Claude 3 Sonnet",
                "description": "高质量的对话和文本生成模型，擅长复杂推理和长文本处理",
                "context_length": 200000,
                "pricing": {},
                "tags": ["高质量", "长上下文", "推理能力强"],
            },
            {
                "id": "openai/gpt-4-turbo",
                "name": "GPT-4 Turbo",
                "description": "OpenAI的高性能模型，支持多模态输入和复杂任务",
                "context_length": 128000,
                "pricing": {},
                "tags": ["多模态", "代码能力", "推理能力"],
            },
            {
                "id": "openai/gpt-3.5-turbo",
                "name": "GPT-3.5 Turbo",
                "description": "快速且经济的模型，适合大多数文本处理任务",
                "context_length": 16000,
                "pricing": {},
                "tags": ["快速", "经济", "通用"],
            },
        ]

    def refresh_models_cache(self) -> List[Dict]:
        """强制刷新模型缓存"""
        return self.get_available_models(force_refresh=True)

    def translate_text(
        self, text: str, model_id: str, system_message: str, reference_context: str = ""
    ) -> str:
        """
        使用指定模型翻译文本

        Args:
            text: 待翻译的文本
            model_id: 模型ID
            system_message: 系统提示词
            reference_context: 参考文件的上下文信息

        Returns:
            翻译后的文本
        """
        try:
            # 构建完整的提示词
            full_prompt = f"""
{system_message}

参考翻译上下文：
{reference_context}

请翻译以下文本：
{text}

翻译结果：
"""

            payload = {
                "model": model_id,
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": full_prompt},
                ],
                "temperature": 0.3,
                "max_tokens": 4000,
            }

            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=120,
            )
            response.raise_for_status()

            result = response.json()
            translated_text = result["choices"][0]["message"]["content"].strip()

            return translated_text

        except Exception as e:
            print(f"翻译失败: {e}")
            return f"翻译错误: {str(e)}"
