"""
OpenRouter API客户端
用于获取可用模型列表和执行翻译任务
"""

import os
import requests
from typing import List, Dict, Optional
from dotenv import load_dotenv

load_dotenv()


class OpenRouterClient:
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        self.base_url = "https://openrouter.ai/api/v1"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://github.com/your-repo",  # 可选：用于统计
            "X-Title": "DTrans - Document Translation App",
        }

    def get_available_models(self) -> List[Dict]:
        """获取所有可用的模型列表"""
        try:
            response = requests.get(
                f"{self.base_url}/models", headers=self.headers, timeout=30
            )
            response.raise_for_status()
            models = response.json()

            # 过滤出适合翻译的模型
            translation_models = []
            for model in models.get("data", []):
                model_id = model.get("id", "")
                model_name = model.get("name", model_id)

                # 优先选择一些知名的翻译效果好的模型
                if any(
                    provider in model_id.lower()
                    for provider in [
                        "anthropic",
                        "openai",
                        "google",
                        "meta",
                        "mistral",
                        "cohere",
                        "ai21",
                    ]
                ):
                    # 增强模型信息
                    model_info = {
                        "id": model_id,
                        "name": model_name,
                        "description": model.get("description", ""),
                        "context_length": model.get("context_length", 0),
                        "pricing": model.get("pricing", {}),
                        "top_provider": model.get("top_provider", {}),
                        "per_request_limits": model.get("per_request_limits", {}),
                    }

                    # 添加模型类型标签
                    if "claude" in model_id.lower():
                        model_info["tags"] = ["高质量", "长上下文", "推理能力强"]
                    elif "gpt-4" in model_id.lower():
                        model_info["tags"] = ["多模态", "代码能力", "推理能力"]
                    elif "gpt-3.5" in model_id.lower():
                        model_info["tags"] = ["快速", "经济", "通用"]
                    elif "gemini" in model_id.lower():
                        model_info["tags"] = ["多模态", "长上下文", "Google"]
                    elif "llama" in model_id.lower():
                        model_info["tags"] = ["开源", "Meta", "通用"]
                    else:
                        model_info["tags"] = ["通用"]

                    translation_models.append(model_info)

            # 按质量和受欢迎程度排序
            def model_priority(model):
                model_id = model["id"].lower()
                # Claude和GPT-4优先级最高
                if "claude-3" in model_id:
                    return 1
                elif "gpt-4" in model_id:
                    return 2
                elif "gemini" in model_id:
                    return 3
                elif "gpt-3.5" in model_id:
                    return 4
                else:
                    return 5

            return sorted(translation_models, key=model_priority)

        except Exception as e:
            print(f"获取模型列表失败: {e}")
            # 返回一些默认模型
            return [
                {
                    "id": "anthropic/claude-3-sonnet-20240229",
                    "name": "Claude 3 Sonnet",
                    "description": "高质量的对话和文本生成模型，擅长复杂推理和长文本处理",
                    "context_length": 200000,
                    "pricing": {},
                    "tags": ["高质量", "长上下文", "推理能力强"],
                },
                {
                    "id": "openai/gpt-4-turbo",
                    "name": "GPT-4 Turbo",
                    "description": "OpenAI的高性能模型，支持多模态输入和复杂任务",
                    "context_length": 128000,
                    "pricing": {},
                    "tags": ["多模态", "代码能力", "推理能力"],
                },
                {
                    "id": "openai/gpt-3.5-turbo",
                    "name": "GPT-3.5 Turbo",
                    "description": "快速且经济的模型，适合大多数文本处理任务",
                    "context_length": 16000,
                    "pricing": {},
                    "tags": ["快速", "经济", "通用"],
                },
            ]

    def translate_text(
        self, text: str, model_id: str, system_message: str, reference_context: str = ""
    ) -> str:
        """
        使用指定模型翻译文本

        Args:
            text: 待翻译的文本
            model_id: 模型ID
            system_message: 系统提示词
            reference_context: 参考文件的上下文信息

        Returns:
            翻译后的文本
        """
        try:
            # 构建完整的提示词
            full_prompt = f"""
{system_message}

参考翻译上下文：
{reference_context}

请翻译以下文本：
{text}

翻译结果：
"""

            payload = {
                "model": model_id,
                "messages": [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": full_prompt},
                ],
                "temperature": 0.3,
                "max_tokens": 4000,
            }

            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=120,
            )
            response.raise_for_status()

            result = response.json()
            translated_text = result["choices"][0]["message"]["content"].strip()

            return translated_text

        except Exception as e:
            print(f"翻译失败: {e}")
            return f"翻译错误: {str(e)}"
