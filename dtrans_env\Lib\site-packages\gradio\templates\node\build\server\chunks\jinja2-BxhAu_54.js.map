{"version": 3, "file": "jinja2-BxhAu_54.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/jinja2.js"], "sourcesContent": ["var keywords = [\n  \"and\",\n  \"as\",\n  \"block\",\n  \"endblock\",\n  \"by\",\n  \"cycle\",\n  \"debug\",\n  \"else\",\n  \"elif\",\n  \"extends\",\n  \"filter\",\n  \"endfilter\",\n  \"firstof\",\n  \"do\",\n  \"for\",\n  \"endfor\",\n  \"if\",\n  \"endif\",\n  \"ifchanged\",\n  \"endifchanged\",\n  \"ifequal\",\n  \"endifequal\",\n  \"ifnotequal\",\n  \"set\",\n  \"raw\",\n  \"endraw\",\n  \"endifnotequal\",\n  \"in\",\n  \"include\",\n  \"load\",\n  \"not\",\n  \"now\",\n  \"or\",\n  \"parsed\",\n  \"regroup\",\n  \"reversed\",\n  \"spaceless\",\n  \"call\",\n  \"endcall\",\n  \"macro\",\n  \"endmacro\",\n  \"endspaceless\",\n  \"ssi\",\n  \"templatetag\",\n  \"openblock\",\n  \"closeblock\",\n  \"openvariable\",\n  \"closevariable\",\n  \"without\",\n  \"context\",\n  \"openbrace\",\n  \"closebrace\",\n  \"opencomment\",\n  \"closecomment\",\n  \"widthratio\",\n  \"url\",\n  \"with\",\n  \"endwith\",\n  \"get_current_language\",\n  \"trans\",\n  \"endtrans\",\n  \"noop\",\n  \"blocktrans\",\n  \"endblocktrans\",\n  \"get_available_languages\",\n  \"get_current_language_bidi\",\n  \"pluralize\",\n  \"autoescape\",\n  \"endautoescape\"\n], operator = /^[+\\-*&%=<>!?|~^]/, sign = /^[:\\[\\(\\{]/, atom = [\"true\", \"false\"], number = /^(\\d[+\\-\\*\\/])?\\d+(\\.\\d+)?/;\nkeywords = new RegExp(\"((\" + keywords.join(\")|(\") + \"))\\\\b\");\natom = new RegExp(\"((\" + atom.join(\")|(\") + \"))\\\\b\");\nfunction tokenBase(stream, state) {\n  var ch = stream.peek();\n  if (state.incomment) {\n    if (!stream.skipTo(\"#}\")) {\n      stream.skipToEnd();\n    } else {\n      stream.eatWhile(/\\#|}/);\n      state.incomment = false;\n    }\n    return \"comment\";\n  } else if (state.intag) {\n    if (state.operator) {\n      state.operator = false;\n      if (stream.match(atom)) {\n        return \"atom\";\n      }\n      if (stream.match(number)) {\n        return \"number\";\n      }\n    }\n    if (state.sign) {\n      state.sign = false;\n      if (stream.match(atom)) {\n        return \"atom\";\n      }\n      if (stream.match(number)) {\n        return \"number\";\n      }\n    }\n    if (state.instring) {\n      if (ch == state.instring) {\n        state.instring = false;\n      }\n      stream.next();\n      return \"string\";\n    } else if (ch == \"'\" || ch == '\"') {\n      state.instring = ch;\n      stream.next();\n      return \"string\";\n    } else if (state.inbraces > 0 && ch == \")\") {\n      stream.next();\n      state.inbraces--;\n    } else if (ch == \"(\") {\n      stream.next();\n      state.inbraces++;\n    } else if (state.inbrackets > 0 && ch == \"]\") {\n      stream.next();\n      state.inbrackets--;\n    } else if (ch == \"[\") {\n      stream.next();\n      state.inbrackets++;\n    } else if (!state.lineTag && (stream.match(state.intag + \"}\") || stream.eat(\"-\") && stream.match(state.intag + \"}\"))) {\n      state.intag = false;\n      return \"tag\";\n    } else if (stream.match(operator)) {\n      state.operator = true;\n      return \"operator\";\n    } else if (stream.match(sign)) {\n      state.sign = true;\n    } else {\n      if (stream.column() == 1 && state.lineTag && stream.match(keywords)) {\n        return \"keyword\";\n      }\n      if (stream.eat(\" \") || stream.sol()) {\n        if (stream.match(keywords)) {\n          return \"keyword\";\n        }\n        if (stream.match(atom)) {\n          return \"atom\";\n        }\n        if (stream.match(number)) {\n          return \"number\";\n        }\n        if (stream.sol()) {\n          stream.next();\n        }\n      } else {\n        stream.next();\n      }\n    }\n    return \"variable\";\n  } else if (stream.eat(\"{\")) {\n    if (stream.eat(\"#\")) {\n      state.incomment = true;\n      if (!stream.skipTo(\"#}\")) {\n        stream.skipToEnd();\n      } else {\n        stream.eatWhile(/\\#|}/);\n        state.incomment = false;\n      }\n      return \"comment\";\n    } else if (ch = stream.eat(/\\{|%/)) {\n      state.intag = ch;\n      state.inbraces = 0;\n      state.inbrackets = 0;\n      if (ch == \"{\") {\n        state.intag = \"}\";\n      }\n      stream.eat(\"-\");\n      return \"tag\";\n    }\n  } else if (stream.eat(\"#\")) {\n    if (stream.peek() == \"#\") {\n      stream.skipToEnd();\n      return \"comment\";\n    } else if (!stream.eol()) {\n      state.intag = true;\n      state.lineTag = true;\n      state.inbraces = 0;\n      state.inbrackets = 0;\n      return \"tag\";\n    }\n  }\n  stream.next();\n}\nconst jinja2 = {\n  name: \"jinja2\",\n  startState: function() {\n    return { tokenize: tokenBase, inbrackets: 0, inbraces: 0 };\n  },\n  token: function(stream, state) {\n    var style = state.tokenize(stream, state);\n    if (stream.eol() && state.lineTag && !state.instring && state.inbraces == 0 && state.inbrackets == 0) {\n      state.intag = false;\n      state.lineTag = false;\n    }\n    return style;\n  },\n  languageData: {\n    commentTokens: { block: { open: \"{#\", close: \"#}\", line: \"##\" } }\n  }\n};\nexport {\n  jinja2\n};\n"], "names": [], "mappings": "AAAA,IAAI,QAAQ,GAAG;AACf,EAAE,KAAK;AACP,EAAE,IAAI;AACN,EAAE,OAAO;AACT,EAAE,UAAU;AACZ,EAAE,IAAI;AACN,EAAE,OAAO;AACT,EAAE,OAAO;AACT,EAAE,MAAM;AACR,EAAE,MAAM;AACR,EAAE,SAAS;AACX,EAAE,QAAQ;AACV,EAAE,WAAW;AACb,EAAE,SAAS;AACX,EAAE,IAAI;AACN,EAAE,KAAK;AACP,EAAE,QAAQ;AACV,EAAE,IAAI;AACN,EAAE,OAAO;AACT,EAAE,WAAW;AACb,EAAE,cAAc;AAChB,EAAE,SAAS;AACX,EAAE,YAAY;AACd,EAAE,YAAY;AACd,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,QAAQ;AACV,EAAE,eAAe;AACjB,EAAE,IAAI;AACN,EAAE,SAAS;AACX,EAAE,MAAM;AACR,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,IAAI;AACN,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,UAAU;AACZ,EAAE,WAAW;AACb,EAAE,MAAM;AACR,EAAE,SAAS;AACX,EAAE,OAAO;AACT,EAAE,UAAU;AACZ,EAAE,cAAc;AAChB,EAAE,KAAK;AACP,EAAE,aAAa;AACf,EAAE,WAAW;AACb,EAAE,YAAY;AACd,EAAE,cAAc;AAChB,EAAE,eAAe;AACjB,EAAE,SAAS;AACX,EAAE,SAAS;AACX,EAAE,WAAW;AACb,EAAE,YAAY;AACd,EAAE,aAAa;AACf,EAAE,cAAc;AAChB,EAAE,YAAY;AACd,EAAE,KAAK;AACP,EAAE,MAAM;AACR,EAAE,SAAS;AACX,EAAE,sBAAsB;AACxB,EAAE,OAAO;AACT,EAAE,UAAU;AACZ,EAAE,MAAM;AACR,EAAE,YAAY;AACd,EAAE,eAAe;AACjB,EAAE,yBAAyB;AAC3B,EAAE,2BAA2B;AAC7B,EAAE,WAAW;AACb,EAAE,YAAY;AACd,EAAE,eAAe;AACjB,CAAC,EAAE,QAAQ,GAAG,mBAAmB,EAAE,IAAI,GAAG,YAAY,EAAE,IAAI,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,GAAG,4BAA4B,CAAC;AACxH,QAAQ,GAAG,IAAI,MAAM,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,CAAC;AAC7D,IAAI,GAAG,IAAI,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,OAAO,CAAC,CAAC;AACrD,SAAS,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE;AAClC,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;AACzB,EAAE,IAAI,KAAK,CAAC,SAAS,EAAE;AACvB,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;AAC9B,MAAM,MAAM,CAAC,SAAS,EAAE,CAAC;AACzB,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAC9B,MAAM,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;AAC9B,KAAK;AACL,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG,MAAM,IAAI,KAAK,CAAC,KAAK,EAAE;AAC1B,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;AACxB,MAAM,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC7B,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;AAC9B,QAAQ,OAAO,MAAM,CAAC;AACtB,OAAO;AACP,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;AAChC,QAAQ,OAAO,QAAQ,CAAC;AACxB,OAAO;AACP,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,IAAI,EAAE;AACpB,MAAM,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;AACzB,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;AAC9B,QAAQ,OAAO,MAAM,CAAC;AACtB,OAAO;AACP,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;AAChC,QAAQ,OAAO,QAAQ,CAAC;AACxB,OAAO;AACP,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;AACxB,MAAM,IAAI,EAAE,IAAI,KAAK,CAAC,QAAQ,EAAE;AAChC,QAAQ,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC;AAC/B,OAAO;AACP,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;AACpB,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK,MAAM,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,EAAE;AACvC,MAAM,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;AAC1B,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;AACpB,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK,MAAM,IAAI,KAAK,CAAC,QAAQ,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE;AAChD,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;AACpB,MAAM,KAAK,CAAC,QAAQ,EAAE,CAAC;AACvB,KAAK,MAAM,IAAI,EAAE,IAAI,GAAG,EAAE;AAC1B,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;AACpB,MAAM,KAAK,CAAC,QAAQ,EAAE,CAAC;AACvB,KAAK,MAAM,IAAI,KAAK,CAAC,UAAU,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE;AAClD,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;AACpB,MAAM,KAAK,CAAC,UAAU,EAAE,CAAC;AACzB,KAAK,MAAM,IAAI,EAAE,IAAI,GAAG,EAAE;AAC1B,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;AACpB,MAAM,KAAK,CAAC,UAAU,EAAE,CAAC;AACzB,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,EAAE;AAC1H,MAAM,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AAC1B,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;AACvC,MAAM,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC5B,MAAM,OAAO,UAAU,CAAC;AACxB,KAAK,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;AACnC,MAAM,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;AACxB,KAAK,MAAM;AACX,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,KAAK,CAAC,OAAO,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;AAC3E,QAAQ,OAAO,SAAS,CAAC;AACzB,OAAO;AACP,MAAM,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,GAAG,EAAE,EAAE;AAC3C,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;AACpC,UAAU,OAAO,SAAS,CAAC;AAC3B,SAAS;AACT,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;AAChC,UAAU,OAAO,MAAM,CAAC;AACxB,SAAS;AACT,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;AAClC,UAAU,OAAO,QAAQ,CAAC;AAC1B,SAAS;AACT,QAAQ,IAAI,MAAM,CAAC,GAAG,EAAE,EAAE;AAC1B,UAAU,MAAM,CAAC,IAAI,EAAE,CAAC;AACxB,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;AACtB,OAAO;AACP,KAAK;AACL,IAAI,OAAO,UAAU,CAAC;AACtB,GAAG,MAAM,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC9B,IAAI,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACzB,MAAM,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;AAC7B,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;AAChC,QAAQ,MAAM,CAAC,SAAS,EAAE,CAAC;AAC3B,OAAO,MAAM;AACb,QAAQ,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AAChC,QAAQ,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;AAChC,OAAO;AACP,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK,MAAM,IAAI,EAAE,GAAG,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;AACxC,MAAM,KAAK,CAAC,KAAK,GAAG,EAAE,CAAC;AACvB,MAAM,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;AACzB,MAAM,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC;AAC3B,MAAM,IAAI,EAAE,IAAI,GAAG,EAAE;AACrB,QAAQ,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC;AAC1B,OAAO;AACP,MAAM,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACtB,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,GAAG,MAAM,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC9B,IAAI,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE;AAC9B,MAAM,MAAM,CAAC,SAAS,EAAE,CAAC;AACzB,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE;AAC9B,MAAM,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;AACzB,MAAM,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3B,MAAM,KAAK,CAAC,QAAQ,GAAG,CAAC,CAAC;AACzB,MAAM,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC;AAC3B,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,GAAG;AACH,EAAE,MAAM,CAAC,IAAI,EAAE,CAAC;AAChB,CAAC;AACI,MAAC,MAAM,GAAG;AACf,EAAE,IAAI,EAAE,QAAQ;AAChB,EAAE,UAAU,EAAE,WAAW;AACzB,IAAI,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;AAC/D,GAAG;AACH,EAAE,KAAK,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE;AACjC,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AAC9C,IAAI,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,IAAI,CAAC,IAAI,KAAK,CAAC,UAAU,IAAI,CAAC,EAAE;AAC1G,MAAM,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AAC1B,MAAM,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC;AAC5B,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,aAAa,EAAE,EAAE,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;AACrE,GAAG;AACH;;;;"}