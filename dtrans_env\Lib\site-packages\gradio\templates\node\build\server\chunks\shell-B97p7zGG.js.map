{"version": 3, "file": "shell-B97p7zGG.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/shell.js"], "sourcesContent": ["var words = {};\nfunction define(style, dict) {\n  for (var i = 0; i < dict.length; i++) {\n    words[dict[i]] = style;\n  }\n}\nvar commonAtoms = [\"true\", \"false\"];\nvar commonKeywords = [\n  \"if\",\n  \"then\",\n  \"do\",\n  \"else\",\n  \"elif\",\n  \"while\",\n  \"until\",\n  \"for\",\n  \"in\",\n  \"esac\",\n  \"fi\",\n  \"fin\",\n  \"fil\",\n  \"done\",\n  \"exit\",\n  \"set\",\n  \"unset\",\n  \"export\",\n  \"function\"\n];\nvar commonCommands = [\n  \"ab\",\n  \"awk\",\n  \"bash\",\n  \"beep\",\n  \"cat\",\n  \"cc\",\n  \"cd\",\n  \"chown\",\n  \"chmod\",\n  \"chroot\",\n  \"clear\",\n  \"cp\",\n  \"curl\",\n  \"cut\",\n  \"diff\",\n  \"echo\",\n  \"find\",\n  \"gawk\",\n  \"gcc\",\n  \"get\",\n  \"git\",\n  \"grep\",\n  \"hg\",\n  \"kill\",\n  \"killall\",\n  \"ln\",\n  \"ls\",\n  \"make\",\n  \"mkdir\",\n  \"openssl\",\n  \"mv\",\n  \"nc\",\n  \"nl\",\n  \"node\",\n  \"npm\",\n  \"ping\",\n  \"ps\",\n  \"restart\",\n  \"rm\",\n  \"rmdir\",\n  \"sed\",\n  \"service\",\n  \"sh\",\n  \"shopt\",\n  \"shred\",\n  \"source\",\n  \"sort\",\n  \"sleep\",\n  \"ssh\",\n  \"start\",\n  \"stop\",\n  \"su\",\n  \"sudo\",\n  \"svn\",\n  \"tee\",\n  \"telnet\",\n  \"top\",\n  \"touch\",\n  \"vi\",\n  \"vim\",\n  \"wall\",\n  \"wc\",\n  \"wget\",\n  \"who\",\n  \"write\",\n  \"yes\",\n  \"zsh\"\n];\ndefine(\"atom\", commonAtoms);\ndefine(\"keyword\", commonKeywords);\ndefine(\"builtin\", commonCommands);\nfunction tokenBase(stream, state) {\n  if (stream.eatSpace())\n    return null;\n  var sol = stream.sol();\n  var ch = stream.next();\n  if (ch === \"\\\\\") {\n    stream.next();\n    return null;\n  }\n  if (ch === \"'\" || ch === '\"' || ch === \"`\") {\n    state.tokens.unshift(tokenString(ch, ch === \"`\" ? \"quote\" : \"string\"));\n    return tokenize(stream, state);\n  }\n  if (ch === \"#\") {\n    if (sol && stream.eat(\"!\")) {\n      stream.skipToEnd();\n      return \"meta\";\n    }\n    stream.skipToEnd();\n    return \"comment\";\n  }\n  if (ch === \"$\") {\n    state.tokens.unshift(tokenDollar);\n    return tokenize(stream, state);\n  }\n  if (ch === \"+\" || ch === \"=\") {\n    return \"operator\";\n  }\n  if (ch === \"-\") {\n    stream.eat(\"-\");\n    stream.eatWhile(/\\w/);\n    return \"attribute\";\n  }\n  if (ch == \"<\") {\n    if (stream.match(\"<<\"))\n      return \"operator\";\n    var heredoc = stream.match(/^<-?\\s*(?:['\"]([^'\"]*)['\"]|([^'\"\\s]*))/);\n    if (heredoc) {\n      state.tokens.unshift(tokenHeredoc(heredoc[1] || heredoc[2]));\n      return \"string.special\";\n    }\n  }\n  if (/\\d/.test(ch)) {\n    stream.eatWhile(/\\d/);\n    if (stream.eol() || !/\\w/.test(stream.peek())) {\n      return \"number\";\n    }\n  }\n  stream.eatWhile(/[\\w-]/);\n  var cur = stream.current();\n  if (stream.peek() === \"=\" && /\\w+/.test(cur))\n    return \"def\";\n  return words.hasOwnProperty(cur) ? words[cur] : null;\n}\nfunction tokenString(quote, style) {\n  var close = quote == \"(\" ? \")\" : quote == \"{\" ? \"}\" : quote;\n  return function(stream, state) {\n    var next, escaped = false;\n    while ((next = stream.next()) != null) {\n      if (next === close && !escaped) {\n        state.tokens.shift();\n        break;\n      } else if (next === \"$\" && !escaped && quote !== \"'\" && stream.peek() != close) {\n        escaped = true;\n        stream.backUp(1);\n        state.tokens.unshift(tokenDollar);\n        break;\n      } else if (!escaped && quote !== close && next === quote) {\n        state.tokens.unshift(tokenString(quote, style));\n        return tokenize(stream, state);\n      } else if (!escaped && /['\"]/.test(next) && !/['\"]/.test(quote)) {\n        state.tokens.unshift(tokenStringStart(next, \"string\"));\n        stream.backUp(1);\n        break;\n      }\n      escaped = !escaped && next === \"\\\\\";\n    }\n    return style;\n  };\n}\nfunction tokenStringStart(quote, style) {\n  return function(stream, state) {\n    state.tokens[0] = tokenString(quote, style);\n    stream.next();\n    return tokenize(stream, state);\n  };\n}\nvar tokenDollar = function(stream, state) {\n  if (state.tokens.length > 1)\n    stream.eat(\"$\");\n  var ch = stream.next();\n  if (/['\"({]/.test(ch)) {\n    state.tokens[0] = tokenString(ch, ch == \"(\" ? \"quote\" : ch == \"{\" ? \"def\" : \"string\");\n    return tokenize(stream, state);\n  }\n  if (!/\\d/.test(ch))\n    stream.eatWhile(/\\w/);\n  state.tokens.shift();\n  return \"def\";\n};\nfunction tokenHeredoc(delim) {\n  return function(stream, state) {\n    if (stream.sol() && stream.string == delim)\n      state.tokens.shift();\n    stream.skipToEnd();\n    return \"string.special\";\n  };\n}\nfunction tokenize(stream, state) {\n  return (state.tokens[0] || tokenBase)(stream, state);\n}\nconst shell = {\n  name: \"shell\",\n  startState: function() {\n    return { tokens: [] };\n  },\n  token: function(stream, state) {\n    return tokenize(stream, state);\n  },\n  languageData: {\n    autocomplete: commonAtoms.concat(commonKeywords, commonCommands),\n    closeBrackets: { brackets: [\"(\", \"[\", \"{\", \"'\", '\"', \"`\"] },\n    commentTokens: { line: \"#\" }\n  }\n};\nexport {\n  shell\n};\n"], "names": [], "mappings": "AAAA,IAAI,KAAK,GAAG,EAAE,CAAC;AACf,SAAS,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE;AAC7B,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAC3B,GAAG;AACH,CAAC;AACD,IAAI,WAAW,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AACpC,IAAI,cAAc,GAAG;AACrB,EAAE,IAAI;AACN,EAAE,MAAM;AACR,EAAE,IAAI;AACN,EAAE,MAAM;AACR,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAE,OAAO;AACT,EAAE,KAAK;AACP,EAAE,IAAI;AACN,EAAE,MAAM;AACR,EAAE,IAAI;AACN,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,MAAM;AACR,EAAE,MAAM;AACR,EAAE,KAAK;AACP,EAAE,OAAO;AACT,EAAE,QAAQ;AACV,EAAE,UAAU;AACZ,CAAC,CAAC;AACF,IAAI,cAAc,GAAG;AACrB,EAAE,IAAI;AACN,EAAE,KAAK;AACP,EAAE,MAAM;AACR,EAAE,MAAM;AACR,EAAE,KAAK;AACP,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,OAAO;AACT,EAAE,OAAO;AACT,EAAE,QAAQ;AACV,EAAE,OAAO;AACT,EAAE,IAAI;AACN,EAAE,MAAM;AACR,EAAE,KAAK;AACP,EAAE,MAAM;AACR,EAAE,MAAM;AACR,EAAE,MAAM;AACR,EAAE,MAAM;AACR,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,MAAM;AACR,EAAE,IAAI;AACN,EAAE,MAAM;AACR,EAAE,SAAS;AACX,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAE,SAAS;AACX,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,MAAM;AACR,EAAE,KAAK;AACP,EAAE,MAAM;AACR,EAAE,IAAI;AACN,EAAE,SAAS;AACX,EAAE,IAAI;AACN,EAAE,OAAO;AACT,EAAE,KAAK;AACP,EAAE,SAAS;AACX,EAAE,IAAI;AACN,EAAE,OAAO;AACT,EAAE,OAAO;AACT,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAE,KAAK;AACP,EAAE,OAAO;AACT,EAAE,MAAM;AACR,EAAE,IAAI;AACN,EAAE,MAAM;AACR,EAAE,KAAK;AACP,EAAE,KAAK;AACP,EAAE,QAAQ;AACV,EAAE,KAAK;AACP,EAAE,OAAO;AACT,EAAE,IAAI;AACN,EAAE,KAAK;AACP,EAAE,MAAM;AACR,EAAE,IAAI;AACN,EAAE,MAAM;AACR,EAAE,KAAK;AACP,EAAE,OAAO;AACT,EAAE,KAAK;AACP,EAAE,KAAK;AACP,CAAC,CAAC;AACF,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAC5B,MAAM,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;AAClC,MAAM,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;AAClC,SAAS,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE;AAClC,EAAE,IAAI,MAAM,CAAC,QAAQ,EAAE;AACvB,IAAI,OAAO,IAAI,CAAC;AAChB,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC,GAAG,EAAE,CAAC;AACzB,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;AACzB,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE;AACnB,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;AAClB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,GAAG,EAAE;AAC9C,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,EAAE,EAAE,KAAK,GAAG,GAAG,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC;AAC3E,IAAI,OAAO,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACnC,GAAG;AACH,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE;AAClB,IAAI,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAChC,MAAM,MAAM,CAAC,SAAS,EAAE,CAAC;AACzB,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK;AACL,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;AACvB,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG;AACH,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE;AAClB,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AACtC,IAAI,OAAO,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACnC,GAAG;AACH,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,KAAK,GAAG,EAAE;AAChC,IAAI,OAAO,UAAU,CAAC;AACtB,GAAG;AACH,EAAE,IAAI,EAAE,KAAK,GAAG,EAAE;AAClB,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACpB,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC1B,IAAI,OAAO,WAAW,CAAC;AACvB,GAAG;AACH,EAAE,IAAI,EAAE,IAAI,GAAG,EAAE;AACjB,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;AAC1B,MAAM,OAAO,UAAU,CAAC;AACxB,IAAI,IAAI,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,wCAAwC,CAAC,CAAC;AACzE,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnE,MAAM,OAAO,gBAAgB,CAAC;AAC9B,KAAK;AACL,GAAG;AACH,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AACrB,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC1B,IAAI,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE;AACnD,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK;AACL,GAAG;AACH,EAAE,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC3B,EAAE,IAAI,GAAG,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;AAC7B,EAAE,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC;AAC9C,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,OAAO,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AACvD,CAAC;AACD,SAAS,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE;AACnC,EAAE,IAAI,KAAK,GAAG,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG,KAAK,CAAC;AAC9D,EAAE,OAAO,SAAS,MAAM,EAAE,KAAK,EAAE;AACjC,IAAI,IAAI,IAAI,EAAE,OAAO,GAAG,KAAK,CAAC;AAC9B,IAAI,OAAO,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE;AAC3C,MAAM,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,OAAO,EAAE;AACtC,QAAQ,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;AAC7B,QAAQ,MAAM;AACd,OAAO,MAAM,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,IAAI,KAAK,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,KAAK,EAAE;AACtF,QAAQ,OAAO,GAAG,IAAI,CAAC;AACvB,QAAQ,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACzB,QAAQ,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AAC1C,QAAQ,MAAM;AACd,OAAO,MAAM,IAAI,CAAC,OAAO,IAAI,KAAK,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE;AAChE,QAAQ,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;AACxD,QAAQ,OAAO,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACvC,OAAO,MAAM,IAAI,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;AACvE,QAAQ,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC/D,QAAQ,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACzB,QAAQ,MAAM;AACd,OAAO;AACP,MAAM,OAAO,GAAG,CAAC,OAAO,IAAI,IAAI,KAAK,IAAI,CAAC;AAC1C,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE;AACxC,EAAE,OAAO,SAAS,MAAM,EAAE,KAAK,EAAE;AACjC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAChD,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;AAClB,IAAI,OAAO,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACnC,GAAG,CAAC;AACJ,CAAC;AACD,IAAI,WAAW,GAAG,SAAS,MAAM,EAAE,KAAK,EAAE;AAC1C,EAAE,IAAI,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC;AAC7B,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACpB,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;AACzB,EAAE,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;AACzB,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,EAAE,EAAE,EAAE,IAAI,GAAG,GAAG,OAAO,GAAG,EAAE,IAAI,GAAG,GAAG,KAAK,GAAG,QAAQ,CAAC,CAAC;AAC1F,IAAI,OAAO,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACnC,GAAG;AACH,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;AACpB,IAAI,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;AACvB,EAAE,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AACF,SAAS,YAAY,CAAC,KAAK,EAAE;AAC7B,EAAE,OAAO,SAAS,MAAM,EAAE,KAAK,EAAE;AACjC,IAAI,IAAI,MAAM,CAAC,GAAG,EAAE,IAAI,MAAM,CAAC,MAAM,IAAI,KAAK;AAC9C,MAAM,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;AAC3B,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;AACvB,IAAI,OAAO,gBAAgB,CAAC;AAC5B,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE;AACjC,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,SAAS,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;AACvD,CAAC;AACI,MAAC,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,UAAU,EAAE,WAAW;AACzB,IAAI,OAAO,EAAE,MAAM,EAAE,EAAE,EAAE,CAAC;AAC1B,GAAG;AACH,EAAE,KAAK,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE;AACjC,IAAI,OAAO,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACnC,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,YAAY,EAAE,WAAW,CAAC,MAAM,CAAC,cAAc,EAAE,cAAc,CAAC;AACpE,IAAI,aAAa,EAAE,EAAE,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAE;AAC/D,IAAI,aAAa,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AAChC,GAAG;AACH;;;;"}