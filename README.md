# DTrans - 香港上市公司文件智能翻译应用

基于Gradio和OpenRouter API的专业文档翻译工具，专门针对香港上市公司披露文件的中英文互译需求。

## ✨ 主要功能

- 📄 **多格式支持**: 支持.docx和.pdf文件的解析和翻译
- 🔍 **智能分析**: 自动分析参考文件，提取翻译规则和术语对照
- 🤖 **多模型选择**: 集成OpenRouter API，支持多种大语言模型
- 🎯 **专业翻译**: 针对香港上市公司文件的专业术语和格式优化
- 📝 **格式保持**: 翻译后保持原文档的格式和结构
- 🌐 **Web界面**: 直观易用的Gradio Web界面

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- OpenRouter API密钥

### 2. 安装依赖

```bash
# 克隆或下载项目到本地
cd DTrans

# 安装Python依赖
pip install -r requirements.txt
```

### 3. 配置API密钥

复制环境变量示例文件：
```bash
cp .env.example .env
```

编辑`.env`文件，添加您的OpenRouter API密钥：
```env
OPENROUTER_API_KEY=your_openrouter_api_key_here
```

> 💡 如何获取OpenRouter API密钥：
> 1. 访问 [OpenRouter官网](https://openrouter.ai/)
> 2. 注册账户并登录
> 3. 在API Keys页面创建新的API密钥
> 4. 复制密钥到.env文件中

### 4. 启动应用

```bash
python app.py
```

应用将在 `http://localhost:7860` 启动。

## 📖 使用指南

### 第一步：准备文件

准备以下三个文件：
1. **参考文件1**: 如中文版的香港上市公司文件
2. **参考文件2**: 对应的英文版文件
3. **目标文件**: 需要翻译的新文档

### 第二步：分析参考文件

1. 在"文件上传与分析"标签页上传两个参考文件
2. 点击"分析参考文件"按钮
3. 系统将自动提取翻译对应关系和专业术语

### 第三步：执行翻译

1. 在"翻译设置与执行"标签页上传目标文件
2. 选择合适的翻译模型（推荐Claude 3 Sonnet或GPT-4）
3. 根据需要调整系统提示词
4. 点击"开始翻译"
5. 等待翻译完成后下载结果

## 🛠️ 技术架构

```
DTrans/
├── app.py                 # 主应用文件
├── requirements.txt       # Python依赖
├── .env.example          # 环境变量示例
├── README.md             # 说明文档
└── utils/                # 工具模块
    ├── __init__.py
    ├── openrouter_client.py    # OpenRouter API客户端
    ├── document_parser.py      # 文档解析器
    └── translator.py           # 翻译引擎
```

### 核心模块说明

- **OpenRouterClient**: 负责与OpenRouter API通信，获取模型列表和执行翻译
- **DocumentParser**: 处理文档解析，支持.docx和.pdf格式
- **DocumentTranslator**: 翻译引擎，整合参考分析和翻译执行

## ⚙️ 配置选项

### 支持的模型

应用自动从OpenRouter获取可用模型，包括但不限于：
- Anthropic Claude 3 Sonnet
- OpenAI GPT-4 Turbo
- OpenAI GPT-3.5 Turbo
- Google Gemini Pro
- Meta Llama 2/3

### 自定义系统提示词

您可以根据具体需求调整系统提示词，例如：

```
你是一个专业的金融文档翻译专家。请根据香港联交所的披露要求，
准确翻译以下内容，确保：
1. 专业术语的准确性
2. 法律条文的严谨性
3. 数字和日期的一致性
4. 格式的规范性
```

## 🔧 故障排除

### 常见问题

1. **API密钥错误**
   - 检查.env文件中的API密钥是否正确
   - 确认OpenRouter账户有足够的余额

2. **文件解析失败**
   - 确认文件格式为.docx或.pdf
   - 检查文件是否损坏或加密

3. **翻译质量不佳**
   - 尝试使用更高质量的模型（如Claude 3 Sonnet）
   - 调整系统提示词，提供更具体的指导
   - 确保参考文件质量良好

4. **内存不足**
   - 对于大文件，考虑分段处理
   - 选择上下文长度更大的模型

### 日志调试

应用会在控制台输出详细的错误信息，有助于诊断问题。

## 📄 许可证

本项目仅供学习和研究使用。使用时请遵守相关API服务商的使用条款。

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📞 支持

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 本工具仅为辅助翻译工具，重要文档的翻译结果建议经过专业人员审核。
