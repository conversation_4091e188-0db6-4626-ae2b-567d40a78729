"""
测试模型搜索功能
验证搜索和过滤功能是否正常工作
"""

from app import DTransApp

def test_model_search():
    """测试模型搜索功能"""
    print("🔍 测试模型搜索功能")
    print("=" * 50)
    
    # 创建应用实例
    app = DTransApp()
    
    # 加载模型列表
    all_models = app.load_models()
    print(f"✅ 加载了 {len(all_models)} 个模型")
    
    # 显示所有模型
    print("\n📋 所有可用模型:")
    for i, model in enumerate(all_models, 1):
        tags = ", ".join(model.get('tags', []))
        print(f"  {i}. {model['name']} ({model['id']})")
        print(f"     标签: {tags}")
        if model.get('description'):
            print(f"     描述: {model['description'][:50]}...")
        print()
    
    # 测试不同的搜索查询
    test_queries = [
        "",  # 空搜索
        "claude",  # 按名称搜索
        "gpt-4",  # 按ID搜索
        "快速",  # 按标签搜索
        "经济",  # 按标签搜索
        "长上下文",  # 按标签搜索
        "anthropic",  # 按提供商搜索
        "openai",  # 按提供商搜索
        "高质量 推理",  # 多关键词搜索
        "不存在的模型",  # 无匹配搜索
    ]
    
    print("🧪 测试搜索查询:")
    print("-" * 50)
    
    for query in test_queries:
        filtered_models = app.filter_models(query, all_models)
        
        if query == "":
            query_display = "(空搜索)"
        else:
            query_display = f'"{query}"'
        
        print(f"\n搜索: {query_display}")
        print(f"结果: {len(filtered_models)} 个模型")
        
        if filtered_models:
            for model_display, model_id in filtered_models[:3]:  # 只显示前3个
                print(f"  - {model_display}")
            if len(filtered_models) > 3:
                print(f"  ... 还有 {len(filtered_models) - 3} 个模型")
        else:
            print("  (无匹配结果)")
    
    print("\n" + "=" * 50)
    print("✅ 搜索功能测试完成!")

def test_model_info_display():
    """测试模型信息显示功能"""
    print("\n📄 测试模型信息显示功能")
    print("=" * 50)
    
    app = DTransApp()
    all_models = app.load_models()
    
    # 测试第一个模型的详细信息
    if all_models:
        test_model = all_models[0]
        print(f"\n测试模型: {test_model['name']}")
        
        # 模拟获取模型详细信息的函数
        def get_model_details(selected_model_id):
            """获取选中模型的详细信息"""
            if not selected_model_id:
                return "请选择一个模型"
            
            # 查找模型详细信息
            selected_model = None
            for model in all_models:
                if model["id"] == selected_model_id:
                    selected_model = model
                    break
            
            if not selected_model:
                return "模型信息不可用"
            
            # 格式化模型信息
            info_parts = [
                f"**{selected_model['name']}**",
                f"🆔 ID: `{selected_model['id']}`",
            ]

            if selected_model.get("description"):
                info_parts.append(f"📝 描述: {selected_model['description']}")

            if selected_model.get("context_length"):
                context_length = selected_model["context_length"]
                if context_length >= 1000:
                    context_str = f"{context_length//1000}K"
                else:
                    context_str = str(context_length)
                info_parts.append(f"📏 上下文长度: {context_str} tokens")
            
            # 显示模型标签
            if selected_model.get("tags"):
                tags_str = " ".join([f"`{tag}`" for tag in selected_model["tags"]])
                info_parts.append(f"🏷️ 特性: {tags_str}")
            
            # 添加使用建议
            model_id_lower = selected_model["id"].lower()
            if "claude" in model_id_lower:
                info_parts.append(
                    "💡 **推荐用途**: 复杂文档翻译、专业术语处理、长文本翻译"
                )
            elif "gpt-4" in model_id_lower:
                info_parts.append(
                    "💡 **推荐用途**: 高质量翻译、多语言处理、复杂格式文档"
                )
            elif "gpt-3.5" in model_id_lower:
                info_parts.append(
                    "💡 **推荐用途**: 快速翻译、日常文档、成本敏感场景"
                )

            return "\n\n".join(info_parts)
        
        # 获取并显示模型详细信息
        model_info = get_model_details(test_model['id'])
        print("\n模型详细信息:")
        print("-" * 30)
        print(model_info)
    
    print("\n✅ 模型信息显示测试完成!")

def main():
    """主测试函数"""
    print("🚀 DTrans模型搜索功能测试")
    print("=" * 60)
    
    try:
        test_model_search()
        test_model_info_display()
        
        print("\n🎉 所有测试通过!")
        print("\n💡 提示:")
        print("- 搜索功能支持按名称、ID、描述和标签搜索")
        print("- 支持多关键词搜索（用空格分隔）")
        print("- 搜索结果按相关性排序")
        print("- 模型信息包含详细描述、标签和使用建议")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
