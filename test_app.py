"""
测试脚本 - 验证DTrans应用的基本功能
"""

import os
import sys
from dotenv import load_dotenv

def test_imports():
    """测试所有模块是否能正常导入"""
    try:
        print("🔍 测试模块导入...")
        
        # 测试工具模块
        from utils.openrouter_client import OpenRouterClient
        from utils.document_parser import DocumentParser
        from utils.translator import DocumentTranslator
        
        print("✅ 工具模块导入成功")
        
        # 测试主应用
        from app import DTransApp
        print("✅ 主应用模块导入成功")
        
        return True
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_environment():
    """测试环境配置"""
    print("\n🔍 测试环境配置...")
    
    load_dotenv()
    api_key = os.getenv("OPENROUTER_API_KEY")
    
    if api_key:
        print("✅ 找到OpenRouter API密钥")
        print(f"   密钥前缀: {api_key[:10]}...")
    else:
        print("⚠️  未找到OpenRouter API密钥")
        print("   请在.env文件中设置OPENROUTER_API_KEY")
    
    return bool(api_key)

def test_basic_functionality():
    """测试基本功能"""
    print("\n🔍 测试基本功能...")
    
    try:
        from utils.openrouter_client import OpenRouterClient
        from utils.document_parser import DocumentParser
        
        # 测试文档解析器
        parser = DocumentParser()
        print("✅ 文档解析器初始化成功")
        
        # 测试OpenRouter客户端
        client = OpenRouterClient()
        print("✅ OpenRouter客户端初始化成功")
        
        # 测试获取模型列表（如果有API密钥）
        if os.getenv("OPENROUTER_API_KEY"):
            try:
                models = client.get_available_models()
                print(f"✅ 成功获取 {len(models)} 个可用模型")
            except Exception as e:
                print(f"⚠️  获取模型列表失败: {e}")
        
        return True
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        return False

def test_gradio_interface():
    """测试Gradio界面创建"""
    print("\n🔍 测试Gradio界面...")
    
    try:
        import gradio as gr
        print("✅ Gradio导入成功")
        
        from app import DTransApp
        app = DTransApp()
        print("✅ DTrans应用初始化成功")
        
        # 不实际启动界面，只测试创建
        interface = app.create_interface()
        print("✅ Gradio界面创建成功")
        
        return True
    except Exception as e:
        print(f"❌ Gradio界面测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 DTrans应用测试开始\n")
    
    tests = [
        ("模块导入", test_imports),
        ("环境配置", test_environment),
        ("基本功能", test_basic_functionality),
        ("Gradio界面", test_gradio_interface)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果摘要
    print("\n" + "="*50)
    print("📊 测试结果摘要:")
    print("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！应用可以正常启动。")
        print("运行 'python app.py' 启动应用")
    else:
        print("\n⚠️  部分测试失败，请检查配置和依赖")
        
        if not os.getenv("OPENROUTER_API_KEY"):
            print("\n💡 提示:")
            print("1. 复制 .env.example 为 .env")
            print("2. 在 .env 文件中设置您的 OpenRouter API 密钥")
            print("3. 重新运行测试")

if __name__ == "__main__":
    main()
