{"version": 3, "file": "Index27-CplFvNur.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Index27.js"], "sourcesContent": ["import { create_ssr_component, add_styles, validate_component, escape, add_attribute } from \"svelte/internal\";\nimport ImagePreview from \"./ImagePreview.js\";\nimport { createEventDispatcher, onMount, onDestroy, afterUpdate } from \"svelte\";\nimport { a1 as Webcam$2, f as StreamingBar, a2 as Spinner, A as Square, a3 as Circle, a4 as Camera, a5 as DropdownArrow, Y as prepare_files, B as BlockLabel, q as Image, I as IconButtonWrapper, F as FullscreenButton, h as IconButton, G as Clear, W as SelectSource, n as Block, S as Static, U as UploadText, k as Empty } from \"./client.js\";\nimport \"svelte/transition\";\nimport { U as Upload } from \"./ModifyUpload.js\";\nimport { I as Image$1 } from \"./Image.js\";\nimport { default as default2 } from \"./Example11.js\";\nconst css$2 = {\n  code: \"button.svelte-qbrfs{cursor:pointer;width:var(--size-full)}.wrap.svelte-qbrfs{display:flex;flex-direction:column;justify-content:center;align-items:center;min-height:var(--size-60);color:var(--block-label-text-color);height:100%;padding-top:var(--size-3)}.icon-wrap.svelte-qbrfs{width:30px;margin-bottom:var(--spacing-lg)}@media(min-width: 768px){.wrap.svelte-qbrfs{font-size:var(--text-lg)}}\",\n  map: '{\"version\":3,\"file\":\"WebcamPermissions.svelte\",\"sources\":[\"WebcamPermissions.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { Webcam } from \\\\\"@gradio/icons\\\\\";\\\\nimport { createEventDispatcher } from \\\\\"svelte\\\\\";\\\\nconst dispatch = createEventDispatcher();\\\\n<\\/script>\\\\n\\\\n<button style:height=\\\\\"100%\\\\\" on:click={() => dispatch(\\\\\"click\\\\\")}>\\\\n\\\\t<div class=\\\\\"wrap\\\\\">\\\\n\\\\t\\\\t<span class=\\\\\"icon-wrap\\\\\">\\\\n\\\\t\\\\t\\\\t<Webcam />\\\\n\\\\t\\\\t</span>\\\\n\\\\t\\\\t{\\\\\"Click to Access Webcam\\\\\"}\\\\n\\\\t</div>\\\\n</button>\\\\n\\\\n<style>\\\\n\\\\tbutton {\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t}\\\\n\\\\n\\\\t.wrap {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tmin-height: var(--size-60);\\\\n\\\\t\\\\tcolor: var(--block-label-text-color);\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tpadding-top: var(--size-3);\\\\n\\\\t}\\\\n\\\\n\\\\t.icon-wrap {\\\\n\\\\t\\\\twidth: 30px;\\\\n\\\\t\\\\tmargin-bottom: var(--spacing-lg);\\\\n\\\\t}\\\\n\\\\n\\\\t@media (min-width: 768px) {\\\\n\\\\t\\\\t.wrap {\\\\n\\\\t\\\\t\\\\tfont-size: var(--text-lg);\\\\n\\\\t\\\\t}\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAeC,mBAAO,CACN,MAAM,CAAE,OAAO,CACf,KAAK,CAAE,IAAI,WAAW,CACvB,CAEA,kBAAM,CACL,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,IAAI,SAAS,CAAC,CAC1B,KAAK,CAAE,IAAI,wBAAwB,CAAC,CACpC,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,IAAI,QAAQ,CAC1B,CAEA,uBAAW,CACV,KAAK,CAAE,IAAI,CACX,aAAa,CAAE,IAAI,YAAY,CAChC,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,kBAAM,CACL,SAAS,CAAE,IAAI,SAAS,CACzB,CACD\"}'\n};\nconst WebcamPermissions = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  createEventDispatcher();\n  $$result.css.add(css$2);\n  return `<button class=\"svelte-qbrfs\"${add_styles({ \"height\": `100%` })}><div class=\"wrap svelte-qbrfs\"><span class=\"icon-wrap svelte-qbrfs\">${validate_component(Webcam$2, \"Webcam\").$$render($$result, {}, {}, {})}</span> ${escape(\"Click to Access Webcam\")}</div> </button>`;\n});\nconst css$1 = {\n  code: \".wrap.svelte-10cpz3p.svelte-10cpz3p{position:relative;width:var(--size-full);height:var(--size-full)}.hide.svelte-10cpz3p.svelte-10cpz3p{display:none}video.svelte-10cpz3p.svelte-10cpz3p{width:var(--size-full);height:var(--size-full);object-fit:contain}.button-wrap.svelte-10cpz3p.svelte-10cpz3p{position:absolute;background-color:var(--block-background-fill);border:1px solid var(--border-color-primary);border-radius:var(--radius-xl);padding:var(--size-1-5);display:flex;bottom:var(--size-2);left:50%;transform:translate(-50%, 0);box-shadow:var(--shadow-drop-lg);border-radius:var(--radius-xl);line-height:var(--size-3);color:var(--button-secondary-text-color)}.icon-with-text.svelte-10cpz3p.svelte-10cpz3p{width:var(--size-20);align-items:center;margin:0 var(--spacing-xl);display:flex;justify-content:space-evenly}@media(min-width: 768px){button.svelte-10cpz3p.svelte-10cpz3p{bottom:var(--size-4)}}@media(min-width: 1280px){button.svelte-10cpz3p.svelte-10cpz3p{bottom:var(--size-8)}}.icon.svelte-10cpz3p.svelte-10cpz3p{width:18px;height:18px;display:flex;justify-content:space-between;align-items:center}.color-primary.svelte-10cpz3p.svelte-10cpz3p{fill:var(--primary-600);stroke:var(--primary-600);color:var(--primary-600)}.flip.svelte-10cpz3p.svelte-10cpz3p{transform:scaleX(-1)}.select-wrap.svelte-10cpz3p.svelte-10cpz3p{-webkit-appearance:none;-moz-appearance:none;appearance:none;color:var(--button-secondary-text-color);background-color:transparent;width:95%;font-size:var(--text-md);position:absolute;bottom:var(--size-2);background-color:var(--block-background-fill);box-shadow:var(--shadow-drop-lg);border-radius:var(--radius-xl);z-index:var(--layer-top);border:1px solid var(--border-color-primary);text-align:left;line-height:var(--size-4);white-space:nowrap;text-overflow:ellipsis;left:50%;transform:translate(-50%, 0);max-width:var(--size-52)}.select-wrap.svelte-10cpz3p>option.svelte-10cpz3p{padding:0.25rem 0.5rem;border-bottom:1px solid var(--border-color-accent);padding-right:var(--size-8);text-overflow:ellipsis;overflow:hidden}.select-wrap.svelte-10cpz3p>option.svelte-10cpz3p:hover{background-color:var(--color-accent)}.select-wrap.svelte-10cpz3p>option.svelte-10cpz3p:last-child{border:none}.inset-icon.svelte-10cpz3p.svelte-10cpz3p{position:absolute;top:5px;right:-6.5px;width:var(--size-10);height:var(--size-5);opacity:0.8}@media(min-width: 768px){.wrap.svelte-10cpz3p.svelte-10cpz3p{font-size:var(--text-lg)}}\",\n  map: '{\"version\":3,\"file\":\"Webcam.svelte\",\"sources\":[\"Webcam.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { createEventDispatcher, onDestroy, onMount } from \\\\\"svelte\\\\\";\\\\nimport { Camera, Circle, Square, DropdownArrow, Spinner } from \\\\\"@gradio/icons\\\\\";\\\\nimport { StreamingBar } from \\\\\"@gradio/statustracker\\\\\";\\\\nimport { prepare_files } from \\\\\"@gradio/client\\\\\";\\\\nimport WebcamPermissions from \\\\\"./WebcamPermissions.svelte\\\\\";\\\\nimport { fade } from \\\\\"svelte/transition\\\\\";\\\\nimport { get_devices, get_video_stream, set_available_devices } from \\\\\"./stream_utils\\\\\";\\\\nlet video_source;\\\\nlet available_video_devices = [];\\\\nlet selected_device = null;\\\\nlet time_limit = null;\\\\nlet stream_state = \\\\\"closed\\\\\";\\\\nexport const modify_stream = (state) => {\\\\n    if (state === \\\\\"closed\\\\\") {\\\\n        time_limit = null;\\\\n        stream_state = \\\\\"closed\\\\\";\\\\n        value = null;\\\\n    }\\\\n    else if (state === \\\\\"waiting\\\\\") {\\\\n        stream_state = \\\\\"waiting\\\\\";\\\\n    }\\\\n    else {\\\\n        stream_state = \\\\\"open\\\\\";\\\\n    }\\\\n};\\\\nexport const set_time_limit = (time) => {\\\\n    if (recording)\\\\n        time_limit = time;\\\\n};\\\\nlet canvas;\\\\nexport let streaming = false;\\\\nexport let pending = false;\\\\nexport let root = \\\\\"\\\\\";\\\\nexport let stream_every = 1;\\\\nexport let mode = \\\\\"image\\\\\";\\\\nexport let mirror_webcam;\\\\nexport let include_audio;\\\\nexport let webcam_constraints = null;\\\\nexport let i18n;\\\\nexport let upload;\\\\nexport let value = null;\\\\nconst dispatch = createEventDispatcher();\\\\nonMount(() => {\\\\n    canvas = document.createElement(\\\\\"canvas\\\\\");\\\\n    if (streaming && mode === \\\\\"image\\\\\") {\\\\n        window.setInterval(() => {\\\\n            if (video_source && !pending) {\\\\n                take_picture();\\\\n            }\\\\n        }, stream_every * 1e3);\\\\n    }\\\\n});\\\\nconst handle_device_change = async (event) => {\\\\n    const target = event.target;\\\\n    const device_id = target.value;\\\\n    await get_video_stream(include_audio, video_source, webcam_constraints, device_id).then(async (local_stream) => {\\\\n        stream = local_stream;\\\\n        selected_device = available_video_devices.find((device) => device.deviceId === device_id) || null;\\\\n        options_open = false;\\\\n    });\\\\n};\\\\nasync function access_webcam() {\\\\n    try {\\\\n        get_video_stream(include_audio, video_source, webcam_constraints).then(async (local_stream) => {\\\\n            webcam_accessed = true;\\\\n            available_video_devices = await get_devices();\\\\n            stream = local_stream;\\\\n        }).then(() => set_available_devices(available_video_devices)).then((devices) => {\\\\n            available_video_devices = devices;\\\\n            const used_devices = stream.getTracks().map((track) => track.getSettings()?.deviceId)[0];\\\\n            selected_device = used_devices ? devices.find((device) => device.deviceId === used_devices) || available_video_devices[0] : available_video_devices[0];\\\\n        });\\\\n        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {\\\\n            dispatch(\\\\\"error\\\\\", i18n(\\\\\"image.no_webcam_support\\\\\"));\\\\n        }\\\\n    }\\\\n    catch (err) {\\\\n        if (err instanceof DOMException && err.name == \\\\\"NotAllowedError\\\\\") {\\\\n            dispatch(\\\\\"error\\\\\", i18n(\\\\\"image.allow_webcam_access\\\\\"));\\\\n        }\\\\n        else {\\\\n            throw err;\\\\n        }\\\\n    }\\\\n}\\\\nfunction take_picture() {\\\\n    var context = canvas.getContext(\\\\\"2d\\\\\");\\\\n    if ((!streaming || streaming && recording) && video_source.videoWidth && video_source.videoHeight) {\\\\n        canvas.width = video_source.videoWidth;\\\\n        canvas.height = video_source.videoHeight;\\\\n        context.drawImage(video_source, 0, 0, video_source.videoWidth, video_source.videoHeight);\\\\n        if (mirror_webcam) {\\\\n            context.scale(-1, 1);\\\\n            context.drawImage(video_source, -video_source.videoWidth, 0);\\\\n        }\\\\n        if (streaming && (!recording || stream_state === \\\\\"waiting\\\\\")) {\\\\n            return;\\\\n        }\\\\n        if (streaming) {\\\\n            const image_data = canvas.toDataURL(\\\\\"image/jpeg\\\\\");\\\\n            dispatch(\\\\\"stream\\\\\", image_data);\\\\n            return;\\\\n        }\\\\n        canvas.toBlob((blob) => {\\\\n            dispatch(streaming ? \\\\\"stream\\\\\" : \\\\\"capture\\\\\", blob);\\\\n        }, `image/${streaming ? \\\\\"jpeg\\\\\" : \\\\\"png\\\\\"}`, 0.8);\\\\n    }\\\\n}\\\\nlet recording = false;\\\\nlet recorded_blobs = [];\\\\nlet stream;\\\\nlet mimeType;\\\\nlet media_recorder;\\\\nfunction take_recording() {\\\\n    if (recording) {\\\\n        media_recorder.stop();\\\\n        let video_blob = new Blob(recorded_blobs, { type: mimeType });\\\\n        let ReaderObj = new FileReader();\\\\n        ReaderObj.onload = async function (e) {\\\\n            if (e.target) {\\\\n                let _video_blob = new File([video_blob], \\\\\"sample.\\\\\" + mimeType.substring(6));\\\\n                const val = await prepare_files([_video_blob]);\\\\n                let val_ = ((await upload(val, root))?.filter(Boolean))[0];\\\\n                dispatch(\\\\\"capture\\\\\", val_);\\\\n                dispatch(\\\\\"stop_recording\\\\\");\\\\n            }\\\\n        };\\\\n        ReaderObj.readAsDataURL(video_blob);\\\\n    }\\\\n    else if (typeof MediaRecorder !== \\\\\"undefined\\\\\") {\\\\n        dispatch(\\\\\"start_recording\\\\\");\\\\n        recorded_blobs = [];\\\\n        let validMimeTypes = [\\\\\"video/webm\\\\\", \\\\\"video/mp4\\\\\"];\\\\n        for (let validMimeType of validMimeTypes) {\\\\n            if (MediaRecorder.isTypeSupported(validMimeType)) {\\\\n                mimeType = validMimeType;\\\\n                break;\\\\n            }\\\\n        }\\\\n        if (mimeType === null) {\\\\n            console.error(\\\\\"No supported MediaRecorder mimeType\\\\\");\\\\n            return;\\\\n        }\\\\n        media_recorder = new MediaRecorder(stream, {\\\\n            mimeType\\\\n        });\\\\n        media_recorder.addEventListener(\\\\\"dataavailable\\\\\", function (e) {\\\\n            recorded_blobs.push(e.data);\\\\n        });\\\\n        media_recorder.start(200);\\\\n    }\\\\n    recording = !recording;\\\\n}\\\\nlet webcam_accessed = false;\\\\nfunction record_video_or_photo({ destroy } = {}) {\\\\n    if (mode === \\\\\"image\\\\\" && streaming) {\\\\n        recording = !recording;\\\\n    }\\\\n    if (!destroy) {\\\\n        if (mode === \\\\\"image\\\\\") {\\\\n            take_picture();\\\\n        }\\\\n        else {\\\\n            take_recording();\\\\n        }\\\\n    }\\\\n    if (!recording && stream) {\\\\n        dispatch(\\\\\"close_stream\\\\\");\\\\n        stream.getTracks().forEach((track) => track.stop());\\\\n        video_source.srcObject = null;\\\\n        webcam_accessed = false;\\\\n        window.setTimeout(() => {\\\\n            value = null;\\\\n        }, 500);\\\\n        value = null;\\\\n    }\\\\n}\\\\nlet options_open = false;\\\\nexport function click_outside(node, cb) {\\\\n    const handle_click = (event) => {\\\\n        if (node && !node.contains(event.target) && !event.defaultPrevented) {\\\\n            cb(event);\\\\n        }\\\\n    };\\\\n    document.addEventListener(\\\\\"click\\\\\", handle_click, true);\\\\n    return {\\\\n        destroy() {\\\\n            document.removeEventListener(\\\\\"click\\\\\", handle_click, true);\\\\n        }\\\\n    };\\\\n}\\\\nfunction handle_click_outside(event) {\\\\n    event.preventDefault();\\\\n    event.stopPropagation();\\\\n    options_open = false;\\\\n}\\\\nonDestroy(() => {\\\\n    if (typeof window === \\\\\"undefined\\\\\")\\\\n        return;\\\\n    record_video_or_photo({ destroy: true });\\\\n    stream?.getTracks().forEach((track) => track.stop());\\\\n});\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"wrap\\\\\">\\\\n\\\\t<StreamingBar {time_limit} />\\\\n\\\\t<!-- svelte-ignore a11y-media-has-caption -->\\\\n\\\\t<!-- need to suppress for video streaming https://github.com/sveltejs/svelte/issues/5967 -->\\\\n\\\\t<video\\\\n\\\\t\\\\tbind:this={video_source}\\\\n\\\\t\\\\tclass:flip={mirror_webcam}\\\\n\\\\t\\\\tclass:hide={!webcam_accessed || (webcam_accessed && !!value)}\\\\n\\\\t/>\\\\n\\\\t<!-- svelte-ignore a11y-missing-attribute -->\\\\n\\\\t<img\\\\n\\\\t\\\\tsrc={value?.url}\\\\n\\\\t\\\\tclass:hide={!webcam_accessed || (webcam_accessed && !value)}\\\\n\\\\t/>\\\\n\\\\t{#if !webcam_accessed}\\\\n\\\\t\\\\t<div\\\\n\\\\t\\\\t\\\\tin:fade={{ delay: 100, duration: 200 }}\\\\n\\\\t\\\\t\\\\ttitle=\\\\\"grant webcam access\\\\\"\\\\n\\\\t\\\\t\\\\tstyle=\\\\\"height: 100%\\\\\"\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t<WebcamPermissions on:click={async () => access_webcam()} />\\\\n\\\\t\\\\t</div>\\\\n\\\\t{:else}\\\\n\\\\t\\\\t<div class=\\\\\"button-wrap\\\\\">\\\\n\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\ton:click={() => record_video_or_photo()}\\\\n\\\\t\\\\t\\\\t\\\\taria-label={mode === \\\\\"image\\\\\" ? \\\\\"capture photo\\\\\" : \\\\\"start recording\\\\\"}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t{#if mode === \\\\\"video\\\\\" || streaming}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#if streaming && stream_state === \\\\\"waiting\\\\\"}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"icon-with-text\\\\\" style=\\\\\"width:var(--size-24);\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"icon color-primary\\\\\" title=\\\\\"spinner\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Spinner />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{i18n(\\\\\"audio.waiting\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{:else if (streaming && stream_state === \\\\\"open\\\\\") || (!streaming && recording)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"icon-with-text\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"icon color-primary\\\\\" title=\\\\\"stop recording\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Square />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{i18n(\\\\\"audio.stop\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"icon-with-text\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"icon color-primary\\\\\" title=\\\\\"start recording\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Circle />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{i18n(\\\\\"audio.record\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"icon\\\\\" title=\\\\\"capture photo\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<Camera />\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t{#if !recording}\\\\n\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click={() => (options_open = true)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\taria-label=\\\\\"select input source\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<DropdownArrow />\\\\n\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</div>\\\\n\\\\t\\\\t{#if options_open && selected_device}\\\\n\\\\t\\\\t\\\\t<select\\\\n\\\\t\\\\t\\\\t\\\\tclass=\\\\\"select-wrap\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\taria-label=\\\\\"select source\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tuse:click_outside={handle_click_outside}\\\\n\\\\t\\\\t\\\\t\\\\ton:change={handle_device_change}\\\\n\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t<button\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tclass=\\\\\"inset-icon\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\ton:click|stopPropagation={() => (options_open = false)}\\\\n\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<DropdownArrow />\\\\n\\\\t\\\\t\\\\t\\\\t</button>\\\\n\\\\t\\\\t\\\\t\\\\t{#if available_video_devices.length === 0}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<option value=\\\\\"\\\\\">{i18n(\\\\\"common.no_devices\\\\\")}</option>\\\\n\\\\t\\\\t\\\\t\\\\t{:else}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{#each available_video_devices as device}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t<option\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tvalue={device.deviceId}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tselected={selected_device.deviceId === device.deviceId}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t{device.label}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\t</option>\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t{/each}\\\\n\\\\t\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t</select>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.wrap {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t}\\\\n\\\\n\\\\t.hide {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t}\\\\n\\\\n\\\\tvideo {\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tobject-fit: contain;\\\\n\\\\t}\\\\n\\\\n\\\\t.button-wrap {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tborder-radius: var(--radius-xl);\\\\n\\\\t\\\\tpadding: var(--size-1-5);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tbottom: var(--size-2);\\\\n\\\\t\\\\tleft: 50%;\\\\n\\\\t\\\\ttransform: translate(-50%, 0);\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop-lg);\\\\n\\\\t\\\\tborder-radius: var(--radius-xl);\\\\n\\\\t\\\\tline-height: var(--size-3);\\\\n\\\\t\\\\tcolor: var(--button-secondary-text-color);\\\\n\\\\t}\\\\n\\\\n\\\\t.icon-with-text {\\\\n\\\\t\\\\twidth: var(--size-20);\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tmargin: 0 var(--spacing-xl);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: space-evenly;\\\\n\\\\t}\\\\n\\\\n\\\\t@media (min-width: 768px) {\\\\n\\\\t\\\\tbutton {\\\\n\\\\t\\\\t\\\\tbottom: var(--size-4);\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t@media (min-width: 1280px) {\\\\n\\\\t\\\\tbutton {\\\\n\\\\t\\\\t\\\\tbottom: var(--size-8);\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t.icon {\\\\n\\\\t\\\\twidth: 18px;\\\\n\\\\t\\\\theight: 18px;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: space-between;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t}\\\\n\\\\n\\\\t.color-primary {\\\\n\\\\t\\\\tfill: var(--primary-600);\\\\n\\\\t\\\\tstroke: var(--primary-600);\\\\n\\\\t\\\\tcolor: var(--primary-600);\\\\n\\\\t}\\\\n\\\\n\\\\t.flip {\\\\n\\\\t\\\\ttransform: scaleX(-1);\\\\n\\\\t}\\\\n\\\\n\\\\t.select-wrap {\\\\n\\\\t\\\\t-webkit-appearance: none;\\\\n\\\\t\\\\t-moz-appearance: none;\\\\n\\\\t\\\\tappearance: none;\\\\n\\\\t\\\\tcolor: var(--button-secondary-text-color);\\\\n\\\\t\\\\tbackground-color: transparent;\\\\n\\\\t\\\\twidth: 95%;\\\\n\\\\t\\\\tfont-size: var(--text-md);\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tbottom: var(--size-2);\\\\n\\\\t\\\\tbackground-color: var(--block-background-fill);\\\\n\\\\t\\\\tbox-shadow: var(--shadow-drop-lg);\\\\n\\\\t\\\\tborder-radius: var(--radius-xl);\\\\n\\\\t\\\\tz-index: var(--layer-top);\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\ttext-align: left;\\\\n\\\\t\\\\tline-height: var(--size-4);\\\\n\\\\t\\\\twhite-space: nowrap;\\\\n\\\\t\\\\ttext-overflow: ellipsis;\\\\n\\\\t\\\\tleft: 50%;\\\\n\\\\t\\\\ttransform: translate(-50%, 0);\\\\n\\\\t\\\\tmax-width: var(--size-52);\\\\n\\\\t}\\\\n\\\\n\\\\t.select-wrap > option {\\\\n\\\\t\\\\tpadding: 0.25rem 0.5rem;\\\\n\\\\t\\\\tborder-bottom: 1px solid var(--border-color-accent);\\\\n\\\\t\\\\tpadding-right: var(--size-8);\\\\n\\\\t\\\\ttext-overflow: ellipsis;\\\\n\\\\t\\\\toverflow: hidden;\\\\n\\\\t}\\\\n\\\\n\\\\t.select-wrap > option:hover {\\\\n\\\\t\\\\tbackground-color: var(--color-accent);\\\\n\\\\t}\\\\n\\\\n\\\\t.select-wrap > option:last-child {\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.inset-icon {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\ttop: 5px;\\\\n\\\\t\\\\tright: -6.5px;\\\\n\\\\t\\\\twidth: var(--size-10);\\\\n\\\\t\\\\theight: var(--size-5);\\\\n\\\\t\\\\topacity: 0.8;\\\\n\\\\t}\\\\n\\\\n\\\\t@media (min-width: 768px) {\\\\n\\\\t\\\\t.wrap {\\\\n\\\\t\\\\t\\\\tfont-size: var(--text-lg);\\\\n\\\\t\\\\t}\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA8SC,mCAAM,CACL,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CACxB,CAEA,mCAAM,CACL,OAAO,CAAE,IACV,CAEA,mCAAM,CACL,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,UAAU,CAAE,OACb,CAEA,0CAAa,CACZ,QAAQ,CAAE,QAAQ,CAClB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,OAAO,CAAE,IAAI,UAAU,CAAC,CACxB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,IAAI,CAAE,GAAG,CACT,SAAS,CAAE,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,CAC7B,UAAU,CAAE,IAAI,gBAAgB,CAAC,CACjC,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,WAAW,CAAE,IAAI,QAAQ,CAAC,CAC1B,KAAK,CAAE,IAAI,6BAA6B,CACzC,CAEA,6CAAgB,CACf,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,WAAW,CAAE,MAAM,CACnB,MAAM,CAAE,CAAC,CAAC,IAAI,YAAY,CAAC,CAC3B,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,YAClB,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,oCAAO,CACN,MAAM,CAAE,IAAI,QAAQ,CACrB,CACD,CAEA,MAAO,YAAY,MAAM,CAAE,CAC1B,oCAAO,CACN,MAAM,CAAE,IAAI,QAAQ,CACrB,CACD,CAEA,mCAAM,CACL,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,aAAa,CAC9B,WAAW,CAAE,MACd,CAEA,4CAAe,CACd,IAAI,CAAE,IAAI,aAAa,CAAC,CACxB,MAAM,CAAE,IAAI,aAAa,CAAC,CAC1B,KAAK,CAAE,IAAI,aAAa,CACzB,CAEA,mCAAM,CACL,SAAS,CAAE,OAAO,EAAE,CACrB,CAEA,0CAAa,CACZ,kBAAkB,CAAE,IAAI,CACxB,eAAe,CAAE,IAAI,CACrB,UAAU,CAAE,IAAI,CAChB,KAAK,CAAE,IAAI,6BAA6B,CAAC,CACzC,gBAAgB,CAAE,WAAW,CAC7B,KAAK,CAAE,GAAG,CACV,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,QAAQ,CAAE,QAAQ,CAClB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,gBAAgB,CAAE,IAAI,uBAAuB,CAAC,CAC9C,UAAU,CAAE,IAAI,gBAAgB,CAAC,CACjC,aAAa,CAAE,IAAI,WAAW,CAAC,CAC/B,OAAO,CAAE,IAAI,WAAW,CAAC,CACzB,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,UAAU,CAAE,IAAI,CAChB,WAAW,CAAE,IAAI,QAAQ,CAAC,CAC1B,WAAW,CAAE,MAAM,CACnB,aAAa,CAAE,QAAQ,CACvB,IAAI,CAAE,GAAG,CACT,SAAS,CAAE,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,CAC7B,SAAS,CAAE,IAAI,SAAS,CACzB,CAEA,2BAAY,CAAG,qBAAO,CACrB,OAAO,CAAE,OAAO,CAAC,MAAM,CACvB,aAAa,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,qBAAqB,CAAC,CACnD,aAAa,CAAE,IAAI,QAAQ,CAAC,CAC5B,aAAa,CAAE,QAAQ,CACvB,QAAQ,CAAE,MACX,CAEA,2BAAY,CAAG,qBAAM,MAAO,CAC3B,gBAAgB,CAAE,IAAI,cAAc,CACrC,CAEA,2BAAY,CAAG,qBAAM,WAAY,CAChC,MAAM,CAAE,IACT,CAEA,yCAAY,CACX,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,GAAG,CACR,KAAK,CAAE,MAAM,CACb,KAAK,CAAE,IAAI,SAAS,CAAC,CACrB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,OAAO,CAAE,GACV,CAEA,MAAO,YAAY,KAAK,CAAE,CACzB,mCAAM,CACL,SAAS,CAAE,IAAI,SAAS,CACzB,CACD\"}'\n};\nfunction click_outside(node, cb) {\n  const handle_click = (event) => {\n    if (node && !node.contains(event.target) && !event.defaultPrevented) {\n      cb(event);\n    }\n  };\n  document.addEventListener(\"click\", handle_click, true);\n  return {\n    destroy() {\n      document.removeEventListener(\"click\", handle_click, true);\n    }\n  };\n}\nconst Webcam = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let video_source;\n  let time_limit = null;\n  let stream_state = \"closed\";\n  const modify_stream = (state) => {\n    if (state === \"closed\") {\n      time_limit = null;\n      stream_state = \"closed\";\n      value = null;\n    } else if (state === \"waiting\") {\n      stream_state = \"waiting\";\n    } else {\n      stream_state = \"open\";\n    }\n  };\n  const set_time_limit = (time) => {\n    if (recording)\n      time_limit = time;\n  };\n  let canvas;\n  let { streaming = false } = $$props;\n  let { pending = false } = $$props;\n  let { root = \"\" } = $$props;\n  let { stream_every = 1 } = $$props;\n  let { mode = \"image\" } = $$props;\n  let { mirror_webcam } = $$props;\n  let { include_audio } = $$props;\n  let { webcam_constraints = null } = $$props;\n  let { i18n } = $$props;\n  let { upload } = $$props;\n  let { value = null } = $$props;\n  const dispatch = createEventDispatcher();\n  onMount(() => {\n    canvas = document.createElement(\"canvas\");\n    if (streaming && mode === \"image\") {\n      window.setInterval(\n        () => {\n        },\n        stream_every * 1e3\n      );\n    }\n  });\n  function take_picture() {\n    var context = canvas.getContext(\"2d\");\n    if ((!streaming || streaming && recording) && video_source.videoWidth && video_source.videoHeight) {\n      canvas.width = video_source.videoWidth;\n      canvas.height = video_source.videoHeight;\n      context.drawImage(video_source, 0, 0, video_source.videoWidth, video_source.videoHeight);\n      if (mirror_webcam) {\n        context.scale(-1, 1);\n        context.drawImage(video_source, -video_source.videoWidth, 0);\n      }\n      if (streaming && (!recording || stream_state === \"waiting\")) {\n        return;\n      }\n      if (streaming) {\n        const image_data = canvas.toDataURL(\"image/jpeg\");\n        dispatch(\"stream\", image_data);\n        return;\n      }\n      canvas.toBlob(\n        (blob) => {\n          dispatch(streaming ? \"stream\" : \"capture\", blob);\n        },\n        `image/${streaming ? \"jpeg\" : \"png\"}`,\n        0.8\n      );\n    }\n  }\n  let recording = false;\n  let recorded_blobs = [];\n  let stream;\n  let mimeType;\n  let media_recorder;\n  function take_recording() {\n    if (recording) {\n      media_recorder.stop();\n      let video_blob = new Blob(recorded_blobs, { type: mimeType });\n      let ReaderObj = new FileReader();\n      ReaderObj.onload = async function(e) {\n        if (e.target) {\n          let _video_blob = new File([video_blob], \"sample.\" + mimeType.substring(6));\n          const val = await prepare_files([_video_blob]);\n          let val_ = (await upload(val, root))?.filter(Boolean)[0];\n          dispatch(\"capture\", val_);\n          dispatch(\"stop_recording\");\n        }\n      };\n      ReaderObj.readAsDataURL(video_blob);\n    } else if (typeof MediaRecorder !== \"undefined\") {\n      dispatch(\"start_recording\");\n      recorded_blobs = [];\n      let validMimeTypes = [\"video/webm\", \"video/mp4\"];\n      for (let validMimeType of validMimeTypes) {\n        if (MediaRecorder.isTypeSupported(validMimeType)) {\n          mimeType = validMimeType;\n          break;\n        }\n      }\n      if (mimeType === null) {\n        console.error(\"No supported MediaRecorder mimeType\");\n        return;\n      }\n      media_recorder = new MediaRecorder(stream, { mimeType });\n      media_recorder.addEventListener(\"dataavailable\", function(e) {\n        recorded_blobs.push(e.data);\n      });\n      media_recorder.start(200);\n    }\n    recording = !recording;\n  }\n  let webcam_accessed = false;\n  function record_video_or_photo({ destroy } = {}) {\n    if (mode === \"image\" && streaming) {\n      recording = !recording;\n    }\n    if (!destroy) {\n      if (mode === \"image\") {\n        take_picture();\n      } else {\n        take_recording();\n      }\n    }\n    if (!recording && stream) {\n      dispatch(\"close_stream\");\n      stream.getTracks().forEach((track) => track.stop());\n      video_source.srcObject = null;\n      webcam_accessed = false;\n      window.setTimeout(\n        () => {\n          value = null;\n        },\n        500\n      );\n      value = null;\n    }\n  }\n  onDestroy(() => {\n    if (typeof window === \"undefined\")\n      return;\n    record_video_or_photo({ destroy: true });\n  });\n  if ($$props.modify_stream === void 0 && $$bindings.modify_stream && modify_stream !== void 0)\n    $$bindings.modify_stream(modify_stream);\n  if ($$props.set_time_limit === void 0 && $$bindings.set_time_limit && set_time_limit !== void 0)\n    $$bindings.set_time_limit(set_time_limit);\n  if ($$props.streaming === void 0 && $$bindings.streaming && streaming !== void 0)\n    $$bindings.streaming(streaming);\n  if ($$props.pending === void 0 && $$bindings.pending && pending !== void 0)\n    $$bindings.pending(pending);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.stream_every === void 0 && $$bindings.stream_every && stream_every !== void 0)\n    $$bindings.stream_every(stream_every);\n  if ($$props.mode === void 0 && $$bindings.mode && mode !== void 0)\n    $$bindings.mode(mode);\n  if ($$props.mirror_webcam === void 0 && $$bindings.mirror_webcam && mirror_webcam !== void 0)\n    $$bindings.mirror_webcam(mirror_webcam);\n  if ($$props.include_audio === void 0 && $$bindings.include_audio && include_audio !== void 0)\n    $$bindings.include_audio(include_audio);\n  if ($$props.webcam_constraints === void 0 && $$bindings.webcam_constraints && webcam_constraints !== void 0)\n    $$bindings.webcam_constraints(webcam_constraints);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.upload === void 0 && $$bindings.upload && upload !== void 0)\n    $$bindings.upload(upload);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.click_outside === void 0 && $$bindings.click_outside && click_outside !== void 0)\n    $$bindings.click_outside(click_outside);\n  $$result.css.add(css$1);\n  return `<div class=\"wrap svelte-10cpz3p\">${validate_component(StreamingBar, \"StreamingBar\").$$render($$result, { time_limit }, {}, {})}   <video class=\"${[\n    \"svelte-10cpz3p\",\n    (mirror_webcam ? \"flip\" : \"\") + \" \" + (!webcam_accessed || webcam_accessed && !!value ? \"hide\" : \"\")\n  ].join(\" \").trim()}\"${add_attribute(\"this\", video_source, 0)}></video>  <img${add_attribute(\"src\", value?.url, 0)} class=\"${[\n    \"svelte-10cpz3p\",\n    !webcam_accessed || webcam_accessed && !value ? \"hide\" : \"\"\n  ].join(\" \").trim()}\"> ${!webcam_accessed ? `<div title=\"grant webcam access\" style=\"height: 100%\">${validate_component(WebcamPermissions, \"WebcamPermissions\").$$render($$result, {}, {}, {})}</div>` : `<div class=\"button-wrap svelte-10cpz3p\"><button${add_attribute(\"aria-label\", mode === \"image\" ? \"capture photo\" : \"start recording\", 0)} class=\"svelte-10cpz3p\">${mode === \"video\" || streaming ? `${streaming && stream_state === \"waiting\" ? `<div class=\"icon-with-text svelte-10cpz3p\" style=\"width:var(--size-24);\"><div class=\"icon color-primary svelte-10cpz3p\" title=\"spinner\">${validate_component(Spinner, \"Spinner\").$$render($$result, {}, {}, {})}</div> ${escape(i18n(\"audio.waiting\"))}</div>` : `${streaming && stream_state === \"open\" || !streaming && recording ? `<div class=\"icon-with-text svelte-10cpz3p\"><div class=\"icon color-primary svelte-10cpz3p\" title=\"stop recording\">${validate_component(Square, \"Square\").$$render($$result, {}, {}, {})}</div> ${escape(i18n(\"audio.stop\"))}</div>` : `<div class=\"icon-with-text svelte-10cpz3p\"><div class=\"icon color-primary svelte-10cpz3p\" title=\"start recording\">${validate_component(Circle, \"Circle\").$$render($$result, {}, {}, {})}</div> ${escape(i18n(\"audio.record\"))}</div>`}`}` : `<div class=\"icon svelte-10cpz3p\" title=\"capture photo\">${validate_component(Camera, \"Camera\").$$render($$result, {}, {}, {})}</div>`}</button> ${!recording ? `<button class=\"icon svelte-10cpz3p\" aria-label=\"select input source\">${validate_component(DropdownArrow, \"DropdownArrow\").$$render($$result, {}, {}, {})}</button>` : ``}</div> ${``}`} </div>`;\n});\nconst Webcam$1 = Webcam;\nconst css = {\n  code: \".image-frame.svelte-1hdlew6 img{width:var(--size-full);height:var(--size-full);object-fit:scale-down}.upload-container.svelte-1hdlew6{display:flex;align-items:center;justify-content:center;height:100%;flex-shrink:1;max-height:100%}.reduced-height.svelte-1hdlew6{height:calc(100% - var(--size-10))}.image-container.svelte-1hdlew6{display:flex;height:100%;flex-direction:column;justify-content:center;align-items:center;max-height:100%}.selectable.svelte-1hdlew6{cursor:crosshair}.image-frame.svelte-1hdlew6{object-fit:cover;width:100%;height:100%}\",\n  map: '{\"version\":3,\"file\":\"ImageUploader.svelte\",\"sources\":[\"ImageUploader.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { createEventDispatcher, tick } from \\\\\"svelte\\\\\";\\\\nimport { BlockLabel, IconButtonWrapper, IconButton } from \\\\\"@gradio/atoms\\\\\";\\\\nimport { Clear, Image as ImageIcon } from \\\\\"@gradio/icons\\\\\";\\\\nimport { FullscreenButton } from \\\\\"@gradio/atoms\\\\\";\\\\nimport {} from \\\\\"@gradio/utils\\\\\";\\\\nimport { get_coordinates_of_clicked_image } from \\\\\"./utils\\\\\";\\\\nimport Webcam from \\\\\"./Webcam.svelte\\\\\";\\\\nimport { Upload } from \\\\\"@gradio/upload\\\\\";\\\\nimport { FileData } from \\\\\"@gradio/client\\\\\";\\\\nimport { SelectSource } from \\\\\"@gradio/atoms\\\\\";\\\\nimport Image from \\\\\"./Image.svelte\\\\\";\\\\nexport let value = null;\\\\nexport let label = void 0;\\\\nexport let show_label;\\\\nexport let sources = [\\\\\"upload\\\\\", \\\\\"clipboard\\\\\", \\\\\"webcam\\\\\"];\\\\nexport let streaming = false;\\\\nexport let pending = false;\\\\nexport let webcam_options;\\\\nexport let selectable = false;\\\\nexport let root;\\\\nexport let i18n;\\\\nexport let max_file_size = null;\\\\nexport let upload;\\\\nexport let stream_handler;\\\\nexport let stream_every;\\\\nexport let modify_stream;\\\\nexport let set_time_limit;\\\\nexport let show_fullscreen_button = true;\\\\nlet upload_input;\\\\nexport let uploading = false;\\\\nexport let active_source = null;\\\\nexport let fullscreen = false;\\\\nasync function handle_upload({ detail }) {\\\\n    if (!streaming) {\\\\n        if (detail.path?.toLowerCase().endsWith(\\\\\".svg\\\\\") && detail.url) {\\\\n            const response = await fetch(detail.url);\\\\n            const svgContent = await response.text();\\\\n            value = {\\\\n                ...detail,\\\\n                url: `data:image/svg+xml,${encodeURIComponent(svgContent)}`\\\\n            };\\\\n        }\\\\n        else {\\\\n            value = detail;\\\\n        }\\\\n        await tick();\\\\n        dispatch(\\\\\"upload\\\\\");\\\\n    }\\\\n}\\\\nfunction handle_clear() {\\\\n    value = null;\\\\n    dispatch(\\\\\"clear\\\\\");\\\\n    dispatch(\\\\\"change\\\\\", null);\\\\n}\\\\nasync function handle_save(img_blob, event) {\\\\n    if (event === \\\\\"stream\\\\\") {\\\\n        dispatch(\\\\\"stream\\\\\", {\\\\n            value: { url: img_blob },\\\\n            is_value_data: true\\\\n        });\\\\n        return;\\\\n    }\\\\n    pending = true;\\\\n    const f = await upload_input.load_files([\\\\n        new File([img_blob], `image/${streaming ? \\\\\"jpeg\\\\\" : \\\\\"png\\\\\"}`)\\\\n    ]);\\\\n    if (event === \\\\\"change\\\\\" || event === \\\\\"upload\\\\\") {\\\\n        value = f?.[0] || null;\\\\n        await tick();\\\\n        dispatch(\\\\\"change\\\\\");\\\\n    }\\\\n    pending = false;\\\\n}\\\\n$: active_streaming = streaming && active_source === \\\\\"webcam\\\\\";\\\\n$: if (uploading && !active_streaming)\\\\n    value = null;\\\\nconst dispatch = createEventDispatcher();\\\\nexport let dragging = false;\\\\n$: dispatch(\\\\\"drag\\\\\", dragging);\\\\nfunction handle_click(evt) {\\\\n    let coordinates = get_coordinates_of_clicked_image(evt);\\\\n    if (coordinates) {\\\\n        dispatch(\\\\\"select\\\\\", { index: coordinates, value: null });\\\\n    }\\\\n}\\\\n$: if (!active_source && sources) {\\\\n    active_source = sources[0];\\\\n}\\\\nasync function handle_select_source(source) {\\\\n    switch (source) {\\\\n        case \\\\\"clipboard\\\\\":\\\\n            upload_input.paste_clipboard();\\\\n            break;\\\\n        default:\\\\n            break;\\\\n    }\\\\n}\\\\nlet image_container;\\\\nfunction on_drag_over(evt) {\\\\n    evt.preventDefault();\\\\n    evt.stopPropagation();\\\\n    if (evt.dataTransfer) {\\\\n        evt.dataTransfer.dropEffect = \\\\\"copy\\\\\";\\\\n    }\\\\n    dragging = true;\\\\n}\\\\nasync function on_drop(evt) {\\\\n    evt.preventDefault();\\\\n    evt.stopPropagation();\\\\n    dragging = false;\\\\n    if (value) {\\\\n        handle_clear();\\\\n        await tick();\\\\n    }\\\\n    active_source = \\\\\"upload\\\\\";\\\\n    await tick();\\\\n    upload_input.load_files_from_drop(evt);\\\\n}\\\\n<\\/script>\\\\n\\\\n<BlockLabel {show_label} Icon={ImageIcon} label={label || \\\\\"Image\\\\\"} />\\\\n\\\\n<div data-testid=\\\\\"image\\\\\" class=\\\\\"image-container\\\\\" bind:this={image_container}>\\\\n\\\\t<IconButtonWrapper>\\\\n\\\\t\\\\t{#if value?.url && !active_streaming}\\\\n\\\\t\\\\t\\\\t{#if show_fullscreen_button}\\\\n\\\\t\\\\t\\\\t\\\\t<FullscreenButton {fullscreen} on:fullscreen />\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t\\\\t<IconButton\\\\n\\\\t\\\\t\\\\t\\\\tIcon={Clear}\\\\n\\\\t\\\\t\\\\t\\\\tlabel=\\\\\"Remove Image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\ton:click={(event) => {\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tvalue = null;\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tdispatch(\\\\\"clear\\\\\");\\\\n\\\\t\\\\t\\\\t\\\\t\\\\tevent.stopPropagation();\\\\n\\\\t\\\\t\\\\t\\\\t}}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</IconButtonWrapper>\\\\n\\\\t<!-- svelte-ignore a11y-no-static-element-interactions -->\\\\n\\\\t<div\\\\n\\\\t\\\\tclass=\\\\\"upload-container\\\\\"\\\\n\\\\t\\\\tclass:reduced-height={sources.length > 1}\\\\n\\\\t\\\\tstyle:width={value ? \\\\\"auto\\\\\" : \\\\\"100%\\\\\"}\\\\n\\\\t\\\\ton:dragover={on_drag_over}\\\\n\\\\t\\\\ton:drop={on_drop}\\\\n\\\\t>\\\\n\\\\t\\\\t<Upload\\\\n\\\\t\\\\t\\\\thidden={value !== null || active_source === \\\\\"webcam\\\\\"}\\\\n\\\\t\\\\t\\\\tbind:this={upload_input}\\\\n\\\\t\\\\t\\\\tbind:uploading\\\\n\\\\t\\\\t\\\\tbind:dragging\\\\n\\\\t\\\\t\\\\tfiletype={active_source === \\\\\"clipboard\\\\\" ? \\\\\"clipboard\\\\\" : \\\\\"image/*\\\\\"}\\\\n\\\\t\\\\t\\\\ton:load={handle_upload}\\\\n\\\\t\\\\t\\\\ton:error\\\\n\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t{max_file_size}\\\\n\\\\t\\\\t\\\\tdisable_click={!sources.includes(\\\\\"upload\\\\\") || value !== null}\\\\n\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t{stream_handler}\\\\n\\\\t\\\\t\\\\taria_label={i18n(\\\\\"image.drop_to_upload\\\\\")}\\\\n\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t{#if value === null}\\\\n\\\\t\\\\t\\\\t\\\\t<slot />\\\\n\\\\t\\\\t\\\\t{/if}\\\\n\\\\t\\\\t</Upload>\\\\n\\\\t\\\\t{#if active_source === \\\\\"webcam\\\\\" && (streaming || (!streaming && !value))}\\\\n\\\\t\\\\t\\\\t<Webcam\\\\n\\\\t\\\\t\\\\t\\\\t{root}\\\\n\\\\t\\\\t\\\\t\\\\t{value}\\\\n\\\\t\\\\t\\\\t\\\\ton:capture={(e) => handle_save(e.detail, \\\\\"change\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\ton:stream={(e) => handle_save(e.detail, \\\\\"stream\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\ton:error\\\\n\\\\t\\\\t\\\\t\\\\ton:drag\\\\n\\\\t\\\\t\\\\t\\\\ton:upload={(e) => handle_save(e.detail, \\\\\"upload\\\\\")}\\\\n\\\\t\\\\t\\\\t\\\\ton:close_stream\\\\n\\\\t\\\\t\\\\t\\\\tmirror_webcam={webcam_options.mirror}\\\\n\\\\t\\\\t\\\\t\\\\t{stream_every}\\\\n\\\\t\\\\t\\\\t\\\\t{streaming}\\\\n\\\\t\\\\t\\\\t\\\\tmode=\\\\\"image\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\tinclude_audio={false}\\\\n\\\\t\\\\t\\\\t\\\\t{i18n}\\\\n\\\\t\\\\t\\\\t\\\\t{upload}\\\\n\\\\t\\\\t\\\\t\\\\tbind:modify_stream\\\\n\\\\t\\\\t\\\\t\\\\tbind:set_time_limit\\\\n\\\\t\\\\t\\\\t\\\\twebcam_constraints={webcam_options.constraints}\\\\n\\\\t\\\\t\\\\t/>\\\\n\\\\t\\\\t{:else if value !== null && !streaming}\\\\n\\\\t\\\\t\\\\t<!-- svelte-ignore a11y-click-events-have-key-events-->\\\\n\\\\t\\\\t\\\\t<!-- svelte-ignore a11y-no-static-element-interactions-->\\\\n\\\\t\\\\t\\\\t<div class:selectable class=\\\\\"image-frame\\\\\" on:click={handle_click}>\\\\n\\\\t\\\\t\\\\t\\\\t<Image src={value.url} alt={value.alt_text} />\\\\n\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t{/if}\\\\n\\\\t</div>\\\\n\\\\t{#if sources.length > 1 || sources.includes(\\\\\"clipboard\\\\\")}\\\\n\\\\t\\\\t<SelectSource\\\\n\\\\t\\\\t\\\\t{sources}\\\\n\\\\t\\\\t\\\\tbind:active_source\\\\n\\\\t\\\\t\\\\t{handle_clear}\\\\n\\\\t\\\\t\\\\thandle_select={handle_select_source}\\\\n\\\\t\\\\t/>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.image-frame :global(img) {\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t\\\\theight: var(--size-full);\\\\n\\\\t\\\\tobject-fit: scale-down;\\\\n\\\\t}\\\\n\\\\n\\\\t.upload-container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tflex-shrink: 1;\\\\n\\\\t\\\\tmax-height: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.reduced-height {\\\\n\\\\t\\\\theight: calc(100% - var(--size-10));\\\\n\\\\t}\\\\n\\\\n\\\\t.image-container {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tmax-height: 100%;\\\\n\\\\t}\\\\n\\\\n\\\\t.selectable {\\\\n\\\\t\\\\tcursor: crosshair;\\\\n\\\\t}\\\\n\\\\n\\\\t.image-frame {\\\\n\\\\t\\\\tobject-fit: cover;\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA8MC,2BAAY,CAAS,GAAK,CACzB,KAAK,CAAE,IAAI,WAAW,CAAC,CACvB,MAAM,CAAE,IAAI,WAAW,CAAC,CACxB,UAAU,CAAE,UACb,CAEA,gCAAkB,CACjB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CAEvB,MAAM,CAAE,IAAI,CACZ,WAAW,CAAE,CAAC,CACd,UAAU,CAAE,IACb,CAEA,8BAAgB,CACf,MAAM,CAAE,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CACnC,CAEA,+BAAiB,CAChB,OAAO,CAAE,IAAI,CACb,MAAM,CAAE,IAAI,CACZ,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MAAM,CACnB,UAAU,CAAE,IACb,CAEA,0BAAY,CACX,MAAM,CAAE,SACT,CAEA,2BAAa,CACZ,UAAU,CAAE,KAAK,CACjB,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IACT\"}'\n};\nconst ImageUploader = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let active_streaming;\n  let { value = null } = $$props;\n  let { label = void 0 } = $$props;\n  let { show_label } = $$props;\n  let { sources = [\"upload\", \"clipboard\", \"webcam\"] } = $$props;\n  let { streaming = false } = $$props;\n  let { pending = false } = $$props;\n  let { webcam_options } = $$props;\n  let { selectable = false } = $$props;\n  let { root } = $$props;\n  let { i18n } = $$props;\n  let { max_file_size = null } = $$props;\n  let { upload } = $$props;\n  let { stream_handler } = $$props;\n  let { stream_every } = $$props;\n  let { modify_stream } = $$props;\n  let { set_time_limit } = $$props;\n  let { show_fullscreen_button = true } = $$props;\n  let upload_input;\n  let { uploading = false } = $$props;\n  let { active_source = null } = $$props;\n  let { fullscreen = false } = $$props;\n  function handle_clear() {\n    value = null;\n    dispatch(\"clear\");\n    dispatch(\"change\", null);\n  }\n  const dispatch = createEventDispatcher();\n  let { dragging = false } = $$props;\n  async function handle_select_source(source) {\n    switch (source) {\n      case \"clipboard\":\n        upload_input.paste_clipboard();\n        break;\n    }\n  }\n  let image_container;\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.sources === void 0 && $$bindings.sources && sources !== void 0)\n    $$bindings.sources(sources);\n  if ($$props.streaming === void 0 && $$bindings.streaming && streaming !== void 0)\n    $$bindings.streaming(streaming);\n  if ($$props.pending === void 0 && $$bindings.pending && pending !== void 0)\n    $$bindings.pending(pending);\n  if ($$props.webcam_options === void 0 && $$bindings.webcam_options && webcam_options !== void 0)\n    $$bindings.webcam_options(webcam_options);\n  if ($$props.selectable === void 0 && $$bindings.selectable && selectable !== void 0)\n    $$bindings.selectable(selectable);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  if ($$props.max_file_size === void 0 && $$bindings.max_file_size && max_file_size !== void 0)\n    $$bindings.max_file_size(max_file_size);\n  if ($$props.upload === void 0 && $$bindings.upload && upload !== void 0)\n    $$bindings.upload(upload);\n  if ($$props.stream_handler === void 0 && $$bindings.stream_handler && stream_handler !== void 0)\n    $$bindings.stream_handler(stream_handler);\n  if ($$props.stream_every === void 0 && $$bindings.stream_every && stream_every !== void 0)\n    $$bindings.stream_every(stream_every);\n  if ($$props.modify_stream === void 0 && $$bindings.modify_stream && modify_stream !== void 0)\n    $$bindings.modify_stream(modify_stream);\n  if ($$props.set_time_limit === void 0 && $$bindings.set_time_limit && set_time_limit !== void 0)\n    $$bindings.set_time_limit(set_time_limit);\n  if ($$props.show_fullscreen_button === void 0 && $$bindings.show_fullscreen_button && show_fullscreen_button !== void 0)\n    $$bindings.show_fullscreen_button(show_fullscreen_button);\n  if ($$props.uploading === void 0 && $$bindings.uploading && uploading !== void 0)\n    $$bindings.uploading(uploading);\n  if ($$props.active_source === void 0 && $$bindings.active_source && active_source !== void 0)\n    $$bindings.active_source(active_source);\n  if ($$props.fullscreen === void 0 && $$bindings.fullscreen && fullscreen !== void 0)\n    $$bindings.fullscreen(fullscreen);\n  if ($$props.dragging === void 0 && $$bindings.dragging && dragging !== void 0)\n    $$bindings.dragging(dragging);\n  $$result.css.add(css);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    {\n      if (!active_source && sources) {\n        active_source = sources[0];\n      }\n    }\n    active_streaming = streaming && active_source === \"webcam\";\n    {\n      if (uploading && !active_streaming)\n        value = null;\n    }\n    {\n      dispatch(\"drag\", dragging);\n    }\n    $$rendered = `${validate_component(BlockLabel, \"BlockLabel\").$$render(\n      $$result,\n      {\n        show_label,\n        Icon: Image,\n        label: label || \"Image\"\n      },\n      {},\n      {}\n    )} <div data-testid=\"image\" class=\"image-container svelte-1hdlew6\"${add_attribute(\"this\", image_container, 0)}>${validate_component(IconButtonWrapper, \"IconButtonWrapper\").$$render($$result, {}, {}, {\n      default: () => {\n        return `${value?.url && !active_streaming ? `${show_fullscreen_button ? `${validate_component(FullscreenButton, \"FullscreenButton\").$$render($$result, { fullscreen }, {}, {})}` : ``} ${validate_component(IconButton, \"IconButton\").$$render($$result, { Icon: Clear, label: \"Remove Image\" }, {}, {})}` : ``}`;\n      }\n    })}  <div class=\"${[\n      \"upload-container svelte-1hdlew6\",\n      sources.length > 1 ? \"reduced-height\" : \"\"\n    ].join(\" \").trim()}\"${add_styles({ \"width\": value ? \"auto\" : \"100%\" })}>${validate_component(Upload, \"Upload\").$$render(\n      $$result,\n      {\n        hidden: value !== null || active_source === \"webcam\",\n        filetype: active_source === \"clipboard\" ? \"clipboard\" : \"image/*\",\n        root,\n        max_file_size,\n        disable_click: !sources.includes(\"upload\") || value !== null,\n        upload,\n        stream_handler,\n        aria_label: i18n(\"image.drop_to_upload\"),\n        this: upload_input,\n        uploading,\n        dragging\n      },\n      {\n        this: ($$value) => {\n          upload_input = $$value;\n          $$settled = false;\n        },\n        uploading: ($$value) => {\n          uploading = $$value;\n          $$settled = false;\n        },\n        dragging: ($$value) => {\n          dragging = $$value;\n          $$settled = false;\n        }\n      },\n      {\n        default: () => {\n          return `${value === null ? `${slots.default ? slots.default({}) : ``}` : ``}`;\n        }\n      }\n    )} ${active_source === \"webcam\" && (streaming || !streaming && !value) ? `${validate_component(Webcam$1, \"Webcam\").$$render(\n      $$result,\n      {\n        root,\n        value,\n        mirror_webcam: webcam_options.mirror,\n        stream_every,\n        streaming,\n        mode: \"image\",\n        include_audio: false,\n        i18n,\n        upload,\n        webcam_constraints: webcam_options.constraints,\n        modify_stream,\n        set_time_limit\n      },\n      {\n        modify_stream: ($$value) => {\n          modify_stream = $$value;\n          $$settled = false;\n        },\n        set_time_limit: ($$value) => {\n          set_time_limit = $$value;\n          $$settled = false;\n        }\n      },\n      {}\n    )}` : `${value !== null && !streaming ? `  <div class=\"${[\"image-frame svelte-1hdlew6\", selectable ? \"selectable\" : \"\"].join(\" \").trim()}\">${validate_component(Image$1, \"Image\").$$render($$result, { src: value.url, alt: value.alt_text }, {}, {})}</div>` : ``}`}</div> ${sources.length > 1 || sources.includes(\"clipboard\") ? `${validate_component(SelectSource, \"SelectSource\").$$render(\n      $$result,\n      {\n        sources,\n        handle_clear,\n        handle_select: handle_select_source,\n        active_source\n      },\n      {\n        active_source: ($$value) => {\n          active_source = $$value;\n          $$settled = false;\n        }\n      },\n      {}\n    )}` : ``} </div>`;\n  } while (!$$settled);\n  return $$rendered;\n});\nconst ImageUploader$1 = ImageUploader;\nconst Index = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let stream_state = \"closed\";\n  let _modify_stream = () => {\n  };\n  function modify_stream_state(state) {\n    stream_state = state;\n    _modify_stream(state);\n  }\n  const get_stream_state = () => stream_state;\n  let { set_time_limit } = $$props;\n  let { value_is_output = false } = $$props;\n  let { elem_id = \"\" } = $$props;\n  let { elem_classes = [] } = $$props;\n  let { visible = true } = $$props;\n  let { value = null } = $$props;\n  let old_value = null;\n  let { label } = $$props;\n  let { show_label } = $$props;\n  let { show_download_button } = $$props;\n  let { root } = $$props;\n  let { height } = $$props;\n  let { width } = $$props;\n  let { stream_every } = $$props;\n  let { _selectable = false } = $$props;\n  let { container = true } = $$props;\n  let { scale = null } = $$props;\n  let { min_width = void 0 } = $$props;\n  let { loading_status } = $$props;\n  let { show_share_button = false } = $$props;\n  let { sources = [\"upload\", \"clipboard\", \"webcam\"] } = $$props;\n  let { interactive } = $$props;\n  let { streaming } = $$props;\n  let { pending } = $$props;\n  let { placeholder = void 0 } = $$props;\n  let { show_fullscreen_button } = $$props;\n  let { input_ready } = $$props;\n  let { webcam_options } = $$props;\n  let fullscreen = false;\n  let uploading = false;\n  let { gradio } = $$props;\n  afterUpdate(() => {\n    value_is_output = false;\n  });\n  let dragging;\n  let active_source = null;\n  let upload_component;\n  if ($$props.modify_stream_state === void 0 && $$bindings.modify_stream_state && modify_stream_state !== void 0)\n    $$bindings.modify_stream_state(modify_stream_state);\n  if ($$props.get_stream_state === void 0 && $$bindings.get_stream_state && get_stream_state !== void 0)\n    $$bindings.get_stream_state(get_stream_state);\n  if ($$props.set_time_limit === void 0 && $$bindings.set_time_limit && set_time_limit !== void 0)\n    $$bindings.set_time_limit(set_time_limit);\n  if ($$props.value_is_output === void 0 && $$bindings.value_is_output && value_is_output !== void 0)\n    $$bindings.value_is_output(value_is_output);\n  if ($$props.elem_id === void 0 && $$bindings.elem_id && elem_id !== void 0)\n    $$bindings.elem_id(elem_id);\n  if ($$props.elem_classes === void 0 && $$bindings.elem_classes && elem_classes !== void 0)\n    $$bindings.elem_classes(elem_classes);\n  if ($$props.visible === void 0 && $$bindings.visible && visible !== void 0)\n    $$bindings.visible(visible);\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.label === void 0 && $$bindings.label && label !== void 0)\n    $$bindings.label(label);\n  if ($$props.show_label === void 0 && $$bindings.show_label && show_label !== void 0)\n    $$bindings.show_label(show_label);\n  if ($$props.show_download_button === void 0 && $$bindings.show_download_button && show_download_button !== void 0)\n    $$bindings.show_download_button(show_download_button);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.height === void 0 && $$bindings.height && height !== void 0)\n    $$bindings.height(height);\n  if ($$props.width === void 0 && $$bindings.width && width !== void 0)\n    $$bindings.width(width);\n  if ($$props.stream_every === void 0 && $$bindings.stream_every && stream_every !== void 0)\n    $$bindings.stream_every(stream_every);\n  if ($$props._selectable === void 0 && $$bindings._selectable && _selectable !== void 0)\n    $$bindings._selectable(_selectable);\n  if ($$props.container === void 0 && $$bindings.container && container !== void 0)\n    $$bindings.container(container);\n  if ($$props.scale === void 0 && $$bindings.scale && scale !== void 0)\n    $$bindings.scale(scale);\n  if ($$props.min_width === void 0 && $$bindings.min_width && min_width !== void 0)\n    $$bindings.min_width(min_width);\n  if ($$props.loading_status === void 0 && $$bindings.loading_status && loading_status !== void 0)\n    $$bindings.loading_status(loading_status);\n  if ($$props.show_share_button === void 0 && $$bindings.show_share_button && show_share_button !== void 0)\n    $$bindings.show_share_button(show_share_button);\n  if ($$props.sources === void 0 && $$bindings.sources && sources !== void 0)\n    $$bindings.sources(sources);\n  if ($$props.interactive === void 0 && $$bindings.interactive && interactive !== void 0)\n    $$bindings.interactive(interactive);\n  if ($$props.streaming === void 0 && $$bindings.streaming && streaming !== void 0)\n    $$bindings.streaming(streaming);\n  if ($$props.pending === void 0 && $$bindings.pending && pending !== void 0)\n    $$bindings.pending(pending);\n  if ($$props.placeholder === void 0 && $$bindings.placeholder && placeholder !== void 0)\n    $$bindings.placeholder(placeholder);\n  if ($$props.show_fullscreen_button === void 0 && $$bindings.show_fullscreen_button && show_fullscreen_button !== void 0)\n    $$bindings.show_fullscreen_button(show_fullscreen_button);\n  if ($$props.input_ready === void 0 && $$bindings.input_ready && input_ready !== void 0)\n    $$bindings.input_ready(input_ready);\n  if ($$props.webcam_options === void 0 && $$bindings.webcam_options && webcam_options !== void 0)\n    $$bindings.webcam_options(webcam_options);\n  if ($$props.gradio === void 0 && $$bindings.gradio && gradio !== void 0)\n    $$bindings.gradio(gradio);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    input_ready = !uploading;\n    {\n      {\n        if (JSON.stringify(value) !== JSON.stringify(old_value)) {\n          old_value = value;\n          gradio.dispatch(\"change\");\n          if (!value_is_output) {\n            gradio.dispatch(\"input\");\n          }\n        }\n      }\n    }\n    $$rendered = `   ${!interactive ? `${validate_component(Block, \"Block\").$$render(\n      $$result,\n      {\n        visible,\n        variant: \"solid\",\n        border_mode: dragging ? \"focus\" : \"base\",\n        padding: false,\n        elem_id,\n        elem_classes,\n        height: height || void 0,\n        width,\n        allow_overflow: false,\n        container,\n        scale,\n        min_width,\n        fullscreen\n      },\n      {\n        fullscreen: ($$value) => {\n          fullscreen = $$value;\n          $$settled = false;\n        }\n      },\n      {\n        default: () => {\n          return `${validate_component(Static, \"StatusTracker\").$$render($$result, Object.assign({}, { autoscroll: gradio.autoscroll }, { i18n: gradio.i18n }, loading_status), {}, {})} ${validate_component(ImagePreview, \"StaticImage\").$$render(\n            $$result,\n            {\n              fullscreen,\n              value,\n              label,\n              show_label,\n              show_download_button,\n              selectable: _selectable,\n              show_share_button,\n              i18n: gradio.i18n,\n              show_fullscreen_button\n            },\n            {},\n            {}\n          )}`;\n        }\n      }\n    )}` : `${validate_component(Block, \"Block\").$$render(\n      $$result,\n      {\n        visible,\n        variant: value === null ? \"dashed\" : \"solid\",\n        border_mode: dragging ? \"focus\" : \"base\",\n        padding: false,\n        elem_id,\n        elem_classes,\n        height: height || void 0,\n        width,\n        allow_overflow: false,\n        container,\n        scale,\n        min_width,\n        fullscreen\n      },\n      {\n        fullscreen: ($$value) => {\n          fullscreen = $$value;\n          $$settled = false;\n        }\n      },\n      {\n        default: () => {\n          return `${validate_component(Static, \"StatusTracker\").$$render($$result, Object.assign({}, { autoscroll: gradio.autoscroll }, { i18n: gradio.i18n }, loading_status), {}, {})} ${validate_component(ImageUploader$1, \"ImageUploader\").$$render(\n            $$result,\n            {\n              selectable: _selectable,\n              root,\n              sources,\n              fullscreen,\n              label,\n              show_label,\n              pending,\n              streaming,\n              webcam_options,\n              stream_every,\n              max_file_size: gradio.max_file_size,\n              i18n: gradio.i18n,\n              upload: (...args) => gradio.client.upload(...args),\n              stream_handler: gradio.client?.stream,\n              this: upload_component,\n              uploading,\n              active_source,\n              value,\n              dragging,\n              modify_stream: _modify_stream,\n              set_time_limit\n            },\n            {\n              this: ($$value) => {\n                upload_component = $$value;\n                $$settled = false;\n              },\n              uploading: ($$value) => {\n                uploading = $$value;\n                $$settled = false;\n              },\n              active_source: ($$value) => {\n                active_source = $$value;\n                $$settled = false;\n              },\n              value: ($$value) => {\n                value = $$value;\n                $$settled = false;\n              },\n              dragging: ($$value) => {\n                dragging = $$value;\n                $$settled = false;\n              },\n              modify_stream: ($$value) => {\n                _modify_stream = $$value;\n                $$settled = false;\n              },\n              set_time_limit: ($$value) => {\n                set_time_limit = $$value;\n                $$settled = false;\n              }\n            },\n            {\n              default: () => {\n                return `${active_source === \"upload\" || !active_source ? `${validate_component(UploadText, \"UploadText\").$$render(\n                  $$result,\n                  {\n                    i18n: gradio.i18n,\n                    type: \"image\",\n                    placeholder\n                  },\n                  {},\n                  {}\n                )}` : `${active_source === \"clipboard\" ? `${validate_component(UploadText, \"UploadText\").$$render(\n                  $$result,\n                  {\n                    i18n: gradio.i18n,\n                    type: \"clipboard\",\n                    mode: \"short\"\n                  },\n                  {},\n                  {}\n                )}` : `${validate_component(Empty, \"Empty\").$$render($$result, { unpadded_box: true, size: \"large\" }, {}, {\n                  default: () => {\n                    return `${validate_component(Image, \"Image\").$$render($$result, {}, {}, {})}`;\n                  }\n                })}`}`}`;\n              }\n            }\n          )}`;\n        }\n      }\n    )}`}`;\n  } while (!$$settled);\n  return $$rendered;\n});\nexport {\n  default2 as BaseExample,\n  Image$1 as BaseImage,\n  ImageUploader$1 as BaseImageUploader,\n  ImagePreview as BaseStaticImage,\n  Webcam$1 as Webcam,\n  Index as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQA,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,yYAAyY;AACjZ,EAAE,GAAG,EAAE,ogDAAogD;AAC3gD,CAAC,CAAC;AACF,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACzF,EAAE,qBAAqB,EAAE,CAAC;AAC1B,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,OAAO,CAAC,4BAA4B,EAAE,UAAU,CAAC,EAAE,QAAQ,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,qEAAqE,EAAE,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,wBAAwB,CAAC,CAAC,gBAAgB,CAAC,CAAC;AACnR,CAAC,CAAC,CAAC;AACH,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,w4EAAw4E;AACh5E,EAAE,GAAG,EAAE,mxgBAAmxgB;AAC1xgB,CAAC,CAAC;AACF,SAAS,aAAa,CAAC,IAAI,EAAE,EAAE,EAAE;AACjC,EAAE,MAAM,YAAY,GAAG,CAAC,KAAK,KAAK;AAClC,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,EAAE;AACzE,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC;AAChB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;AACzD,EAAE,OAAO;AACT,IAAI,OAAO,GAAG;AACd,MAAM,QAAQ,CAAC,mBAAmB,CAAC,OAAO,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;AAChE,KAAK;AACL,GAAG,CAAC;AACJ,CAAC;AACD,MAAM,MAAM,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC9E,EAAE,IAAI,YAAY,CAAC;AACnB,EAAE,IAAI,UAAU,GAAG,IAAI,CAAC;AACxB,EAAE,IAAI,YAAY,GAAG,QAAQ,CAAC;AAC9B,EAAE,MAAM,aAAa,GAAG,CAAC,KAAK,KAAK;AACnC,IAAI,IAAI,KAAK,KAAK,QAAQ,EAAE;AAC5B,MAAM,UAAU,GAAG,IAAI,CAAC;AACxB,MAAM,YAAY,GAAG,QAAQ,CAAC;AAC9B,MAAM,KAAK,GAAG,IAAI,CAAC;AACnB,KAAK,MAAM,IAAI,KAAK,KAAK,SAAS,EAAE;AACpC,MAAM,YAAY,GAAG,SAAS,CAAC;AAC/B,KAAK,MAAM;AACX,MAAM,YAAY,GAAG,MAAM,CAAC;AAC5B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,MAAM,cAAc,GAAG,CAAC,IAAI,KAAK;AACnC,IAAI,IAAI,SAAS;AACjB,MAAM,UAAU,GAAG,IAAI,CAAC;AACxB,GAAG,CAAC;AACJ,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,IAAI,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,YAAY,GAAG,CAAC,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,IAAI,GAAG,OAAO,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,kBAAkB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAC9C,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAW3C,EAAE,SAAS,YAAY,GAAG;AAC1B,IAAI,IAAI,OAAO,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AAC1C,IAAI,IAAI,CAAC,CAAC,SAAS,IAAI,SAAS,IAAI,SAAS,KAAK,YAAY,CAAC,UAAU,IAAI,YAAY,CAAC,WAAW,EAAE;AACvG,MAAM,MAAM,CAAC,KAAK,GAAG,YAAY,CAAC,UAAU,CAAC;AAC7C,MAAM,MAAM,CAAC,MAAM,GAAG,YAAY,CAAC,WAAW,CAAC;AAC/C,MAAM,OAAO,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;AAC/F,MAAM,IAAI,aAAa,EAAE;AACzB,QAAQ,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7B,QAAQ,OAAO,CAAC,SAAS,CAAC,YAAY,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;AACrE,OAAO;AACP,MAAM,IAAI,SAAS,KAAK,CAAC,SAAS,IAAI,YAAY,KAAK,SAAS,CAAC,EAAE;AACnE,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,IAAI,SAAS,EAAE;AACrB,QAAQ,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC;AAC1D,QAAQ,QAAQ,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AACvC,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,MAAM,CAAC,MAAM;AACnB,QAAQ,CAAC,IAAI,KAAK;AAClB,UAAU,QAAQ,CAAC,SAAS,GAAG,QAAQ,GAAG,SAAS,EAAE,IAAI,CAAC,CAAC;AAC3D,SAAS;AACT,QAAQ,CAAC,MAAM,EAAE,SAAS,GAAG,MAAM,GAAG,KAAK,CAAC,CAAC;AAC7C,QAAQ,GAAG;AACX,OAAO,CAAC;AACR,KAAK;AACL,GAAG;AACH,EAAE,IAAI,SAAS,GAAG,KAAK,CAAC;AACxB,EAAE,IAAI,cAAc,GAAG,EAAE,CAAC;AAC1B,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,IAAI,QAAQ,CAAC;AACf,EAAE,IAAI,cAAc,CAAC;AACrB,EAAE,SAAS,cAAc,GAAG;AAC5B,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,cAAc,CAAC,IAAI,EAAE,CAAC;AAC5B,MAAM,IAAI,UAAU,GAAG,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,CAAC,CAAC;AACpE,MAAM,IAAI,SAAS,GAAG,IAAI,UAAU,EAAE,CAAC;AACvC,MAAM,SAAS,CAAC,MAAM,GAAG,eAAe,CAAC,EAAE;AAC3C,QAAQ,IAAI,CAAC,CAAC,MAAM,EAAE;AACtB,UAAU,IAAI,WAAW,GAAG,IAAI,IAAI,CAAC,CAAC,UAAU,CAAC,EAAE,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;AACtF,UAAU,MAAM,GAAG,GAAG,MAAM,aAAa,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC;AACzD,UAAU,IAAI,IAAI,GAAG,CAAC,MAAM,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AACnE,UAAU,QAAQ,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AACpC,UAAU,QAAQ,CAAC,gBAAgB,CAAC,CAAC;AACrC,SAAS;AACT,OAAO,CAAC;AACR,MAAM,SAAS,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AAC1C,KAAK,MAAM,IAAI,OAAO,aAAa,KAAK,WAAW,EAAE;AACrD,MAAM,QAAQ,CAAC,iBAAiB,CAAC,CAAC;AAClC,MAAM,cAAc,GAAG,EAAE,CAAC;AAC1B,MAAM,IAAI,cAAc,GAAG,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;AACvD,MAAM,KAAK,IAAI,aAAa,IAAI,cAAc,EAAE;AAChD,QAAQ,IAAI,aAAa,CAAC,eAAe,CAAC,aAAa,CAAC,EAAE;AAC1D,UAAU,QAAQ,GAAG,aAAa,CAAC;AACnC,UAAU,MAAM;AAChB,SAAS;AACT,OAAO;AACP,MAAM,IAAI,QAAQ,KAAK,IAAI,EAAE;AAC7B,QAAQ,OAAO,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC;AAC7D,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,cAAc,GAAG,IAAI,aAAa,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAC;AAC/D,MAAM,cAAc,CAAC,gBAAgB,CAAC,eAAe,EAAE,SAAS,CAAC,EAAE;AACnE,QAAQ,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;AACpC,OAAO,CAAC,CAAC;AACT,MAAM,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,SAAS,GAAG,CAAC,SAAS,CAAC;AAC3B,GAAG;AACH,EAAE,IAAI,eAAe,GAAG,KAAK,CAAC;AAC9B,EAAE,SAAS,qBAAqB,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,EAAE;AACnD,IAAI,IAAI,IAAI,KAAK,OAAO,IAAI,SAAS,EAAE;AACvC,MAAM,SAAS,GAAG,CAAC,SAAS,CAAC;AAC7B,KAAK;AACL,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB,MAAM,IAAI,IAAI,KAAK,OAAO,EAAE;AAC5B,QAAQ,YAAY,EAAE,CAAC;AACvB,OAAO,MAAM;AACb,QAAQ,cAAc,EAAE,CAAC;AACzB,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,SAAS,IAAI,MAAM,EAAE;AAC9B,MAAM,QAAQ,CAAC,cAAc,CAAC,CAAC;AAC/B,MAAM,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;AAC1D,MAAM,YAAY,CAAC,SAAS,GAAG,IAAI,CAAC;AACpC,MAAM,eAAe,GAAG,KAAK,CAAC;AAC9B,MAAM,MAAM,CAAC,UAAU;AACvB,QAAQ,MAAM;AACd,UAAU,KAAK,GAAG,IAAI,CAAC;AACvB,SAAS;AACT,QAAQ,GAAG;AACX,OAAO,CAAC;AACR,MAAM,KAAK,GAAG,IAAI,CAAC;AACnB,KAAK;AACL,GAAG;AACH,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,IAAI,OAAO,MAAM,KAAK,WAAW;AACrC,MAAM,OAAO;AACb,IAAI,qBAAqB,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;AAC7C,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,kBAAkB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kBAAkB,IAAI,kBAAkB,KAAK,KAAK,CAAC;AAC7G,IAAI,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AACtD,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE,OAAO,CAAC,iCAAiC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,iBAAiB,EAAE;AAC5J,IAAI,gBAAgB;AACpB,IAAI,CAAC,aAAa,GAAG,MAAM,GAAG,EAAE,IAAI,GAAG,IAAI,CAAC,eAAe,IAAI,eAAe,IAAI,CAAC,CAAC,KAAK,GAAG,MAAM,GAAG,EAAE,CAAC;AACxG,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC,eAAe,EAAE,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE;AAC9H,IAAI,gBAAgB;AACpB,IAAI,CAAC,eAAe,IAAI,eAAe,IAAI,CAAC,KAAK,GAAG,MAAM,GAAG,EAAE;AAC/D,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,eAAe,GAAG,CAAC,sDAAsD,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,+CAA+C,EAAE,aAAa,CAAC,YAAY,EAAE,IAAI,KAAK,OAAO,GAAG,eAAe,GAAG,iBAAiB,EAAE,CAAC,CAAC,CAAC,wBAAwB,EAAE,IAAI,KAAK,OAAO,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,IAAI,YAAY,KAAK,SAAS,GAAG,CAAC,wIAAwI,EAAE,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,SAAS,IAAI,YAAY,KAAK,MAAM,IAAI,CAAC,SAAS,IAAI,SAAS,GAAG,CAAC,iHAAiH,EAAE,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,kHAAkH,EAAE,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,uDAAuD,EAAE,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,UAAU,EAAE,CAAC,SAAS,GAAG,CAAC,qEAAqE,EAAE,kBAAkB,CAAC,aAAa,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACpjD,CAAC,CAAC,CAAC;AACE,MAAC,QAAQ,GAAG,OAAO;AACxB,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,oiBAAoiB;AAC5iB,EAAE,GAAG,EAAE,ykQAAykQ;AAChlQ,CAAC,CAAC;AACF,MAAM,aAAa,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACrF,EAAE,IAAI,gBAAgB,CAAC;AACvB,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,KAAK,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC;AAChE,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,OAAO,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,sBAAsB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAClD,EAAE,IAAI,YAAY,CAAC;AACnB,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,SAAS,YAAY,GAAG;AAC1B,IAAI,KAAK,GAAG,IAAI,CAAC;AACjB,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC;AACtB,IAAI,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAC7B,GAAG;AACH,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAC3C,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,eAAe,oBAAoB,CAAC,MAAM,EAAE;AAC9C,IAAI,QAAQ,MAAM;AAClB,MAAM,KAAK,WAAW;AACtB,QAAQ,YAAY,CAAC,eAAe,EAAE,CAAC;AACvC,QAAQ,MAAM;AACd,KAAK;AACL,GAAG;AACH,EAAE,IAAI,eAAe,CAAC;AACtB,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,sBAAsB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,sBAAsB,IAAI,sBAAsB,KAAK,KAAK,CAAC;AACzH,IAAI,UAAU,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;AAC9D,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI;AACJ,MAAM,IAAI,CAAC,aAAa,IAAI,OAAO,EAAE;AACrC,QAAQ,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AACnC,OAAO;AACP,KAAK;AACL,IAAI,gBAAgB,GAAG,SAAS,IAAI,aAAa,KAAK,QAAQ,CAAC;AAC/D,IAAI;AACJ,MAAM,IAAI,SAAS,IAAI,CAAC,gBAAgB;AACxC,QAAQ,KAAK,GAAG,IAAI,CAAC;AACrB,KAAK;AACL,IAAI;AACJ,MAAM,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AACjC,KAAK;AACL,IAAI,UAAU,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACzE,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,UAAU;AAClB,QAAQ,IAAI,EAAE,KAAK;AACnB,QAAQ,KAAK,EAAE,KAAK,IAAI,OAAO;AAC/B,OAAO;AACP,MAAM,EAAE;AACR,MAAM,EAAE;AACR,KAAK,CAAC,gEAAgE,EAAE,aAAa,CAAC,MAAM,EAAE,eAAe,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE;AAC3M,MAAM,OAAO,EAAE,MAAM;AACrB,QAAQ,OAAO,CAAC,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,sBAAsB,GAAG,CAAC,EAAE,kBAAkB,CAAC,gBAAgB,EAAE,kBAAkB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,cAAc,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1T,OAAO;AACP,KAAK,CAAC,CAAC,cAAc,EAAE;AACvB,MAAM,iCAAiC;AACvC,MAAM,OAAO,CAAC,MAAM,GAAG,CAAC,GAAG,gBAAgB,GAAG,EAAE;AAChD,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC,EAAE,OAAO,EAAE,KAAK,GAAG,MAAM,GAAG,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ;AAC3H,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,MAAM,EAAE,KAAK,KAAK,IAAI,IAAI,aAAa,KAAK,QAAQ;AAC5D,QAAQ,QAAQ,EAAE,aAAa,KAAK,WAAW,GAAG,WAAW,GAAG,SAAS;AACzE,QAAQ,IAAI;AACZ,QAAQ,aAAa;AACrB,QAAQ,aAAa,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,KAAK,IAAI;AACpE,QAAQ,MAAM;AACd,QAAQ,cAAc;AACtB,QAAQ,UAAU,EAAE,IAAI,CAAC,sBAAsB,CAAC;AAChD,QAAQ,IAAI,EAAE,YAAY;AAC1B,QAAQ,SAAS;AACjB,QAAQ,QAAQ;AAChB,OAAO;AACP,MAAM;AACN,QAAQ,IAAI,EAAE,CAAC,OAAO,KAAK;AAC3B,UAAU,YAAY,GAAG,OAAO,CAAC;AACjC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,SAAS,EAAE,CAAC,OAAO,KAAK;AAChC,UAAU,SAAS,GAAG,OAAO,CAAC;AAC9B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,QAAQ,EAAE,CAAC,OAAO,KAAK;AAC/B,UAAU,QAAQ,GAAG,OAAO,CAAC;AAC7B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,KAAK,KAAK,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACxF,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,EAAE,aAAa,KAAK,QAAQ,KAAK,SAAS,IAAI,CAAC,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,QAAQ;AAC/H,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,IAAI;AACZ,QAAQ,KAAK;AACb,QAAQ,aAAa,EAAE,cAAc,CAAC,MAAM;AAC5C,QAAQ,YAAY;AACpB,QAAQ,SAAS;AACjB,QAAQ,IAAI,EAAE,OAAO;AACrB,QAAQ,aAAa,EAAE,KAAK;AAC5B,QAAQ,IAAI;AACZ,QAAQ,MAAM;AACd,QAAQ,kBAAkB,EAAE,cAAc,CAAC,WAAW;AACtD,QAAQ,aAAa;AACrB,QAAQ,cAAc;AACtB,OAAO;AACP,MAAM;AACN,QAAQ,aAAa,EAAE,CAAC,OAAO,KAAK;AACpC,UAAU,aAAa,GAAG,OAAO,CAAC;AAClC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,cAAc,EAAE,CAAC,OAAO,KAAK;AACrC,UAAU,cAAc,GAAG,OAAO,CAAC;AACnC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,KAAK,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,cAAc,EAAE,CAAC,4BAA4B,EAAE,UAAU,GAAG,YAAY,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,kBAAkB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ;AACpY,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO;AACf,QAAQ,YAAY;AACpB,QAAQ,aAAa,EAAE,oBAAoB;AAC3C,QAAQ,aAAa;AACrB,OAAO;AACP,MAAM;AACN,QAAQ,aAAa,EAAE,CAAC,OAAO,KAAK;AACpC,UAAU,aAAa,GAAG,OAAO,CAAC;AAClC,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM,EAAE;AACR,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACtB,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC,CAAC;AACE,MAAC,eAAe,GAAG,cAAc;AACjC,MAAC,KAAK,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7E,EAAE,IAAI,YAAY,GAAG,QAAQ,CAAC;AAC9B,EAAE,IAAI,cAAc,GAAG,MAAM;AAC7B,GAAG,CAAC;AACJ,EAAE,SAAS,mBAAmB,CAAC,KAAK,EAAE;AACtC,IAAI,YAAY,GAAG,KAAK,CAAC;AACzB,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC;AAC1B,GAAG;AACH,EAAE,MAAM,gBAAgB,GAAG,MAAM,YAAY,CAAC;AAC9C,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,eAAe,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC5C,EAAE,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,YAAY,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC;AACvB,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,OAAO,CAAC;AAC/B,EAAE,IAAI,EAAE,oBAAoB,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,YAAY,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACxC,EAAE,IAAI,EAAE,SAAS,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACjC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,iBAAiB,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC9C,EAAE,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,QAAQ,CAAC,EAAE,GAAG,OAAO,CAAC;AAChE,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;AAC5B,EAAE,IAAI,EAAE,WAAW,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,sBAAsB,EAAE,GAAG,OAAO,CAAC;AAC3C,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,UAAU,GAAG,KAAK,CAAC;AACzB,EAAE,IAAI,SAAS,GAAG,KAAK,CAAC;AACxB,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAI3B,EAAE,IAAI,QAAQ,CAAC;AACf,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC;AAC3B,EAAE,IAAI,gBAAgB,CAAC;AACvB,EAAE,IAAI,OAAO,CAAC,mBAAmB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,mBAAmB,IAAI,mBAAmB,KAAK,KAAK,CAAC;AAChH,IAAI,UAAU,CAAC,mBAAmB,CAAC,mBAAmB,CAAC,CAAC;AACxD,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,YAAY,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,YAAY,IAAI,YAAY,KAAK,KAAK,CAAC;AAC3F,IAAI,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,iBAAiB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,iBAAiB,IAAI,iBAAiB,KAAK,KAAK,CAAC;AAC1G,IAAI,UAAU,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC;AACpD,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,sBAAsB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,sBAAsB,IAAI,sBAAsB,KAAK,KAAK,CAAC;AACzH,IAAI,UAAU,CAAC,sBAAsB,CAAC,sBAAsB,CAAC,CAAC;AAC9D,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI,WAAW,GAAG,CAAC,SAAS,CAAC;AAC7B,IAAI;AACJ,MAAM;AACN,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE;AACjE,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,UAAU,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACpC,UAAU,IAAI,CAAC,eAAe,EAAE;AAChC,YAAY,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AACrC,WAAW;AACX,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,UAAU,GAAG,CAAC,GAAG,EAAE,CAAC,WAAW,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACpF,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO;AACf,QAAQ,OAAO,EAAE,OAAO;AACxB,QAAQ,WAAW,EAAE,QAAQ,GAAG,OAAO,GAAG,MAAM;AAChD,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,OAAO;AACf,QAAQ,YAAY;AACpB,QAAQ,MAAM,EAAE,MAAM,IAAI,KAAK,CAAC;AAChC,QAAQ,KAAK;AACb,QAAQ,cAAc,EAAE,KAAK;AAC7B,QAAQ,SAAS;AACjB,QAAQ,KAAK;AACb,QAAQ,SAAS;AACjB,QAAQ,UAAU;AAClB,OAAO;AACP,MAAM;AACN,QAAQ,UAAU,EAAE,CAAC,OAAO,KAAK;AACjC,UAAU,UAAU,GAAG,OAAO,CAAC;AAC/B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC,QAAQ;AACnP,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,UAAU;AACxB,cAAc,KAAK;AACnB,cAAc,KAAK;AACnB,cAAc,UAAU;AACxB,cAAc,oBAAoB;AAClC,cAAc,UAAU,EAAE,WAAW;AACrC,cAAc,iBAAiB;AAC/B,cAAc,IAAI,EAAE,MAAM,CAAC,IAAI;AAC/B,cAAc,sBAAsB;AACpC,aAAa;AACb,YAAY,EAAE;AACd,YAAY,EAAE;AACd,WAAW,CAAC,CAAC,CAAC;AACd,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AACxD,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO;AACf,QAAQ,OAAO,EAAE,KAAK,KAAK,IAAI,GAAG,QAAQ,GAAG,OAAO;AACpD,QAAQ,WAAW,EAAE,QAAQ,GAAG,OAAO,GAAG,MAAM;AAChD,QAAQ,OAAO,EAAE,KAAK;AACtB,QAAQ,OAAO;AACf,QAAQ,YAAY;AACpB,QAAQ,MAAM,EAAE,MAAM,IAAI,KAAK,CAAC;AAChC,QAAQ,KAAK;AACb,QAAQ,cAAc,EAAE,KAAK;AAC7B,QAAQ,SAAS;AACjB,QAAQ,KAAK;AACb,QAAQ,SAAS;AACjB,QAAQ,UAAU;AAClB,OAAO;AACP,MAAM;AACN,QAAQ,UAAU,EAAE,CAAC,OAAO,KAAK;AACjC,UAAU,UAAU,GAAG,OAAO,CAAC;AAC/B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC,QAAQ;AACxP,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,UAAU,EAAE,WAAW;AACrC,cAAc,IAAI;AAClB,cAAc,OAAO;AACrB,cAAc,UAAU;AACxB,cAAc,KAAK;AACnB,cAAc,UAAU;AACxB,cAAc,OAAO;AACrB,cAAc,SAAS;AACvB,cAAc,cAAc;AAC5B,cAAc,YAAY;AAC1B,cAAc,aAAa,EAAE,MAAM,CAAC,aAAa;AACjD,cAAc,IAAI,EAAE,MAAM,CAAC,IAAI;AAC/B,cAAc,MAAM,EAAE,CAAC,GAAG,IAAI,KAAK,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAChE,cAAc,cAAc,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM;AACnD,cAAc,IAAI,EAAE,gBAAgB;AACpC,cAAc,SAAS;AACvB,cAAc,aAAa;AAC3B,cAAc,KAAK;AACnB,cAAc,QAAQ;AACtB,cAAc,aAAa,EAAE,cAAc;AAC3C,cAAc,cAAc;AAC5B,aAAa;AACb,YAAY;AACZ,cAAc,IAAI,EAAE,CAAC,OAAO,KAAK;AACjC,gBAAgB,gBAAgB,GAAG,OAAO,CAAC;AAC3C,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,cAAc,SAAS,EAAE,CAAC,OAAO,KAAK;AACtC,gBAAgB,SAAS,GAAG,OAAO,CAAC;AACpC,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,cAAc,aAAa,EAAE,CAAC,OAAO,KAAK;AAC1C,gBAAgB,aAAa,GAAG,OAAO,CAAC;AACxC,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,cAAc,KAAK,EAAE,CAAC,OAAO,KAAK;AAClC,gBAAgB,KAAK,GAAG,OAAO,CAAC;AAChC,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,cAAc,QAAQ,EAAE,CAAC,OAAO,KAAK;AACrC,gBAAgB,QAAQ,GAAG,OAAO,CAAC;AACnC,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,cAAc,aAAa,EAAE,CAAC,OAAO,KAAK;AAC1C,gBAAgB,cAAc,GAAG,OAAO,CAAC;AACzC,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,cAAc,cAAc,EAAE,CAAC,OAAO,KAAK;AAC3C,gBAAgB,cAAc,GAAG,OAAO,CAAC;AACzC,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,aAAa;AACb,YAAY;AACZ,cAAc,OAAO,EAAE,MAAM;AAC7B,gBAAgB,OAAO,CAAC,EAAE,aAAa,KAAK,QAAQ,IAAI,CAAC,aAAa,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACjI,kBAAkB,QAAQ;AAC1B,kBAAkB;AAClB,oBAAoB,IAAI,EAAE,MAAM,CAAC,IAAI;AACrC,oBAAoB,IAAI,EAAE,OAAO;AACjC,oBAAoB,WAAW;AAC/B,mBAAmB;AACnB,kBAAkB,EAAE;AACpB,kBAAkB,EAAE;AACpB,iBAAiB,CAAC,CAAC,GAAG,CAAC,EAAE,aAAa,KAAK,WAAW,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACjH,kBAAkB,QAAQ;AAC1B,kBAAkB;AAClB,oBAAoB,IAAI,EAAE,MAAM,CAAC,IAAI;AACrC,oBAAoB,IAAI,EAAE,WAAW;AACrC,oBAAoB,IAAI,EAAE,OAAO;AACjC,mBAAmB;AACnB,kBAAkB,EAAE;AACpB,kBAAkB,EAAE;AACpB,iBAAiB,CAAC,CAAC,GAAG,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE;AAC1H,kBAAkB,OAAO,EAAE,MAAM;AACjC,oBAAoB,OAAO,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AAClG,mBAAmB;AACnB,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzB,eAAe;AACf,aAAa;AACb,WAAW,CAAC,CAAC,CAAC;AACd,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACV,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC;;;;"}