{"version": 3, "file": "yaml-BZBlrf2X.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/yaml.js"], "sourcesContent": ["var cons = [\"true\", \"false\", \"on\", \"off\", \"yes\", \"no\"];\nvar keywordRegex = new RegExp(\"\\\\b((\" + cons.join(\")|(\") + \"))$\", \"i\");\nconst yaml = {\n  name: \"yaml\",\n  token: function(stream, state) {\n    var ch = stream.peek();\n    var esc = state.escaped;\n    state.escaped = false;\n    if (ch == \"#\" && (stream.pos == 0 || /\\s/.test(stream.string.charAt(stream.pos - 1)))) {\n      stream.skipToEnd();\n      return \"comment\";\n    }\n    if (stream.match(/^('([^']|\\\\.)*'?|\"([^\"]|\\\\.)*\"?)/))\n      return \"string\";\n    if (state.literal && stream.indentation() > state.keyCol) {\n      stream.skipToEnd();\n      return \"string\";\n    } else if (state.literal) {\n      state.literal = false;\n    }\n    if (stream.sol()) {\n      state.keyCol = 0;\n      state.pair = false;\n      state.pairStart = false;\n      if (stream.match(\"---\")) {\n        return \"def\";\n      }\n      if (stream.match(\"...\")) {\n        return \"def\";\n      }\n      if (stream.match(/^\\s*-\\s+/)) {\n        return \"meta\";\n      }\n    }\n    if (stream.match(/^(\\{|\\}|\\[|\\])/)) {\n      if (ch == \"{\")\n        state.inlinePairs++;\n      else if (ch == \"}\")\n        state.inlinePairs--;\n      else if (ch == \"[\")\n        state.inlineList++;\n      else\n        state.inlineList--;\n      return \"meta\";\n    }\n    if (state.inlineList > 0 && !esc && ch == \",\") {\n      stream.next();\n      return \"meta\";\n    }\n    if (state.inlinePairs > 0 && !esc && ch == \",\") {\n      state.keyCol = 0;\n      state.pair = false;\n      state.pairStart = false;\n      stream.next();\n      return \"meta\";\n    }\n    if (state.pairStart) {\n      if (stream.match(/^\\s*(\\||\\>)\\s*/)) {\n        state.literal = true;\n        return \"meta\";\n      }\n      if (stream.match(/^\\s*(\\&|\\*)[a-z0-9\\._-]+\\b/i)) {\n        return \"variable\";\n      }\n      if (state.inlinePairs == 0 && stream.match(/^\\s*-?[0-9\\.\\,]+\\s?$/)) {\n        return \"number\";\n      }\n      if (state.inlinePairs > 0 && stream.match(/^\\s*-?[0-9\\.\\,]+\\s?(?=(,|}))/)) {\n        return \"number\";\n      }\n      if (stream.match(keywordRegex)) {\n        return \"keyword\";\n      }\n    }\n    if (!state.pair && stream.match(/^\\s*(?:[,\\[\\]{}&*!|>'\"%@`][^\\s'\":]|[^,\\[\\]{}#&*!|>'\"%@`])[^#]*?(?=\\s*:($|\\s))/)) {\n      state.pair = true;\n      state.keyCol = stream.indentation();\n      return \"atom\";\n    }\n    if (state.pair && stream.match(/^:\\s*/)) {\n      state.pairStart = true;\n      return \"meta\";\n    }\n    state.pairStart = false;\n    state.escaped = ch == \"\\\\\";\n    stream.next();\n    return null;\n  },\n  startState: function() {\n    return {\n      pair: false,\n      pairStart: false,\n      keyCol: 0,\n      inlinePairs: 0,\n      inlineList: 0,\n      literal: false,\n      escaped: false\n    };\n  },\n  languageData: {\n    commentTokens: { line: \"#\" }\n  }\n};\nexport {\n  yaml\n};\n"], "names": [], "mappings": "AAAA,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;AACvD,IAAI,YAAY,GAAG,IAAI,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,EAAE,GAAG,CAAC,CAAC;AAClE,MAAC,IAAI,GAAG;AACb,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,KAAK,EAAE,SAAS,MAAM,EAAE,KAAK,EAAE;AACjC,IAAI,IAAI,EAAE,GAAG,MAAM,CAAC,IAAI,EAAE,CAAC;AAC3B,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC;AAC5B,IAAI,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC;AAC1B,IAAI,IAAI,EAAE,IAAI,GAAG,KAAK,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;AAC3F,MAAM,MAAM,CAAC,SAAS,EAAE,CAAC;AACzB,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,kCAAkC,CAAC;AACxD,MAAM,OAAO,QAAQ,CAAC;AACtB,IAAI,IAAI,KAAK,CAAC,OAAO,IAAI,MAAM,CAAC,WAAW,EAAE,GAAG,KAAK,CAAC,MAAM,EAAE;AAC9D,MAAM,MAAM,CAAC,SAAS,EAAE,CAAC;AACzB,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK,MAAM,IAAI,KAAK,CAAC,OAAO,EAAE;AAC9B,MAAM,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC;AAC5B,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,GAAG,EAAE,EAAE;AACtB,MAAM,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AACvB,MAAM,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;AACzB,MAAM,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;AAC9B,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AAC/B,QAAQ,OAAO,KAAK,CAAC;AACrB,OAAO;AACP,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AAC/B,QAAQ,OAAO,KAAK,CAAC;AACrB,OAAO;AACP,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,EAAE;AACpC,QAAQ,OAAO,MAAM,CAAC;AACtB,OAAO;AACP,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE;AACxC,MAAM,IAAI,EAAE,IAAI,GAAG;AACnB,QAAQ,KAAK,CAAC,WAAW,EAAE,CAAC;AAC5B,WAAW,IAAI,EAAE,IAAI,GAAG;AACxB,QAAQ,KAAK,CAAC,WAAW,EAAE,CAAC;AAC5B,WAAW,IAAI,EAAE,IAAI,GAAG;AACxB,QAAQ,KAAK,CAAC,UAAU,EAAE,CAAC;AAC3B;AACA,QAAQ,KAAK,CAAC,UAAU,EAAE,CAAC;AAC3B,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,IAAI,GAAG,EAAE;AACnD,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;AACpB,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,IAAI,GAAG,EAAE;AACpD,MAAM,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AACvB,MAAM,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC;AACzB,MAAM,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;AAC9B,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;AACpB,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,SAAS,EAAE;AACzB,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE;AAC1C,QAAQ,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;AAC7B,QAAQ,OAAO,MAAM,CAAC;AACtB,OAAO;AACP,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,6BAA6B,CAAC,EAAE;AACvD,QAAQ,OAAO,UAAU,CAAC;AAC1B,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,WAAW,IAAI,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,sBAAsB,CAAC,EAAE;AAC1E,QAAQ,OAAO,QAAQ,CAAC;AACxB,OAAO;AACP,MAAM,IAAI,KAAK,CAAC,WAAW,GAAG,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,8BAA8B,CAAC,EAAE;AACjF,QAAQ,OAAO,QAAQ,CAAC;AACxB,OAAO;AACP,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;AACtC,QAAQ,OAAO,SAAS,CAAC;AACzB,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,+EAA+E,CAAC,EAAE;AACtH,MAAM,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;AACxB,MAAM,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;AAC1C,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK;AACL,IAAI,IAAI,KAAK,CAAC,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;AAC7C,MAAM,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;AAC7B,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK;AACL,IAAI,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC;AAC5B,IAAI,KAAK,CAAC,OAAO,GAAG,EAAE,IAAI,IAAI,CAAC;AAC/B,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;AAClB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,UAAU,EAAE,WAAW;AACzB,IAAI,OAAO;AACX,MAAM,IAAI,EAAE,KAAK;AACjB,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,MAAM,EAAE,CAAC;AACf,MAAM,WAAW,EAAE,CAAC;AACpB,MAAM,UAAU,EAAE,CAAC;AACnB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,OAAO,EAAE,KAAK;AACpB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,aAAa,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE;AAChC,GAAG;AACH;;;;"}