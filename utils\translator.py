"""
翻译引擎
基于参考文件分析翻译规则并执行翻译任务
"""

import os
import tempfile
from typing import List, Dict, Optional, Callable
from .document_parser import DocumentParser
from .openrouter_client import OpenRouterClient

# from tqdm import tqdm  # 如果需要进度条可以取消注释


class DocumentTranslator:
    def __init__(self, api_key: Optional[str] = None):
        self.parser = DocumentParser()
        self.client = OpenRouterClient(api_key)

    def analyze_reference_files(self, ref_file1_path: str, ref_file2_path: str) -> Dict:
        """
        分析参考文件，提取翻译规则和术语对应关系

        Args:
            ref_file1_path: 参考文件1路径
            ref_file2_path: 参考文件2路径

        Returns:
            翻译分析结果
        """
        try:
            # 提取翻译对应关系
            translation_data = self.parser.extract_translation_pairs(
                ref_file1_path, ref_file2_path
            )

            # 分析翻译风格和规则
            style_analysis = self._analyze_translation_style(translation_data["pairs"])

            return {
                "translation_pairs": translation_data["pairs"],
                "terminology": translation_data["terminology"],
                "context": translation_data["context"],
                "style_analysis": style_analysis,
                "total_pairs": len(translation_data["pairs"]),
                "terminology_count": len(translation_data["terminology"]),
            }

        except Exception as e:
            raise Exception(f"分析参考文件失败: {str(e)}")

    def translate_document(
        self,
        target_file_path: str,
        reference_analysis: Dict,
        model_id: str,
        system_message: str,
        progress_callback: Optional[Callable] = None,
    ) -> str:
        """
        翻译目标文档

        Args:
            target_file_path: 目标文件路径
            reference_analysis: 参考文件分析结果
            model_id: 使用的模型ID
            system_message: 系统提示词
            progress_callback: 进度回调函数

        Returns:
            翻译后的文档路径
        """
        try:
            # 解析目标文档
            target_paragraphs = self.parser.parse_document(target_file_path)

            if not target_paragraphs:
                raise ValueError("目标文档为空或无法解析")

            # 准备翻译上下文
            context = self._prepare_translation_context(reference_analysis)

            # 翻译每个段落
            translated_paragraphs = []
            total_paragraphs = len(target_paragraphs)

            for i, paragraph in enumerate(target_paragraphs):
                if progress_callback:
                    progress_callback(
                        i / total_paragraphs, f"正在翻译第 {i+1}/{total_paragraphs} 段"
                    )

                # 翻译段落文本
                translated_text = self.client.translate_text(
                    text=paragraph["text"],
                    model_id=model_id,
                    system_message=system_message,
                    reference_context=context,
                )

                # 保持段落结构
                translated_paragraph = paragraph.copy()
                translated_paragraph["text"] = translated_text
                translated_paragraphs.append(translated_paragraph)

            # 创建输出文件
            output_filename = self._generate_output_filename(target_file_path)
            output_path = os.path.join(tempfile.gettempdir(), output_filename)

            # 生成翻译后的文档
            final_output = self.parser.create_docx_from_paragraphs(
                translated_paragraphs, output_path
            )

            if progress_callback:
                progress_callback(1.0, "翻译完成")

            return final_output

        except Exception as e:
            raise Exception(f"翻译文档失败: {str(e)}")

    def _analyze_translation_style(self, pairs: List[Dict]) -> Dict:
        """分析翻译风格"""
        if not pairs:
            return {}

        # 分析翻译特点
        analysis = {
            "avg_source_length": sum(len(p["source"]) for p in pairs) / len(pairs),
            "avg_target_length": sum(len(p["target"]) for p in pairs) / len(pairs),
            "formal_indicators": [],
            "common_patterns": [],
        }

        # 检测正式文档的特征
        formal_keywords = ["hereby", "whereas", "pursuant", "特此", "兹", "根据"]
        for keyword in formal_keywords:
            count = sum(
                1 for p in pairs if keyword in p["source"] or keyword in p["target"]
            )
            if count > 0:
                analysis["formal_indicators"].append(
                    {"keyword": keyword, "frequency": count / len(pairs)}
                )

        return analysis

    def _prepare_translation_context(self, reference_analysis: Dict) -> str:
        """准备翻译上下文"""
        context_parts = []

        # 添加术语表
        if reference_analysis.get("terminology"):
            context_parts.append("专业术语对照表：")
            for source_term, target_term in list(
                reference_analysis["terminology"].items()
            )[:10]:
                context_parts.append(f"- {source_term} → {target_term}")

        # 添加翻译示例
        if reference_analysis.get("context"):
            context_parts.append("\n" + reference_analysis["context"])

        # 添加风格指导
        style_analysis = reference_analysis.get("style_analysis", {})
        if style_analysis.get("formal_indicators"):
            context_parts.append("\n翻译风格：正式文档，注意使用正式用语")

        return "\n".join(context_parts)

    def _generate_output_filename(self, input_path: str) -> str:
        """生成输出文件名"""
        base_name = os.path.splitext(os.path.basename(input_path))[0]
        return f"{base_name}_translated.docx"

    def get_translation_summary(self, reference_analysis: Dict) -> str:
        """获取翻译分析摘要"""
        summary_parts = []

        summary_parts.append(f"✅ 成功分析参考文件")
        summary_parts.append(
            f"📊 提取翻译对: {reference_analysis.get('total_pairs', 0)} 组"
        )
        summary_parts.append(
            f"📝 术语对照: {reference_analysis.get('terminology_count', 0)} 个"
        )

        style_analysis = reference_analysis.get("style_analysis", {})
        if style_analysis.get("formal_indicators"):
            summary_parts.append(f"📋 检测到正式文档特征")

        return "\n".join(summary_parts)
