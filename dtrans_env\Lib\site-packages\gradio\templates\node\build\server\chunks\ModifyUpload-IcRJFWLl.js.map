{"version": 3, "file": "ModifyUpload-IcRJFWLl.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/ModifyUpload.js"], "sourcesContent": ["import { create_ssr_component, escape, add_attribute, add_styles, validate_component } from \"svelte/internal\";\nimport { createEventDispatcher, onMount, onDestroy, tick } from \"svelte\";\nimport { Y as prepare_files, I as IconButtonWrapper, h as <PERSON><PERSON><PERSON><PERSON><PERSON>, Z as Edit, _ as Undo, D as Download, G as Clear } from \"./client.js\";\nimport { D as DownloadLink } from \"./DownloadLink.js\";\nconst css$1 = {\n  code: '.wrap.svelte-1vsfomn.svelte-1vsfomn{overflow-y:auto;transition:opacity 0.5s ease-in-out;background:var(--block-background-fill);position:relative;display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:var(--size-40);width:var(--size-full)}.wrap.svelte-1vsfomn.svelte-1vsfomn::after{content:\"\";position:absolute;top:0;left:0;width:var(--upload-progress-width);height:100%;transition:all 0.5s ease-in-out;z-index:1}.uploading.svelte-1vsfomn.svelte-1vsfomn{font-size:var(--text-lg);font-family:var(--font);z-index:2}.file-name.svelte-1vsfomn.svelte-1vsfomn{margin:var(--spacing-md);font-size:var(--text-lg);color:var(--body-text-color-subdued)}.file.svelte-1vsfomn.svelte-1vsfomn{font-size:var(--text-md);z-index:2;display:flex;align-items:center}.file.svelte-1vsfomn progress.svelte-1vsfomn{display:inline;height:var(--size-1);width:100%;transition:all 0.5s ease-in-out;color:var(--color-accent);border:none}.file.svelte-1vsfomn progress[value].svelte-1vsfomn::-webkit-progress-value{background-color:var(--color-accent);border-radius:20px}.file.svelte-1vsfomn progress[value].svelte-1vsfomn::-webkit-progress-bar{background-color:var(--border-color-accent);border-radius:20px}.progress-bar.svelte-1vsfomn.svelte-1vsfomn{width:14px;height:14px;border-radius:50%;background:radial-gradient(\\n\t\t\t\tclosest-side,\\n\t\t\t\tvar(--block-background-fill) 64%,\\n\t\t\t\ttransparent 53% 100%\\n\t\t\t),\\n\t\t\tconic-gradient(\\n\t\t\t\tvar(--color-accent) var(--upload-progress-width),\\n\t\t\t\tvar(--border-color-accent) 0\\n\t\t\t);transition:all 0.5s ease-in-out}',\n  map: '{\"version\":3,\"file\":\"UploadProgress.svelte\",\"sources\":[\"UploadProgress.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { FileData } from \\\\\"@gradio/client\\\\\";\\\\nimport { onMount, createEventDispatcher, onDestroy } from \\\\\"svelte\\\\\";\\\\nexport let upload_id;\\\\nexport let root;\\\\nexport let files;\\\\nexport let stream_handler;\\\\nlet stream;\\\\nlet progress = false;\\\\nlet current_file_upload;\\\\nlet file_to_display;\\\\nlet files_with_progress = files.map((file) => {\\\\n    return {\\\\n        ...file,\\\\n        progress: 0\\\\n    };\\\\n});\\\\nconst dispatch = createEventDispatcher();\\\\nfunction handleProgress(filename, chunk_size) {\\\\n    files_with_progress = files_with_progress.map((file) => {\\\\n        if (file.orig_name === filename) {\\\\n            file.progress += chunk_size;\\\\n        }\\\\n        return file;\\\\n    });\\\\n}\\\\nfunction getProgress(file) {\\\\n    return file.progress * 100 / (file.size || 0) || 0;\\\\n}\\\\nonMount(async () => {\\\\n    stream = await stream_handler(new URL(`${root}/gradio_api/upload_progress?upload_id=${upload_id}`));\\\\n    if (stream == null) {\\\\n        throw new Error(\\\\\"Event source is not defined\\\\\");\\\\n    }\\\\n    stream.onmessage = async function (event) {\\\\n        const _data = JSON.parse(event.data);\\\\n        if (!progress)\\\\n            progress = true;\\\\n        if (_data.msg === \\\\\"done\\\\\") {\\\\n            stream?.close();\\\\n            dispatch(\\\\\"done\\\\\");\\\\n        }\\\\n        else {\\\\n            current_file_upload = _data;\\\\n            handleProgress(_data.orig_name, _data.chunk_size);\\\\n        }\\\\n    };\\\\n});\\\\nonDestroy(() => {\\\\n    if (stream != null || stream != void 0)\\\\n        stream.close();\\\\n});\\\\nfunction calculateTotalProgress(files2) {\\\\n    let totalProgress = 0;\\\\n    files2.forEach((file) => {\\\\n        totalProgress += getProgress(file);\\\\n    });\\\\n    document.documentElement.style.setProperty(\\\\\"--upload-progress-width\\\\\", (totalProgress / files2.length).toFixed(2) + \\\\\"%\\\\\");\\\\n    return totalProgress / files2.length;\\\\n}\\\\n$: calculateTotalProgress(files_with_progress);\\\\n$: file_to_display = current_file_upload || files_with_progress[0];\\\\n<\\/script>\\\\n\\\\n<div class=\\\\\"wrap\\\\\" class:progress>\\\\n\\\\t<span class=\\\\\"uploading\\\\\"\\\\n\\\\t\\\\t>Uploading {files_with_progress.length}\\\\n\\\\t\\\\t{files_with_progress.length > 1 ? \\\\\"files\\\\\" : \\\\\"file\\\\\"}...</span\\\\n\\\\t>\\\\n\\\\n\\\\t{#if file_to_display}\\\\n\\\\t\\\\t<div class=\\\\\"file\\\\\">\\\\n\\\\t\\\\t\\\\t<span>\\\\n\\\\t\\\\t\\\\t\\\\t<div class=\\\\\"progress-bar\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t<progress\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tstyle=\\\\\"visibility:hidden;height:0;width:0;\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tvalue={getProgress(file_to_display)}\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t\\\\tmax=\\\\\"100\\\\\">{getProgress(file_to_display)}</progress\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t>\\\\n\\\\t\\\\t\\\\t\\\\t</div>\\\\n\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t\\\\t<span class=\\\\\"file-name\\\\\">\\\\n\\\\t\\\\t\\\\t\\\\t{file_to_display.orig_name}\\\\n\\\\t\\\\t\\\\t</span>\\\\n\\\\t\\\\t</div>\\\\n\\\\t{/if}\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.wrap {\\\\n\\\\t\\\\toverflow-y: auto;\\\\n\\\\t\\\\ttransition: opacity 0.5s ease-in-out;\\\\n\\\\t\\\\tbackground: var(--block-background-fill);\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\tmin-height: var(--size-40);\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t}\\\\n\\\\n\\\\t.wrap::after {\\\\n\\\\t\\\\tcontent: \\\\\"\\\\\";\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\ttop: 0;\\\\n\\\\t\\\\tleft: 0;\\\\n\\\\t\\\\twidth: var(--upload-progress-width);\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\ttransition: all 0.5s ease-in-out;\\\\n\\\\t\\\\tz-index: 1;\\\\n\\\\t}\\\\n\\\\n\\\\t.uploading {\\\\n\\\\t\\\\tfont-size: var(--text-lg);\\\\n\\\\t\\\\tfont-family: var(--font);\\\\n\\\\t\\\\tz-index: 2;\\\\n\\\\t}\\\\n\\\\n\\\\t.file-name {\\\\n\\\\t\\\\tmargin: var(--spacing-md);\\\\n\\\\t\\\\tfont-size: var(--text-lg);\\\\n\\\\t\\\\tcolor: var(--body-text-color-subdued);\\\\n\\\\t}\\\\n\\\\n\\\\t.file {\\\\n\\\\t\\\\tfont-size: var(--text-md);\\\\n\\\\t\\\\tz-index: 2;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t}\\\\n\\\\n\\\\t.file progress {\\\\n\\\\t\\\\tdisplay: inline;\\\\n\\\\t\\\\theight: var(--size-1);\\\\n\\\\t\\\\twidth: 100%;\\\\n\\\\t\\\\ttransition: all 0.5s ease-in-out;\\\\n\\\\t\\\\tcolor: var(--color-accent);\\\\n\\\\t\\\\tborder: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.file progress[value]::-webkit-progress-value {\\\\n\\\\t\\\\tbackground-color: var(--color-accent);\\\\n\\\\t\\\\tborder-radius: 20px;\\\\n\\\\t}\\\\n\\\\n\\\\t.file progress[value]::-webkit-progress-bar {\\\\n\\\\t\\\\tbackground-color: var(--border-color-accent);\\\\n\\\\t\\\\tborder-radius: 20px;\\\\n\\\\t}\\\\n\\\\n\\\\t.progress-bar {\\\\n\\\\t\\\\twidth: 14px;\\\\n\\\\t\\\\theight: 14px;\\\\n\\\\t\\\\tborder-radius: 50%;\\\\n\\\\t\\\\tbackground: radial-gradient(\\\\n\\\\t\\\\t\\\\t\\\\tclosest-side,\\\\n\\\\t\\\\t\\\\t\\\\tvar(--block-background-fill) 64%,\\\\n\\\\t\\\\t\\\\t\\\\ttransparent 53% 100%\\\\n\\\\t\\\\t\\\\t),\\\\n\\\\t\\\\t\\\\tconic-gradient(\\\\n\\\\t\\\\t\\\\t\\\\tvar(--color-accent) var(--upload-progress-width),\\\\n\\\\t\\\\t\\\\t\\\\tvar(--border-color-accent) 0\\\\n\\\\t\\\\t\\\\t);\\\\n\\\\t\\\\ttransition: all 0.5s ease-in-out;\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAwFC,mCAAM,CACL,UAAU,CAAE,IAAI,CAChB,UAAU,CAAE,OAAO,CAAC,IAAI,CAAC,WAAW,CACpC,UAAU,CAAE,IAAI,uBAAuB,CAAC,CACxC,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,UAAU,CAAE,IAAI,SAAS,CAAC,CAC1B,KAAK,CAAE,IAAI,WAAW,CACvB,CAEA,mCAAK,OAAQ,CACZ,OAAO,CAAE,EAAE,CACX,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,CAAC,CACN,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,IAAI,uBAAuB,CAAC,CACnC,MAAM,CAAE,IAAI,CACZ,UAAU,CAAE,GAAG,CAAC,IAAI,CAAC,WAAW,CAChC,OAAO,CAAE,CACV,CAEA,wCAAW,CACV,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,WAAW,CAAE,IAAI,MAAM,CAAC,CACxB,OAAO,CAAE,CACV,CAEA,wCAAW,CACV,MAAM,CAAE,IAAI,YAAY,CAAC,CACzB,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,KAAK,CAAE,IAAI,yBAAyB,CACrC,CAEA,mCAAM,CACL,SAAS,CAAE,IAAI,SAAS,CAAC,CACzB,OAAO,CAAE,CAAC,CACV,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MACd,CAEA,oBAAK,CAAC,uBAAS,CACd,OAAO,CAAE,MAAM,CACf,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,KAAK,CAAE,IAAI,CACX,UAAU,CAAE,GAAG,CAAC,IAAI,CAAC,WAAW,CAChC,KAAK,CAAE,IAAI,cAAc,CAAC,CAC1B,MAAM,CAAE,IACT,CAEA,oBAAK,CAAC,QAAQ,CAAC,KAAK,gBAAC,wBAAyB,CAC7C,gBAAgB,CAAE,IAAI,cAAc,CAAC,CACrC,aAAa,CAAE,IAChB,CAEA,oBAAK,CAAC,QAAQ,CAAC,KAAK,gBAAC,sBAAuB,CAC3C,gBAAgB,CAAE,IAAI,qBAAqB,CAAC,CAC5C,aAAa,CAAE,IAChB,CAEA,2CAAc,CACb,KAAK,CAAE,IAAI,CACX,MAAM,CAAE,IAAI,CACZ,aAAa,CAAE,GAAG,CAClB,UAAU,CAAE;AACd,IAAI,YAAY,CAAC;AACjB,IAAI,IAAI,uBAAuB,CAAC,CAAC,GAAG,CAAC;AACrC,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI;AACxB,IAAI,CAAC;AACL,GAAG;AACH,IAAI,IAAI,cAAc,CAAC,CAAC,IAAI,uBAAuB,CAAC,CAAC;AACrD,IAAI,IAAI,qBAAqB,CAAC,CAAC,CAAC;AAChC,IAAI,CACF,UAAU,CAAE,GAAG,CAAC,IAAI,CAAC,WACtB\"}'\n};\nfunction getProgress(file) {\n  return file.progress * 100 / (file.size || 0) || 0;\n}\nfunction calculateTotalProgress(files2) {\n  let totalProgress = 0;\n  files2.forEach((file) => {\n    totalProgress += getProgress(file);\n  });\n  document.documentElement.style.setProperty(\"--upload-progress-width\", (totalProgress / files2.length).toFixed(2) + \"%\");\n  return totalProgress / files2.length;\n}\nconst UploadProgress = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { upload_id } = $$props;\n  let { root } = $$props;\n  let { files } = $$props;\n  let { stream_handler } = $$props;\n  let stream;\n  let progress = false;\n  let current_file_upload;\n  let file_to_display;\n  let files_with_progress = files.map((file) => {\n    return { ...file, progress: 0 };\n  });\n  const dispatch = createEventDispatcher();\n  function handleProgress(filename, chunk_size) {\n    files_with_progress = files_with_progress.map((file) => {\n      if (file.orig_name === filename) {\n        file.progress += chunk_size;\n      }\n      return file;\n    });\n  }\n  onMount(async () => {\n    stream = await stream_handler(new URL(`${root}/gradio_api/upload_progress?upload_id=${upload_id}`));\n    if (stream == null) {\n      throw new Error(\"Event source is not defined\");\n    }\n    stream.onmessage = async function(event) {\n      const _data = JSON.parse(event.data);\n      if (!progress)\n        progress = true;\n      if (_data.msg === \"done\") {\n        stream?.close();\n        dispatch(\"done\");\n      } else {\n        current_file_upload = _data;\n        handleProgress(_data.orig_name, _data.chunk_size);\n      }\n    };\n  });\n  onDestroy(() => {\n    if (stream != null || stream != void 0)\n      stream.close();\n  });\n  if ($$props.upload_id === void 0 && $$bindings.upload_id && upload_id !== void 0)\n    $$bindings.upload_id(upload_id);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.files === void 0 && $$bindings.files && files !== void 0)\n    $$bindings.files(files);\n  if ($$props.stream_handler === void 0 && $$bindings.stream_handler && stream_handler !== void 0)\n    $$bindings.stream_handler(stream_handler);\n  $$result.css.add(css$1);\n  {\n    calculateTotalProgress(files_with_progress);\n  }\n  file_to_display = current_file_upload || files_with_progress[0];\n  return `<div class=\"${[\"wrap svelte-1vsfomn\", progress ? \"progress\" : \"\"].join(\" \").trim()}\"><span class=\"uploading svelte-1vsfomn\">Uploading ${escape(files_with_progress.length)} ${escape(files_with_progress.length > 1 ? \"files\" : \"file\")}...</span> ${file_to_display ? `<div class=\"file svelte-1vsfomn\"><span><div class=\"progress-bar svelte-1vsfomn\"><progress style=\"visibility:hidden;height:0;width:0;\"${add_attribute(\"value\", getProgress(file_to_display), 0)} max=\"100\" class=\"svelte-1vsfomn\">${escape(getProgress(file_to_display))}</progress></div></span> <span class=\"file-name svelte-1vsfomn\">${escape(file_to_display.orig_name)}</span></div>` : ``} </div>`;\n});\nfunction create_drag() {\n  let hidden_input;\n  let _options;\n  return {\n    drag(node, options = {}) {\n      _options = options;\n      function setup_hidden_input() {\n        hidden_input = document.createElement(\"input\");\n        hidden_input.type = \"file\";\n        hidden_input.style.display = \"none\";\n        hidden_input.setAttribute(\"aria-label\", \"File upload\");\n        hidden_input.setAttribute(\"data-testid\", \"file-upload\");\n        const accept_options = Array.isArray(_options.accepted_types) ? _options.accepted_types.join(\",\") : _options.accepted_types || void 0;\n        if (accept_options) {\n          hidden_input.accept = accept_options;\n        }\n        hidden_input.multiple = _options.mode === \"multiple\" || false;\n        if (_options.mode === \"directory\") {\n          hidden_input.webkitdirectory = true;\n          hidden_input.setAttribute(\"directory\", \"\");\n          hidden_input.setAttribute(\"mozdirectory\", \"\");\n        }\n        node.appendChild(hidden_input);\n      }\n      setup_hidden_input();\n      function handle_drag(e) {\n        e.preventDefault();\n        e.stopPropagation();\n      }\n      function handle_drag_enter(e) {\n        e.preventDefault();\n        e.stopPropagation();\n        _options.on_drag_change?.(true);\n      }\n      function handle_drag_leave(e) {\n        e.preventDefault();\n        e.stopPropagation();\n        _options.on_drag_change?.(false);\n      }\n      function handle_drop(e) {\n        e.preventDefault();\n        e.stopPropagation();\n        _options.on_drag_change?.(false);\n        if (!e.dataTransfer?.files)\n          return;\n        const files = Array.from(e.dataTransfer.files);\n        if (files.length > 0) {\n          _options.on_files?.(files);\n        }\n      }\n      function handle_click() {\n        if (!_options.disable_click) {\n          hidden_input.value = \"\";\n          hidden_input.click();\n        }\n      }\n      function handle_file_input_change() {\n        if (hidden_input.files) {\n          const files = Array.from(hidden_input.files);\n          if (files.length > 0) {\n            _options.on_files?.(files);\n          }\n        }\n      }\n      node.addEventListener(\"drag\", handle_drag);\n      node.addEventListener(\"dragstart\", handle_drag);\n      node.addEventListener(\"dragend\", handle_drag);\n      node.addEventListener(\"dragover\", handle_drag);\n      node.addEventListener(\"dragenter\", handle_drag_enter);\n      node.addEventListener(\"dragleave\", handle_drag_leave);\n      node.addEventListener(\"drop\", handle_drop);\n      node.addEventListener(\"click\", handle_click);\n      hidden_input.addEventListener(\"change\", handle_file_input_change);\n      return {\n        update(new_options) {\n          _options = new_options;\n          hidden_input.remove();\n          setup_hidden_input();\n          hidden_input.addEventListener(\"change\", handle_file_input_change);\n        },\n        destroy() {\n          node.removeEventListener(\"drag\", handle_drag);\n          node.removeEventListener(\"dragstart\", handle_drag);\n          node.removeEventListener(\"dragend\", handle_drag);\n          node.removeEventListener(\"dragover\", handle_drag);\n          node.removeEventListener(\"dragenter\", handle_drag_enter);\n          node.removeEventListener(\"dragleave\", handle_drag_leave);\n          node.removeEventListener(\"drop\", handle_drop);\n          node.removeEventListener(\"click\", handle_click);\n          hidden_input.removeEventListener(\"change\", handle_file_input_change);\n          hidden_input.remove();\n        }\n      };\n    },\n    open_file_upload() {\n      if (hidden_input) {\n        hidden_input.value = \"\";\n        hidden_input.click();\n      }\n    }\n  };\n}\nconst css = {\n  code: \"button.svelte-edrmkl{cursor:pointer;width:var(--size-full)}.center.svelte-edrmkl{display:flex;justify-content:center}.flex.svelte-edrmkl{display:flex;flex-direction:column;justify-content:center;align-items:center}.hidden.svelte-edrmkl{display:none;position:absolute;flex-grow:0}.hidden.svelte-edrmkl svg{display:none}.disable_click.svelte-edrmkl{cursor:default}.icon-mode.svelte-edrmkl{position:absolute !important;width:var(--size-4);height:var(--size-4);padding:0;min-height:0;border-radius:var(--radius-circle)}.icon-mode.svelte-edrmkl svg{width:var(--size-4);height:var(--size-4)}\",\n  map: '{\"version\":3,\"file\":\"Upload.svelte\",\"sources\":[\"Upload.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { createEventDispatcher, tick, getContext } from \\\\\"svelte\\\\\";\\\\nimport { prepare_files } from \\\\\"@gradio/client\\\\\";\\\\nimport { _ } from \\\\\"svelte-i18n\\\\\";\\\\nimport UploadProgress from \\\\\"./UploadProgress.svelte\\\\\";\\\\nimport { create_drag } from \\\\\"./utils\\\\\";\\\\nconst { drag, open_file_upload: _open_file_upload } = create_drag();\\\\nexport let filetype = null;\\\\nexport let dragging = false;\\\\nexport let boundedheight = true;\\\\nexport let center = true;\\\\nexport let flex = true;\\\\nexport let file_count = \\\\\"single\\\\\";\\\\nexport let disable_click = false;\\\\nexport let root;\\\\nexport let hidden = false;\\\\nexport let format = \\\\\"file\\\\\";\\\\nexport let uploading = false;\\\\nexport let show_progress = true;\\\\nexport let max_file_size = null;\\\\nexport let upload;\\\\nexport let stream_handler;\\\\nexport let icon_upload = false;\\\\nexport let height = void 0;\\\\nexport let aria_label = void 0;\\\\nexport function open_upload() {\\\\n    _open_file_upload();\\\\n}\\\\nlet upload_id;\\\\nlet file_data;\\\\nlet accept_file_types;\\\\nlet use_post_upload_validation = null;\\\\nconst get_ios = () => {\\\\n    if (typeof navigator !== \\\\\"undefined\\\\\") {\\\\n        const userAgent = navigator.userAgent.toLowerCase();\\\\n        return userAgent.indexOf(\\\\\"iphone\\\\\") > -1 || userAgent.indexOf(\\\\\"ipad\\\\\") > -1;\\\\n    }\\\\n    return false;\\\\n};\\\\n$: ios = get_ios();\\\\nconst dispatch = createEventDispatcher();\\\\nconst validFileTypes = [\\\\\"image\\\\\", \\\\\"video\\\\\", \\\\\"audio\\\\\", \\\\\"text\\\\\", \\\\\"file\\\\\"];\\\\nconst process_file_type = (type) => {\\\\n    if (ios && type.startsWith(\\\\\".\\\\\")) {\\\\n        use_post_upload_validation = true;\\\\n        return type;\\\\n    }\\\\n    if (ios && type.includes(\\\\\"file/*\\\\\")) {\\\\n        return \\\\\"*\\\\\";\\\\n    }\\\\n    if (type.startsWith(\\\\\".\\\\\") || type.endsWith(\\\\\"/*\\\\\")) {\\\\n        return type;\\\\n    }\\\\n    if (validFileTypes.includes(type)) {\\\\n        return type + \\\\\"/*\\\\\";\\\\n    }\\\\n    return \\\\\".\\\\\" + type;\\\\n};\\\\n$: if (filetype == null) {\\\\n    accept_file_types = null;\\\\n}\\\\nelse if (typeof filetype === \\\\\"string\\\\\") {\\\\n    accept_file_types = process_file_type(filetype);\\\\n}\\\\nelse if (ios && filetype.includes(\\\\\"file/*\\\\\")) {\\\\n    accept_file_types = \\\\\"*\\\\\";\\\\n}\\\\nelse {\\\\n    filetype = filetype.map(process_file_type);\\\\n    accept_file_types = filetype.join(\\\\\", \\\\\");\\\\n}\\\\nexport function paste_clipboard() {\\\\n    navigator.clipboard.read().then(async (items) => {\\\\n        for (let i = 0; i < items.length; i++) {\\\\n            const type = items[i].types.find((t) => t.startsWith(\\\\\"image/\\\\\"));\\\\n            if (type) {\\\\n                items[i].getType(type).then(async (blob) => {\\\\n                    const file = new File([blob], `clipboard.${type.replace(\\\\\"image/\\\\\", \\\\\"\\\\\")}`);\\\\n                    await load_files([file]);\\\\n                });\\\\n                break;\\\\n            }\\\\n        }\\\\n    });\\\\n}\\\\nexport function open_file_upload() {\\\\n    _open_file_upload();\\\\n}\\\\nasync function handle_upload(file_data2) {\\\\n    await tick();\\\\n    upload_id = Math.random().toString(36).substring(2, 15);\\\\n    uploading = true;\\\\n    try {\\\\n        const _file_data = await upload(file_data2, root, upload_id, max_file_size ?? Infinity);\\\\n        dispatch(\\\\\"load\\\\\", file_count === \\\\\"single\\\\\" ? _file_data?.[0] : _file_data);\\\\n        uploading = false;\\\\n        return _file_data || [];\\\\n    }\\\\n    catch (e) {\\\\n        dispatch(\\\\\"error\\\\\", e.message);\\\\n        uploading = false;\\\\n        return [];\\\\n    }\\\\n}\\\\nfunction is_valid_mimetype(file_accept, uploaded_file_extension, uploaded_file_type) {\\\\n    if (!file_accept || file_accept === \\\\\"*\\\\\" || file_accept === \\\\\"file/*\\\\\" || Array.isArray(file_accept) && file_accept.some((accept) => accept === \\\\\"*\\\\\" || accept === \\\\\"file/*\\\\\")) {\\\\n        return true;\\\\n    }\\\\n    let acceptArray;\\\\n    if (typeof file_accept === \\\\\"string\\\\\") {\\\\n        acceptArray = file_accept.split(\\\\\",\\\\\").map((s) => s.trim());\\\\n    }\\\\n    else if (Array.isArray(file_accept)) {\\\\n        acceptArray = file_accept;\\\\n    }\\\\n    else {\\\\n        return false;\\\\n    }\\\\n    return acceptArray.includes(uploaded_file_extension) || acceptArray.some((type) => {\\\\n        const [category] = type.split(\\\\\"/\\\\\").map((s) => s.trim());\\\\n        return type.endsWith(\\\\\"/*\\\\\") && uploaded_file_type.startsWith(category + \\\\\"/\\\\\");\\\\n    });\\\\n}\\\\nexport async function load_files(files) {\\\\n    if (!files.length) {\\\\n        return;\\\\n    }\\\\n    let _files = files.map((f) => new File([f], f instanceof File ? f.name : \\\\\"file\\\\\", { type: f.type }));\\\\n    if (ios && use_post_upload_validation) {\\\\n        _files = _files.filter((file) => {\\\\n            if (is_valid_file(file)) {\\\\n                return true;\\\\n            }\\\\n            dispatch(\\\\\"error\\\\\", `Invalid file type: ${file.name}. Only ${filetype} allowed.`);\\\\n            return false;\\\\n        });\\\\n        if (_files.length === 0) {\\\\n            return [];\\\\n        }\\\\n    }\\\\n    file_data = await prepare_files(_files);\\\\n    return await handle_upload(file_data);\\\\n}\\\\nfunction is_valid_file(file) {\\\\n    if (!filetype)\\\\n        return true;\\\\n    const allowed_types = Array.isArray(filetype) ? filetype : [filetype];\\\\n    return allowed_types.some((type) => {\\\\n        const processed_type = process_file_type(type);\\\\n        if (processed_type.startsWith(\\\\\".\\\\\")) {\\\\n            return file.name.toLowerCase().endsWith(processed_type.toLowerCase());\\\\n        }\\\\n        if (processed_type === \\\\\"*\\\\\") {\\\\n            return true;\\\\n        }\\\\n        if (processed_type.endsWith(\\\\\"/*\\\\\")) {\\\\n            const [category] = processed_type.split(\\\\\"/\\\\\");\\\\n            return file.type.startsWith(category + \\\\\"/\\\\\");\\\\n        }\\\\n        return file.type === processed_type;\\\\n    });\\\\n}\\\\nasync function load_files_from_upload(files) {\\\\n    const files_to_load = files.filter((file) => {\\\\n        const file_extension = \\\\\".\\\\\" + file.name.split(\\\\\".\\\\\").pop();\\\\n        if (file_extension && is_valid_mimetype(accept_file_types, file_extension, file.type)) {\\\\n            return true;\\\\n        }\\\\n        if (file_extension && Array.isArray(filetype) ? filetype.includes(file_extension) : file_extension === filetype) {\\\\n            return true;\\\\n        }\\\\n        dispatch(\\\\\"error\\\\\", `Invalid file type only ${filetype} allowed.`);\\\\n        return false;\\\\n    });\\\\n    if (format != \\\\\"blob\\\\\") {\\\\n        await load_files(files_to_load);\\\\n    }\\\\n    else {\\\\n        if (file_count === \\\\\"single\\\\\") {\\\\n            dispatch(\\\\\"load\\\\\", files_to_load[0]);\\\\n            return;\\\\n        }\\\\n        dispatch(\\\\\"load\\\\\", files_to_load);\\\\n    }\\\\n}\\\\nexport async function load_files_from_drop(e) {\\\\n    dragging = false;\\\\n    if (!e.dataTransfer?.files)\\\\n        return;\\\\n    const files_to_load = Array.from(e.dataTransfer.files).filter(is_valid_file);\\\\n    if (format != \\\\\"blob\\\\\") {\\\\n        await load_files(files_to_load);\\\\n    }\\\\n    else {\\\\n        if (file_count === \\\\\"single\\\\\") {\\\\n            dispatch(\\\\\"load\\\\\", files_to_load[0]);\\\\n            return;\\\\n        }\\\\n        dispatch(\\\\\"load\\\\\", files_to_load);\\\\n    }\\\\n}\\\\n<\\/script>\\\\n\\\\n{#if filetype === \\\\\"clipboard\\\\\"}\\\\n\\\\t<button\\\\n\\\\t\\\\tclass:hidden\\\\n\\\\t\\\\tclass:center\\\\n\\\\t\\\\tclass:boundedheight\\\\n\\\\t\\\\tclass:flex\\\\n\\\\t\\\\tclass:icon-mode={icon_upload}\\\\n\\\\t\\\\tstyle:height={icon_upload\\\\n\\\\t\\\\t\\\\t? \\\\\"\\\\\"\\\\n\\\\t\\\\t\\\\t: height\\\\n\\\\t\\\\t\\\\t\\\\t? typeof height === \\\\\"number\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t? height + \\\\\"px\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t: height\\\\n\\\\t\\\\t\\\\t\\\\t: \\\\\"100%\\\\\"}\\\\n\\\\t\\\\ttabindex={hidden ? -1 : 0}\\\\n\\\\t\\\\ton:click={paste_clipboard}\\\\n\\\\t\\\\taria-label={aria_label || \\\\\"Paste from clipboard\\\\\"}\\\\n\\\\t>\\\\n\\\\t\\\\t<slot />\\\\n\\\\t</button>\\\\n{:else if uploading && show_progress}\\\\n\\\\t{#if !hidden}\\\\n\\\\t\\\\t<UploadProgress {root} {upload_id} files={file_data} {stream_handler} />\\\\n\\\\t{/if}\\\\n{:else}\\\\n\\\\t<button\\\\n\\\\t\\\\tclass:hidden\\\\n\\\\t\\\\tclass:center\\\\n\\\\t\\\\tclass:boundedheight\\\\n\\\\t\\\\tclass:flex\\\\n\\\\t\\\\tclass:disable_click\\\\n\\\\t\\\\tclass:icon-mode={icon_upload}\\\\n\\\\t\\\\tstyle:height={icon_upload\\\\n\\\\t\\\\t\\\\t? \\\\\"\\\\\"\\\\n\\\\t\\\\t\\\\t: height\\\\n\\\\t\\\\t\\\\t\\\\t? typeof height === \\\\\"number\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t? height + \\\\\"px\\\\\"\\\\n\\\\t\\\\t\\\\t\\\\t\\\\t: height\\\\n\\\\t\\\\t\\\\t\\\\t: \\\\\"100%\\\\\"}\\\\n\\\\t\\\\ttabindex={hidden ? -1 : 0}\\\\n\\\\t\\\\tuse:drag={{\\\\n\\\\t\\\\t\\\\ton_drag_change: (dragging) => (dragging = dragging),\\\\n\\\\t\\\\t\\\\ton_files: (files) => load_files_from_upload(files),\\\\n\\\\t\\\\t\\\\taccepted_types: accept_file_types,\\\\n\\\\t\\\\t\\\\tmode: file_count,\\\\n\\\\t\\\\t\\\\tdisable_click\\\\n\\\\t\\\\t}}\\\\n\\\\t\\\\taria-label={aria_label || \\\\\"Click to upload or drop files\\\\\"}\\\\n\\\\t\\\\taria-dropeffect=\\\\\"copy\\\\\"\\\\n\\\\t>\\\\n\\\\t\\\\t<slot />\\\\n\\\\t</button>\\\\n{/if}\\\\n\\\\n<style>\\\\n\\\\tbutton {\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\twidth: var(--size-full);\\\\n\\\\t}\\\\n\\\\n\\\\t.center {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t}\\\\n\\\\t.flex {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t}\\\\n\\\\t.hidden {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\tflex-grow: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.hidden :global(svg) {\\\\n\\\\t\\\\tdisplay: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.disable_click {\\\\n\\\\t\\\\tcursor: default;\\\\n\\\\t}\\\\n\\\\n\\\\t.icon-mode {\\\\n\\\\t\\\\tposition: absolute !important;\\\\n\\\\t\\\\twidth: var(--size-4);\\\\n\\\\t\\\\theight: var(--size-4);\\\\n\\\\t\\\\tpadding: 0;\\\\n\\\\t\\\\tmin-height: 0;\\\\n\\\\t\\\\tborder-radius: var(--radius-circle);\\\\n\\\\t}\\\\n\\\\n\\\\t.icon-mode :global(svg) {\\\\n\\\\t\\\\twidth: var(--size-4);\\\\n\\\\t\\\\theight: var(--size-4);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAiQC,oBAAO,CACN,MAAM,CAAE,OAAO,CACf,KAAK,CAAE,IAAI,WAAW,CACvB,CAEA,qBAAQ,CACP,OAAO,CAAE,IAAI,CACb,eAAe,CAAE,MAClB,CACA,mBAAM,CACL,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,eAAe,CAAE,MAAM,CACvB,WAAW,CAAE,MACd,CACA,qBAAQ,CACP,OAAO,CAAE,IAAI,CACb,QAAQ,CAAE,QAAQ,CAClB,SAAS,CAAE,CACZ,CAEA,qBAAO,CAAS,GAAK,CACpB,OAAO,CAAE,IACV,CAEA,4BAAe,CACd,MAAM,CAAE,OACT,CAEA,wBAAW,CACV,QAAQ,CAAE,QAAQ,CAAC,UAAU,CAC7B,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,OAAO,CAAE,CAAC,CACV,UAAU,CAAE,CAAC,CACb,aAAa,CAAE,IAAI,eAAe,CACnC,CAEA,wBAAU,CAAS,GAAK,CACvB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CACrB\"}'\n};\nconst Upload = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let ios;\n  const { drag, open_file_upload: _open_file_upload } = create_drag();\n  let { filetype = null } = $$props;\n  let { dragging = false } = $$props;\n  let { boundedheight = true } = $$props;\n  let { center = true } = $$props;\n  let { flex = true } = $$props;\n  let { file_count = \"single\" } = $$props;\n  let { disable_click = false } = $$props;\n  let { root } = $$props;\n  let { hidden = false } = $$props;\n  let { format = \"file\" } = $$props;\n  let { uploading = false } = $$props;\n  let { show_progress = true } = $$props;\n  let { max_file_size = null } = $$props;\n  let { upload } = $$props;\n  let { stream_handler } = $$props;\n  let { icon_upload = false } = $$props;\n  let { height = void 0 } = $$props;\n  let { aria_label = void 0 } = $$props;\n  function open_upload() {\n    _open_file_upload();\n  }\n  let upload_id;\n  let file_data;\n  let use_post_upload_validation = null;\n  const get_ios = () => {\n    if (typeof navigator !== \"undefined\") {\n      const userAgent = navigator.userAgent.toLowerCase();\n      return userAgent.indexOf(\"iphone\") > -1 || userAgent.indexOf(\"ipad\") > -1;\n    }\n    return false;\n  };\n  const dispatch = createEventDispatcher();\n  const validFileTypes = [\"image\", \"video\", \"audio\", \"text\", \"file\"];\n  const process_file_type = (type) => {\n    if (ios && type.startsWith(\".\")) {\n      use_post_upload_validation = true;\n      return type;\n    }\n    if (ios && type.includes(\"file/*\")) {\n      return \"*\";\n    }\n    if (type.startsWith(\".\") || type.endsWith(\"/*\")) {\n      return type;\n    }\n    if (validFileTypes.includes(type)) {\n      return type + \"/*\";\n    }\n    return \".\" + type;\n  };\n  function paste_clipboard() {\n    navigator.clipboard.read().then(async (items) => {\n      for (let i = 0; i < items.length; i++) {\n        const type = items[i].types.find((t) => t.startsWith(\"image/\"));\n        if (type) {\n          items[i].getType(type).then(async (blob) => {\n            const file = new File([blob], `clipboard.${type.replace(\"image/\", \"\")}`);\n            await load_files([file]);\n          });\n          break;\n        }\n      }\n    });\n  }\n  function open_file_upload() {\n    _open_file_upload();\n  }\n  async function handle_upload(file_data2) {\n    await tick();\n    upload_id = Math.random().toString(36).substring(2, 15);\n    uploading = true;\n    try {\n      const _file_data = await upload(file_data2, root, upload_id, max_file_size ?? Infinity);\n      dispatch(\"load\", file_count === \"single\" ? _file_data?.[0] : _file_data);\n      uploading = false;\n      return _file_data || [];\n    } catch (e) {\n      dispatch(\"error\", e.message);\n      uploading = false;\n      return [];\n    }\n  }\n  async function load_files(files) {\n    if (!files.length) {\n      return;\n    }\n    let _files = files.map((f) => new File([f], f instanceof File ? f.name : \"file\", { type: f.type }));\n    if (ios && use_post_upload_validation) {\n      _files = _files.filter((file) => {\n        if (is_valid_file(file)) {\n          return true;\n        }\n        dispatch(\"error\", `Invalid file type: ${file.name}. Only ${filetype} allowed.`);\n        return false;\n      });\n      if (_files.length === 0) {\n        return [];\n      }\n    }\n    file_data = await prepare_files(_files);\n    return await handle_upload(file_data);\n  }\n  function is_valid_file(file) {\n    if (!filetype)\n      return true;\n    const allowed_types = Array.isArray(filetype) ? filetype : [filetype];\n    return allowed_types.some((type) => {\n      const processed_type = process_file_type(type);\n      if (processed_type.startsWith(\".\")) {\n        return file.name.toLowerCase().endsWith(processed_type.toLowerCase());\n      }\n      if (processed_type === \"*\") {\n        return true;\n      }\n      if (processed_type.endsWith(\"/*\")) {\n        const [category] = processed_type.split(\"/\");\n        return file.type.startsWith(category + \"/\");\n      }\n      return file.type === processed_type;\n    });\n  }\n  async function load_files_from_drop(e) {\n    dragging = false;\n    if (!e.dataTransfer?.files)\n      return;\n    const files_to_load = Array.from(e.dataTransfer.files).filter(is_valid_file);\n    if (format != \"blob\") {\n      await load_files(files_to_load);\n    } else {\n      if (file_count === \"single\") {\n        dispatch(\"load\", files_to_load[0]);\n        return;\n      }\n      dispatch(\"load\", files_to_load);\n    }\n  }\n  if ($$props.filetype === void 0 && $$bindings.filetype && filetype !== void 0)\n    $$bindings.filetype(filetype);\n  if ($$props.dragging === void 0 && $$bindings.dragging && dragging !== void 0)\n    $$bindings.dragging(dragging);\n  if ($$props.boundedheight === void 0 && $$bindings.boundedheight && boundedheight !== void 0)\n    $$bindings.boundedheight(boundedheight);\n  if ($$props.center === void 0 && $$bindings.center && center !== void 0)\n    $$bindings.center(center);\n  if ($$props.flex === void 0 && $$bindings.flex && flex !== void 0)\n    $$bindings.flex(flex);\n  if ($$props.file_count === void 0 && $$bindings.file_count && file_count !== void 0)\n    $$bindings.file_count(file_count);\n  if ($$props.disable_click === void 0 && $$bindings.disable_click && disable_click !== void 0)\n    $$bindings.disable_click(disable_click);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  if ($$props.hidden === void 0 && $$bindings.hidden && hidden !== void 0)\n    $$bindings.hidden(hidden);\n  if ($$props.format === void 0 && $$bindings.format && format !== void 0)\n    $$bindings.format(format);\n  if ($$props.uploading === void 0 && $$bindings.uploading && uploading !== void 0)\n    $$bindings.uploading(uploading);\n  if ($$props.show_progress === void 0 && $$bindings.show_progress && show_progress !== void 0)\n    $$bindings.show_progress(show_progress);\n  if ($$props.max_file_size === void 0 && $$bindings.max_file_size && max_file_size !== void 0)\n    $$bindings.max_file_size(max_file_size);\n  if ($$props.upload === void 0 && $$bindings.upload && upload !== void 0)\n    $$bindings.upload(upload);\n  if ($$props.stream_handler === void 0 && $$bindings.stream_handler && stream_handler !== void 0)\n    $$bindings.stream_handler(stream_handler);\n  if ($$props.icon_upload === void 0 && $$bindings.icon_upload && icon_upload !== void 0)\n    $$bindings.icon_upload(icon_upload);\n  if ($$props.height === void 0 && $$bindings.height && height !== void 0)\n    $$bindings.height(height);\n  if ($$props.aria_label === void 0 && $$bindings.aria_label && aria_label !== void 0)\n    $$bindings.aria_label(aria_label);\n  if ($$props.open_upload === void 0 && $$bindings.open_upload && open_upload !== void 0)\n    $$bindings.open_upload(open_upload);\n  if ($$props.paste_clipboard === void 0 && $$bindings.paste_clipboard && paste_clipboard !== void 0)\n    $$bindings.paste_clipboard(paste_clipboard);\n  if ($$props.open_file_upload === void 0 && $$bindings.open_file_upload && open_file_upload !== void 0)\n    $$bindings.open_file_upload(open_file_upload);\n  if ($$props.load_files === void 0 && $$bindings.load_files && load_files !== void 0)\n    $$bindings.load_files(load_files);\n  if ($$props.load_files_from_drop === void 0 && $$bindings.load_files_from_drop && load_files_from_drop !== void 0)\n    $$bindings.load_files_from_drop(load_files_from_drop);\n  $$result.css.add(css);\n  ios = get_ios();\n  {\n    if (filetype == null)\n      ;\n    else if (typeof filetype === \"string\") {\n      process_file_type(filetype);\n    } else if (ios && filetype.includes(\"file/*\"))\n      ;\n    else {\n      filetype = filetype.map(process_file_type);\n      filetype.join(\", \");\n    }\n  }\n  return `${filetype === \"clipboard\" ? `<button${add_attribute(\"tabindex\", hidden ? -1 : 0, 0)}${add_attribute(\"aria-label\", aria_label || \"Paste from clipboard\", 0)} class=\"${[\n    \"svelte-edrmkl\",\n    (hidden ? \"hidden\" : \"\") + \" \" + (center ? \"center\" : \"\") + \" \" + (boundedheight ? \"boundedheight\" : \"\") + \" \" + (flex ? \"flex\" : \"\") + \" \" + (icon_upload ? \"icon-mode\" : \"\")\n  ].join(\" \").trim()}\"${add_styles({\n    \"height\": icon_upload ? \"\" : height ? typeof height === \"number\" ? height + \"px\" : height : \"100%\"\n  })}>${slots.default ? slots.default({}) : ``}</button>` : `${uploading && show_progress ? `${!hidden ? `${validate_component(UploadProgress, \"UploadProgress\").$$render(\n    $$result,\n    {\n      root,\n      upload_id,\n      files: file_data,\n      stream_handler\n    },\n    {},\n    {}\n  )}` : ``}` : `<button${add_attribute(\"tabindex\", hidden ? -1 : 0, 0)}${add_attribute(\"aria-label\", aria_label || \"Click to upload or drop files\", 0)} aria-dropeffect=\"copy\" class=\"${[\n    \"svelte-edrmkl\",\n    (hidden ? \"hidden\" : \"\") + \" \" + (center ? \"center\" : \"\") + \" \" + (boundedheight ? \"boundedheight\" : \"\") + \" \" + (flex ? \"flex\" : \"\") + \" \" + (disable_click ? \"disable_click\" : \"\") + \" \" + (icon_upload ? \"icon-mode\" : \"\")\n  ].join(\" \").trim()}\"${add_styles({\n    \"height\": icon_upload ? \"\" : height ? typeof height === \"number\" ? height + \"px\" : height : \"100%\"\n  })}>${slots.default ? slots.default({}) : ``}</button>`}`}`;\n});\nconst ModifyUpload = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { editable = false } = $$props;\n  let { undoable = false } = $$props;\n  let { download = null } = $$props;\n  let { i18n } = $$props;\n  createEventDispatcher();\n  if ($$props.editable === void 0 && $$bindings.editable && editable !== void 0)\n    $$bindings.editable(editable);\n  if ($$props.undoable === void 0 && $$bindings.undoable && undoable !== void 0)\n    $$bindings.undoable(undoable);\n  if ($$props.download === void 0 && $$bindings.download && download !== void 0)\n    $$bindings.download(download);\n  if ($$props.i18n === void 0 && $$bindings.i18n && i18n !== void 0)\n    $$bindings.i18n(i18n);\n  return `${validate_component(IconButtonWrapper, \"IconButtonWrapper\").$$render($$result, {}, {}, {\n    default: () => {\n      return `${editable ? `${validate_component(IconButton, \"IconButton\").$$render($$result, { Icon: Edit, label: i18n(\"common.edit\") }, {}, {})}` : ``} ${undoable ? `${validate_component(IconButton, \"IconButton\").$$render($$result, { Icon: Undo, label: i18n(\"common.undo\") }, {}, {})}` : ``} ${download ? `${validate_component(DownloadLink, \"DownloadLink\").$$render($$result, { href: download, download: true }, {}, {\n        default: () => {\n          return `${validate_component(IconButton, \"IconButton\").$$render(\n            $$result,\n            {\n              Icon: Download,\n              label: i18n(\"common.download\")\n            },\n            {},\n            {}\n          )}`;\n        }\n      })}` : ``} ${slots.default ? slots.default({}) : ``} ${validate_component(IconButton, \"IconButton\").$$render($$result, { Icon: Clear, label: i18n(\"common.clear\") }, {}, {})}`;\n    }\n  })}`;\n});\nexport {\n  ModifyUpload as M,\n  Upload as U\n};\n"], "names": [], "mappings": ";;;;AAIA,MAAM,KAAK,GAAG;AACd,EAAE,IAAI,EAAE,yhDAAyhD;AACjiD,EAAE,GAAG,EAAE,gyMAAgyM;AACvyM,CAAC,CAAC;AACF,SAAS,WAAW,CAAC,IAAI,EAAE;AAC3B,EAAE,OAAO,IAAI,CAAC,QAAQ,GAAG,GAAG,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;AACrD,CAAC;AACD,SAAS,sBAAsB,CAAC,MAAM,EAAE;AACxC,EAAE,IAAI,aAAa,GAAG,CAAC,CAAC;AACxB,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC3B,IAAI,aAAa,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC;AACvC,GAAG,CAAC,CAAC;AACL,EAAE,QAAQ,CAAC,eAAe,CAAC,KAAK,CAAC,WAAW,CAAC,yBAAyB,EAAE,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AAC1H,EAAE,OAAO,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC;AACvC,CAAC;AACD,MAAM,cAAc,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACtF,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAC9B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AAInC,EAAE,IAAI,eAAe,CAAC;AACtB,EAAE,IAAI,mBAAmB,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK;AAChD,IAAI,OAAO,EAAE,GAAG,IAAI,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC;AACpC,GAAG,CAAC,CAAC;AACL,EAAmB,qBAAqB,GAAG;AA2B3C,EAAE,SAAS,CAAC,MAAM;AAGlB,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1B,EAAE;AACF,IAAI,sBAAsB,CAAC,mBAAmB,CAAC,CAAC;AAChD,GAAG;AACH,EAAE,eAAe,GAA0B,mBAAmB,CAAC,CAAC,CAAC,CAAC;AAClE,EAAE,OAAO,CAAC,YAAY,EAAE,CAAC,qBAAqB,EAA0B,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,mDAAmD,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,GAAG,OAAO,GAAG,MAAM,CAAC,CAAC,WAAW,EAAE,eAAe,GAAG,CAAC,qIAAqI,EAAE,aAAa,CAAC,OAAO,EAAE,WAAW,CAAC,eAAe,CAAC,EAAE,CAAC,CAAC,CAAC,kCAAkC,EAAE,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,CAAC,gEAAgE,EAAE,MAAM,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AAC3pB,CAAC,CAAC,CAAC;AACH,SAAS,WAAW,GAAG;AACvB,EAAE,IAAI,YAAY,CAAC;AACnB,EAAE,IAAI,QAAQ,CAAC;AACf,EAAE,OAAO;AACT,IAAI,IAAI,CAAC,IAAI,EAAE,OAAO,GAAG,EAAE,EAAE;AAC7B,MAAM,QAAQ,GAAG,OAAO,CAAC;AACzB,MAAM,SAAS,kBAAkB,GAAG;AACpC,QAAQ,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AACvD,QAAQ,YAAY,CAAC,IAAI,GAAG,MAAM,CAAC;AACnC,QAAQ,YAAY,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AAC5C,QAAQ,YAAY,CAAC,YAAY,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;AAC/D,QAAQ,YAAY,CAAC,YAAY,CAAC,aAAa,EAAE,aAAa,CAAC,CAAC;AAChE,QAAQ,MAAM,cAAc,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,cAAc,IAAI,KAAK,CAAC,CAAC;AAC9I,QAAQ,IAAI,cAAc,EAAE;AAC5B,UAAU,YAAY,CAAC,MAAM,GAAG,cAAc,CAAC;AAC/C,SAAS;AACT,QAAQ,YAAY,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI,KAAK,UAAU,IAAI,KAAK,CAAC;AACtE,QAAQ,IAAI,QAAQ,CAAC,IAAI,KAAK,WAAW,EAAE;AAC3C,UAAU,YAAY,CAAC,eAAe,GAAG,IAAI,CAAC;AAC9C,UAAU,YAAY,CAAC,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AACrD,UAAU,YAAY,CAAC,YAAY,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;AACxD,SAAS;AACT,QAAQ,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC;AACvC,OAAO;AACP,MAAM,kBAAkB,EAAE,CAAC;AAC3B,MAAM,SAAS,WAAW,CAAC,CAAC,EAAE;AAC9B,QAAQ,CAAC,CAAC,cAAc,EAAE,CAAC;AAC3B,QAAQ,CAAC,CAAC,eAAe,EAAE,CAAC;AAC5B,OAAO;AACP,MAAM,SAAS,iBAAiB,CAAC,CAAC,EAAE;AACpC,QAAQ,CAAC,CAAC,cAAc,EAAE,CAAC;AAC3B,QAAQ,CAAC,CAAC,eAAe,EAAE,CAAC;AAC5B,QAAQ,QAAQ,CAAC,cAAc,GAAG,IAAI,CAAC,CAAC;AACxC,OAAO;AACP,MAAM,SAAS,iBAAiB,CAAC,CAAC,EAAE;AACpC,QAAQ,CAAC,CAAC,cAAc,EAAE,CAAC;AAC3B,QAAQ,CAAC,CAAC,eAAe,EAAE,CAAC;AAC5B,QAAQ,QAAQ,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC;AACzC,OAAO;AACP,MAAM,SAAS,WAAW,CAAC,CAAC,EAAE;AAC9B,QAAQ,CAAC,CAAC,cAAc,EAAE,CAAC;AAC3B,QAAQ,CAAC,CAAC,eAAe,EAAE,CAAC;AAC5B,QAAQ,QAAQ,CAAC,cAAc,GAAG,KAAK,CAAC,CAAC;AACzC,QAAQ,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE,KAAK;AAClC,UAAU,OAAO;AACjB,QAAQ,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AACvD,QAAQ,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAC9B,UAAU,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC;AACrC,SAAS;AACT,OAAO;AACP,MAAM,SAAS,YAAY,GAAG;AAC9B,QAAQ,IAAI,CAAC,QAAQ,CAAC,aAAa,EAAE;AACrC,UAAU,YAAY,CAAC,KAAK,GAAG,EAAE,CAAC;AAClC,UAAU,YAAY,CAAC,KAAK,EAAE,CAAC;AAC/B,SAAS;AACT,OAAO;AACP,MAAM,SAAS,wBAAwB,GAAG;AAC1C,QAAQ,IAAI,YAAY,CAAC,KAAK,EAAE;AAChC,UAAU,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;AACvD,UAAU,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AAChC,YAAY,QAAQ,CAAC,QAAQ,GAAG,KAAK,CAAC,CAAC;AACvC,WAAW;AACX,SAAS;AACT,OAAO;AACP,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AACjD,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AACtD,MAAM,IAAI,CAAC,gBAAgB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;AACpD,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;AACrD,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAC5D,MAAM,IAAI,CAAC,gBAAgB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAC5D,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AACjD,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;AACnD,MAAM,YAAY,CAAC,gBAAgB,CAAC,QAAQ,EAAE,wBAAwB,CAAC,CAAC;AACxE,MAAM,OAAO;AACb,QAAQ,MAAM,CAAC,WAAW,EAAE;AAC5B,UAAU,QAAQ,GAAG,WAAW,CAAC;AACjC,UAAU,YAAY,CAAC,MAAM,EAAE,CAAC;AAChC,UAAU,kBAAkB,EAAE,CAAC;AAC/B,UAAU,YAAY,CAAC,gBAAgB,CAAC,QAAQ,EAAE,wBAAwB,CAAC,CAAC;AAC5E,SAAS;AACT,QAAQ,OAAO,GAAG;AAClB,UAAU,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AACxD,UAAU,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AAC7D,UAAU,IAAI,CAAC,mBAAmB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;AAC3D,UAAU,IAAI,CAAC,mBAAmB,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;AAC5D,UAAU,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AACnE,UAAU,IAAI,CAAC,mBAAmB,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;AACnE,UAAU,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AACxD,UAAU,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;AAC1D,UAAU,YAAY,CAAC,mBAAmB,CAAC,QAAQ,EAAE,wBAAwB,CAAC,CAAC;AAC/E,UAAU,YAAY,CAAC,MAAM,EAAE,CAAC;AAChC,SAAS;AACT,OAAO,CAAC;AACR,KAAK;AACL,IAAI,gBAAgB,GAAG;AACvB,MAAM,IAAI,YAAY,EAAE;AACxB,QAAQ,YAAY,CAAC,KAAK,GAAG,EAAE,CAAC;AAChC,QAAQ,YAAY,CAAC,KAAK,EAAE,CAAC;AAC7B,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ,CAAC;AACD,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,2kBAA2kB;AACnlB,EAAE,GAAG,EAAE,ynUAAynU;AAChoU,CAAC,CAAC;AACG,MAAC,MAAM,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC9E,EAAE,IAAI,GAAG,CAAC;AACV,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,GAAG,WAAW,EAAE,CAAC;AACtE,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,UAAU,GAAG,QAAQ,EAAE,GAAG,OAAO,CAAC;AAC1C,EAAE,IAAI,EAAE,aAAa,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1C,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,aAAa,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACzC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,WAAW,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACxC,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,CAAC,EAAE,GAAG,OAAO,CAAC;AACxC,EAAE,SAAS,WAAW,GAAG;AACzB,IAAI,iBAAiB,EAAE,CAAC;AACxB,GAAG;AACH,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,0BAA0B,GAAG,IAAI,CAAC;AACxC,EAAE,MAAM,OAAO,GAAG,MAAM;AACxB,IAAI,IAAI,OAAO,SAAS,KAAK,WAAW,EAAE;AAC1C,MAAM,MAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;AAC1D,MAAM,OAAO,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAChF,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG,CAAC;AACJ,EAAE,MAAM,QAAQ,GAAG,qBAAqB,EAAE,CAAC;AAC3C,EAAE,MAAM,cAAc,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AACrE,EAAE,MAAM,iBAAiB,GAAG,CAAC,IAAI,KAAK;AACtC,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AACrC,MAAM,0BAA0B,GAAG,IAAI,CAAC;AACxC,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;AACxC,MAAM,OAAO,GAAG,CAAC;AACjB,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACrD,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,IAAI,IAAI,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACvC,MAAM,OAAO,IAAI,GAAG,IAAI,CAAC;AACzB,KAAK;AACL,IAAI,OAAO,GAAG,GAAG,IAAI,CAAC;AACtB,GAAG,CAAC;AACJ,EAAE,SAAS,eAAe,GAAG;AAC7B,IAAI,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,OAAO,KAAK,KAAK;AACrD,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC7C,QAAQ,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC;AACxE,QAAQ,IAAI,IAAI,EAAE;AAClB,UAAU,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,KAAK;AACtD,YAAY,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACrF,YAAY,MAAM,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AACrC,WAAW,CAAC,CAAC;AACb,UAAU,MAAM;AAChB,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,SAAS,gBAAgB,GAAG;AAC9B,IAAI,iBAAiB,EAAE,CAAC;AACxB,GAAG;AACH,EAAE,eAAe,aAAa,CAAC,UAAU,EAAE;AAC3C,IAAI,MAAM,IAAI,EAAE,CAAC;AACjB,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC5D,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI;AACR,MAAM,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,aAAa,IAAI,QAAQ,CAAC,CAAC;AAC9F,MAAM,QAAQ,CAAC,MAAM,EAAE,UAAU,KAAK,QAAQ,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC;AAC/E,MAAM,SAAS,GAAG,KAAK,CAAC;AACxB,MAAM,OAAO,UAAU,IAAI,EAAE,CAAC;AAC9B,KAAK,CAAC,OAAO,CAAC,EAAE;AAChB,MAAM,QAAQ,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;AACnC,MAAM,SAAS,GAAG,KAAK,CAAC;AACxB,MAAM,OAAO,EAAE,CAAC;AAChB,KAAK;AACL,GAAG;AACH,EAAE,eAAe,UAAU,CAAC,KAAK,EAAE;AACnC,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AACvB,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,YAAY,IAAI,GAAG,CAAC,CAAC,IAAI,GAAG,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACxG,IAAI,IAAI,GAAG,IAAI,0BAA0B,EAAE;AAC3C,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK;AACvC,QAAQ,IAAI,aAAa,CAAC,IAAI,CAAC,EAAE;AACjC,UAAU,OAAO,IAAI,CAAC;AACtB,SAAS;AACT,QAAQ,QAAQ,CAAC,OAAO,EAAE,CAAC,mBAAmB,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;AACxF,QAAQ,OAAO,KAAK,CAAC;AACrB,OAAO,CAAC,CAAC;AACT,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;AAC/B,QAAQ,OAAO,EAAE,CAAC;AAClB,OAAO;AACP,KAAK;AACL,IAAI,SAAS,GAAG,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC;AAC5C,IAAI,OAAO,MAAM,aAAa,CAAC,SAAS,CAAC,CAAC;AAC1C,GAAG;AACH,EAAE,SAAS,aAAa,CAAC,IAAI,EAAE;AAC/B,IAAI,IAAI,CAAC,QAAQ;AACjB,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,MAAM,aAAa,GAAG,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC;AAC1E,IAAI,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK;AACxC,MAAM,MAAM,cAAc,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACrD,MAAM,IAAI,cAAc,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE;AAC1C,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC;AAC9E,OAAO;AACP,MAAM,IAAI,cAAc,KAAK,GAAG,EAAE;AAClC,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO;AACP,MAAM,IAAI,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACzC,QAAQ,MAAM,CAAC,QAAQ,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACrD,QAAQ,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,GAAG,CAAC,CAAC;AACpD,OAAO;AACP,MAAM,OAAO,IAAI,CAAC,IAAI,KAAK,cAAc,CAAC;AAC1C,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,eAAe,oBAAoB,CAAC,CAAC,EAAE;AACzC,IAAI,QAAQ,GAAG,KAAK,CAAC;AACrB,IAAI,IAAI,CAAC,CAAC,CAAC,YAAY,EAAE,KAAK;AAC9B,MAAM,OAAO;AACb,IAAI,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AACjF,IAAI,IAAI,MAAM,IAAI,MAAM,EAAE;AAC1B,MAAM,MAAM,UAAU,CAAC,aAAa,CAAC,CAAC;AACtC,KAAK,MAAM;AACX,MAAM,IAAI,UAAU,KAAK,QAAQ,EAAE;AACnC,QAAQ,QAAQ,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AAC3C,QAAQ,OAAO;AACf,OAAO;AACP,MAAM,QAAQ,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;AACtC,KAAK;AACL,GAAG;AACH,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,eAAe,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,eAAe,IAAI,eAAe,KAAK,KAAK,CAAC;AACpG,IAAI,UAAU,CAAC,eAAe,CAAC,eAAe,CAAC,CAAC;AAChD,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,oBAAoB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,oBAAoB,IAAI,oBAAoB,KAAK,KAAK,CAAC;AACnH,IAAI,UAAU,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,CAAC;AAC1D,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,GAAG,GAAG,OAAO,EAAE,CAAC;AAClB,EAAE;AACF,IAAI,IAAI,QAAQ,IAAI,IAAI;AACxB,MAAM,CAAC;AACP,SAAS,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;AAC3C,MAAM,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AAClC,KAAK,MAAM,IAAI,GAAG,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;AACjD,MAAM,CAAC;AACP,SAAS;AACT,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;AACjD,MAAM,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,KAAK;AACL,GAAG;AACH,EAAE,OAAO,CAAC,EAAE,QAAQ,KAAK,WAAW,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC,UAAU,EAAE,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,YAAY,EAAE,UAAU,IAAI,sBAAsB,EAAE,CAAC,CAAC,CAAC,QAAQ,EAAE;AAChL,IAAI,eAAe;AACnB,IAAI,CAAC,MAAM,GAAG,QAAQ,GAAG,EAAE,IAAI,GAAG,IAAI,MAAM,GAAG,QAAQ,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,aAAa,GAAG,eAAe,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,WAAW,GAAG,WAAW,GAAG,EAAE,CAAC;AAClL,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC;AACnC,IAAI,QAAQ,EAAE,WAAW,GAAG,EAAE,GAAG,MAAM,GAAG,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,MAAM;AACtG,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,SAAS,IAAI,aAAa,GAAG,CAAC,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE,kBAAkB,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC,QAAQ;AACzK,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,IAAI;AACV,MAAM,SAAS;AACf,MAAM,KAAK,EAAE,SAAS;AACtB,MAAM,cAAc;AACpB,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC,UAAU,EAAE,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,YAAY,EAAE,UAAU,IAAI,+BAA+B,EAAE,CAAC,CAAC,CAAC,+BAA+B,EAAE;AACxL,IAAI,eAAe;AACnB,IAAI,CAAC,MAAM,GAAG,QAAQ,GAAG,EAAE,IAAI,GAAG,IAAI,MAAM,GAAG,QAAQ,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,aAAa,GAAG,eAAe,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,IAAI,GAAG,MAAM,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,aAAa,GAAG,eAAe,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,WAAW,GAAG,WAAW,GAAG,EAAE,CAAC;AACjO,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,UAAU,CAAC;AACnC,IAAI,QAAQ,EAAE,WAAW,GAAG,EAAE,GAAG,MAAM,GAAG,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,IAAI,GAAG,MAAM,GAAG,MAAM;AACtG,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9D,CAAC,EAAE;AACE,MAAC,YAAY,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AACpF,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,qBAAqB,EAAE,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,OAAO,CAAC,EAAE,kBAAkB,CAAC,iBAAiB,EAAE,mBAAmB,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE;AAClG,IAAI,OAAO,EAAE,MAAM;AACnB,MAAM,OAAO,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,GAAG,CAAC,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;AACla,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ;AACzE,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,IAAI,EAAE,QAAQ;AAC5B,cAAc,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC;AAC5C,aAAa;AACb,YAAY,EAAE;AACd,YAAY,EAAE;AACd,WAAW,CAAC,CAAC,CAAC;AACd,SAAS;AACT,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACrL,KAAK;AACL,GAAG,CAAC,CAAC,CAAC,CAAC;AACP,CAAC;;;;"}