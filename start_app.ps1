# PowerShell启动脚本
Write-Host "🚀 启动DTrans应用..." -ForegroundColor Green
Write-Host ""

# 激活虚拟环境
& ".\dtrans_env\Scripts\Activate.ps1"

# 检查.env文件是否存在
if (-not (Test-Path ".env")) {
    Write-Host "⚠️  警告: 未找到.env文件" -ForegroundColor Yellow
    Write-Host "请复制.env.example为.env并设置您的OpenRouter API密钥" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "按任意键继续..."
    exit 1
}

# 启动应用
Write-Host "🌐 启动Gradio应用..." -ForegroundColor Green
python app.py

Read-Host "按任意键退出..."
