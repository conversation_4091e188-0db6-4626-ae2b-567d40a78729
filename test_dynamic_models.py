"""
测试动态模型获取功能
验证是否能获取最新、最完整的模型列表
"""

import os
from utils.openrouter_client import OpenRouterClient
from dotenv import load_dotenv

def test_dynamic_model_loading():
    """测试动态模型加载功能"""
    print("🔄 测试动态模型获取功能")
    print("=" * 60)
    
    load_dotenv()
    api_key = os.getenv("OPENROUTER_API_KEY")
    
    if not api_key:
        print("⚠️ 未找到API密钥，将测试备用模型功能")
        client = OpenRouterClient()
        models = client.get_available_models()
        print(f"📋 备用模型数量: {len(models)}")
        return models
    
    print("✅ 找到API密钥，开始测试...")
    
    # 创建客户端
    client = OpenRouterClient(api_key)
    
    # 第一次获取模型（会从API获取）
    print("\n🔄 第一次获取模型列表...")
    models_first = client.get_available_models()
    print(f"📊 获取到 {len(models_first)} 个模型")
    
    # 第二次获取模型（应该使用缓存）
    print("\n📋 第二次获取模型列表（测试缓存）...")
    models_second = client.get_available_models()
    print(f"📊 获取到 {len(models_second)} 个模型")
    
    # 强制刷新模型列表
    print("\n🔄 强制刷新模型列表...")
    models_refresh = client.refresh_models_cache()
    print(f"📊 刷新后获取到 {len(models_refresh)} 个模型")
    
    return models_refresh

def analyze_model_distribution(models):
    """分析模型分布情况"""
    print("\n📊 模型分布分析")
    print("=" * 60)
    
    # 按提供商统计
    providers = {}
    for model in models:
        model_id = model['id']
        if '/' in model_id:
            provider = model_id.split('/')[0]
            providers[provider] = providers.get(provider, 0) + 1
    
    print("🏢 按提供商分布:")
    for provider, count in sorted(providers.items(), key=lambda x: x[1], reverse=True):
        print(f"  {provider}: {count} 个模型")
    
    # 按标签统计
    tag_counts = {}
    for model in models:
        for tag in model.get('tags', []):
            tag_counts[tag] = tag_counts.get(tag, 0) + 1
    
    print("\n🏷️ 按标签分布:")
    for tag, count in sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
        print(f"  {tag}: {count} 个模型")
    
    # 显示最新模型
    print("\n🆕 最新高质量模型 (前10个):")
    for i, model in enumerate(models[:10], 1):
        tags_str = ", ".join(model.get('tags', [])[:3])
        print(f"  {i}. {model['name']} [{tags_str}]")

def test_model_search_functionality(models):
    """测试模型搜索功能"""
    print("\n🔍 测试搜索功能")
    print("=" * 60)
    
    from app import DTransApp
    app = DTransApp()
    
    # 测试不同搜索查询
    test_queries = [
        "claude",
        "gpt-4",
        "快速",
        "长上下文",
        "开源",
        "anthropic",
        "高质量 推理"
    ]
    
    for query in test_queries:
        filtered = app.filter_models(query, models)
        print(f"搜索 '{query}': {len(filtered)} 个结果")
        if filtered:
            print(f"  首选: {filtered[0][0]}")

def test_model_details(models):
    """测试模型详细信息"""
    print("\n📝 测试模型详细信息")
    print("=" * 60)
    
    if not models:
        print("❌ 没有可用模型")
        return
    
    # 测试前3个模型的详细信息
    for i, model in enumerate(models[:3], 1):
        print(f"\n模型 {i}: {model['name']}")
        print(f"  ID: {model['id']}")
        print(f"  描述: {model.get('description', '无描述')[:100]}...")
        print(f"  上下文长度: {model.get('context_length', '未知')}")
        print(f"  标签: {', '.join(model.get('tags', []))}")

def main():
    """主测试函数"""
    print("🌐 DTrans动态模型获取功能测试")
    print("=" * 70)
    
    try:
        # 测试动态模型加载
        models = test_dynamic_model_loading()
        
        if not models:
            print("❌ 未能获取任何模型")
            return
        
        # 分析模型分布
        analyze_model_distribution(models)
        
        # 测试搜索功能
        test_model_search_functionality(models)
        
        # 测试模型详细信息
        test_model_details(models)
        
        print("\n🎉 所有测试完成！")
        print(f"✅ 成功获取 {len(models)} 个可用模型")
        print("\n💡 功能特点:")
        print("- ✅ 动态获取最新模型列表")
        print("- ✅ 智能缓存机制（5分钟缓存）")
        print("- ✅ 强制刷新功能")
        print("- ✅ 完整的模型信息")
        print("- ✅ 智能标签分配")
        print("- ✅ 相关性排序")
        print("- ✅ 备用模型支持")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
