{"version": 3, "file": "_layout.svelte-DNIDTnGO.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/entries/pages/_layout.svelte.js"], "sourcesContent": ["import { create_ssr_component } from \"svelte/internal\";\nimport * as svelte from \"svelte\";\nconst is_browser = typeof window !== \"undefined\";\nif (is_browser) {\n  const o = {\n    SvelteComponent: svelte.SvelteComponent\n  };\n  for (const key in svelte) {\n    if (key === \"SvelteComponent\")\n      continue;\n    if (key === \"SvelteComponentDev\") {\n      o[key] = o[\"SvelteComponent\"];\n    } else {\n      o[key] = svelte[key];\n    }\n  }\n  window.__gradio__svelte__internal = o;\n  window.__gradio__svelte__internal[\"globals\"] = {};\n  window.globals = window;\n}\nconst css = {\n  code: \"body{background:var(--body-background-fill);color:var(--body-text-color)}\",\n  map: '{\"version\":3,\"file\":\"+layout.svelte\",\"sources\":[\"+layout.svelte\"],\"sourcesContent\":[\"<script>\\\\n\\\\timport \\\\\"@gradio/theme/reset.css\\\\\";\\\\n\\\\timport \\\\\"@gradio/theme/global.css\\\\\";\\\\n\\\\timport \\\\\"@gradio/theme/pollen.css\\\\\";\\\\n\\\\timport \\\\\"@gradio/theme/typography.css\\\\\";\\\\n\\\\timport \\\\\"./svelte_init\\\\\";\\\\n<\\/script>\\\\n\\\\n<slot></slot>\\\\n\\\\n<style>\\\\n\\\\t:global(body) {\\\\n\\\\t\\\\tbackground: var(--body-background-fill);\\\\n\\\\t\\\\tcolor: var(--body-text-color);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AAWS,IAAM,CACb,UAAU,CAAE,IAAI,sBAAsB,CAAC,CACvC,KAAK,CAAE,IAAI,iBAAiB,CAC7B\"}'\n};\nconst Layout = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  $$result.css.add(css);\n  return `${slots.default ? slots.default({}) : ``}`;\n});\nexport {\n  Layout as default\n};\n"], "names": ["svelte.SvelteComponent"], "mappings": ";;;;AAEA,MAAM,UAAU,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC;AACjD,IAAI,UAAU,EAAE;AAChB,EAAE,MAAM,CAAC,GAAG;AACZ,IAAI,eAAe,EAAEA,kBAAsB;AAC3C,GAAG,CAAC;AACJ,EAAE,KAAK,MAAM,GAAG,IAAI,MAAM,EAAE;AAC5B,IAAI,IAAI,GAAG,KAAK,iBAAiB;AACjC,MAAM,SAAS;AACf,IAAI,IAAI,GAAG,KAAK,oBAAoB,EAAE;AACtC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,iBAAiB,CAAC,CAAC;AACpC,KAAK,MAAM;AACX,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;AAC3B,KAAK;AACL,GAAG;AACH,EAAE,MAAM,CAAC,0BAA0B,GAAG,CAAC,CAAC;AACxC,EAAE,MAAM,CAAC,0BAA0B,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;AACpD,EAAE,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC;AAC1B,CAAC;AACD,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,2EAA2E;AACnF,EAAE,GAAG,EAAE,skBAAskB;AAC7kB,CAAC,CAAC;AACG,MAAC,MAAM,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC9E,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,OAAO,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,CAAC;;;;"}