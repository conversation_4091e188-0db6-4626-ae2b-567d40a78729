{"version": 3, "file": "SharedSystems-Bj7P6-iC.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/SharedSystems.js"], "sourcesContent": ["import { F as Filter, v as TextureMatrix, l as UniformGroup, M as Matrix, G as GpuProgram, x as GlProgram, E as ExtensionType, e as extensions, y as DefaultBatcher, S as State, z as BigPool, H as getGlobalBounds, I as TexturePool, R as RendererType, J as Bounds, K as FilterEffect, L as Sprite, u as Texture, b as STENCIL_MODES, i as CLEAR, w as warn, N as getAttributeInfoFromFormat, O as unsafeEvalSupported, c as <PERSON><PERSON><PERSON>, B as BufferUsage, j as CanvasSource, Q as uid$1, k as TextureSource, V as Rectangle, W as SystemRunner, a as EventEmitter, C as Container, X as multiplyColors, Y as UPDATE_COLOR, Z as UPDATE_BLEND, _ as UPDATE_VISIBLE, $ as Color, a0 as getLocalBounds, a1 as RenderTexture, P as Point, d as BindGroup, T as Ticker, D as DOM<PERSON><PERSON>pter, a2 as VERSION, a3 as deprecation, a4 as v8_0_0, a5 as RendererInitHook } from \"./Index3.js\";\nimport { B as BatchableSprite, c as color32BitToUniform } from \"./colorToUniform.js\";\nvar fragment = \"in vec2 vMaskCoord;\\nin vec2 vTextureCoord;\\n\\nuniform sampler2D uTexture;\\nuniform sampler2D uMaskTexture;\\n\\nuniform float uAlpha;\\nuniform vec4 uMaskClamp;\\nuniform float uInverse;\\n\\nout vec4 finalColor;\\n\\nvoid main(void)\\n{\\n    float clip = step(3.5,\\n        step(uMaskClamp.x, vMaskCoord.x) +\\n        step(uMaskClamp.y, vMaskCoord.y) +\\n        step(vMaskCoord.x, uMaskClamp.z) +\\n        step(vMaskCoord.y, uMaskClamp.w));\\n\\n    // TODO look into why this is needed\\n    float npmAlpha = uAlpha;\\n    vec4 original = texture(uTexture, vTextureCoord);\\n    vec4 masky = texture(uMaskTexture, vMaskCoord);\\n    float alphaMul = 1.0 - npmAlpha * (1.0 - masky.a);\\n\\n    float a = alphaMul * masky.r * npmAlpha * clip;\\n\\n    if (uInverse == 1.0) {\\n        a = 1.0 - a;\\n    }\\n\\n    finalColor = original * a;\\n}\\n\";\nvar vertex = \"in vec2 aPosition;\\n\\nout vec2 vTextureCoord;\\nout vec2 vMaskCoord;\\n\\n\\nuniform vec4 uInputSize;\\nuniform vec4 uOutputFrame;\\nuniform vec4 uOutputTexture;\\nuniform mat3 uFilterMatrix;\\n\\nvec4 filterVertexPosition(  vec2 aPosition )\\n{\\n    vec2 position = aPosition * uOutputFrame.zw + uOutputFrame.xy;\\n       \\n    position.x = position.x * (2.0 / uOutputTexture.x) - 1.0;\\n    position.y = position.y * (2.0*uOutputTexture.z / uOutputTexture.y) - uOutputTexture.z;\\n\\n    return vec4(position, 0.0, 1.0);\\n}\\n\\nvec2 filterTextureCoord(  vec2 aPosition )\\n{\\n    return aPosition * (uOutputFrame.zw * uInputSize.zw);\\n}\\n\\nvec2 getFilterCoord( vec2 aPosition )\\n{\\n    return  ( uFilterMatrix * vec3( filterTextureCoord(aPosition), 1.0)  ).xy;\\n}   \\n\\nvoid main(void)\\n{\\n    gl_Position = filterVertexPosition(aPosition);\\n    vTextureCoord = filterTextureCoord(aPosition);\\n    vMaskCoord = getFilterCoord(aPosition);\\n}\\n\";\nvar source = \"struct GlobalFilterUniforms {\\n  uInputSize:vec4<f32>,\\n  uInputPixel:vec4<f32>,\\n  uInputClamp:vec4<f32>,\\n  uOutputFrame:vec4<f32>,\\n  uGlobalFrame:vec4<f32>,\\n  uOutputTexture:vec4<f32>,\\n};\\n\\nstruct MaskUniforms {\\n  uFilterMatrix:mat3x3<f32>,\\n  uMaskClamp:vec4<f32>,\\n  uAlpha:f32,\\n  uInverse:f32,\\n};\\n\\n@group(0) @binding(0) var<uniform> gfu: GlobalFilterUniforms;\\n@group(0) @binding(1) var uTexture: texture_2d<f32>;\\n@group(0) @binding(2) var uSampler : sampler;\\n\\n@group(1) @binding(0) var<uniform> filterUniforms : MaskUniforms;\\n@group(1) @binding(1) var uMaskTexture: texture_2d<f32>;\\n\\nstruct VSOutput {\\n    @builtin(position) position: vec4<f32>,\\n    @location(0) uv : vec2<f32>,\\n    @location(1) filterUv : vec2<f32>,\\n};\\n\\nfn filterVertexPosition(aPosition:vec2<f32>) -> vec4<f32>\\n{\\n    var position = aPosition * gfu.uOutputFrame.zw + gfu.uOutputFrame.xy;\\n\\n    position.x = position.x * (2.0 / gfu.uOutputTexture.x) - 1.0;\\n    position.y = position.y * (2.0*gfu.uOutputTexture.z / gfu.uOutputTexture.y) - gfu.uOutputTexture.z;\\n\\n    return vec4(position, 0.0, 1.0);\\n}\\n\\nfn filterTextureCoord( aPosition:vec2<f32> ) -> vec2<f32>\\n{\\n    return aPosition * (gfu.uOutputFrame.zw * gfu.uInputSize.zw);\\n}\\n\\nfn globalTextureCoord( aPosition:vec2<f32> ) -> vec2<f32>\\n{\\n  return  (aPosition.xy / gfu.uGlobalFrame.zw) + (gfu.uGlobalFrame.xy / gfu.uGlobalFrame.zw);\\n}\\n\\nfn getFilterCoord(aPosition:vec2<f32> ) -> vec2<f32>\\n{\\n  return ( filterUniforms.uFilterMatrix * vec3( filterTextureCoord(aPosition), 1.0)  ).xy;\\n}\\n\\nfn getSize() -> vec2<f32>\\n{\\n  return gfu.uGlobalFrame.zw;\\n}\\n\\n@vertex\\nfn mainVertex(\\n  @location(0) aPosition : vec2<f32>,\\n) -> VSOutput {\\n  return VSOutput(\\n   filterVertexPosition(aPosition),\\n   filterTextureCoord(aPosition),\\n   getFilterCoord(aPosition)\\n  );\\n}\\n\\n@fragment\\nfn mainFragment(\\n  @location(0) uv: vec2<f32>,\\n  @location(1) filterUv: vec2<f32>,\\n  @builtin(position) position: vec4<f32>\\n) -> @location(0) vec4<f32> {\\n\\n    var maskClamp = filterUniforms.uMaskClamp;\\n    var uAlpha = filterUniforms.uAlpha;\\n\\n    var clip = step(3.5,\\n      step(maskClamp.x, filterUv.x) +\\n      step(maskClamp.y, filterUv.y) +\\n      step(filterUv.x, maskClamp.z) +\\n      step(filterUv.y, maskClamp.w));\\n\\n    var mask = textureSample(uMaskTexture, uSampler, filterUv);\\n    var source = textureSample(uTexture, uSampler, uv);\\n    var alphaMul = 1.0 - uAlpha * (1.0 - mask.a);\\n\\n    var a: f32 = alphaMul * mask.r * uAlpha * clip;\\n\\n    if (filterUniforms.uInverse == 1.0) {\\n        a = 1.0 - a;\\n    }\\n\\n    return source * a;\\n}\\n\";\nclass MaskFilter extends Filter {\n  constructor(options) {\n    const { sprite, ...rest } = options;\n    const textureMatrix = new TextureMatrix(sprite.texture);\n    const filterUniforms = new UniformGroup({\n      uFilterMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      uMaskClamp: { value: textureMatrix.uClampFrame, type: \"vec4<f32>\" },\n      uAlpha: { value: 1, type: \"f32\" },\n      uInverse: { value: options.inverse ? 1 : 0, type: \"f32\" }\n    });\n    const gpuProgram = GpuProgram.from({\n      vertex: {\n        source,\n        entryPoint: \"mainVertex\"\n      },\n      fragment: {\n        source,\n        entryPoint: \"mainFragment\"\n      }\n    });\n    const glProgram = GlProgram.from({\n      vertex,\n      fragment,\n      name: \"mask-filter\"\n    });\n    super({\n      ...rest,\n      gpuProgram,\n      glProgram,\n      resources: {\n        filterUniforms,\n        uMaskTexture: sprite.texture.source\n      }\n    });\n    this.sprite = sprite;\n    this._textureMatrix = textureMatrix;\n  }\n  set inverse(value) {\n    this.resources.filterUniforms.uniforms.uInverse = value ? 1 : 0;\n  }\n  get inverse() {\n    return this.resources.filterUniforms.uniforms.uInverse === 1;\n  }\n  apply(filterManager, input, output, clearMode) {\n    this._textureMatrix.texture = this.sprite.texture;\n    filterManager.calculateSpriteMatrix(\n      this.resources.filterUniforms.uniforms.uFilterMatrix,\n      this.sprite\n    ).prepend(this._textureMatrix.mapCoord);\n    this.resources.uMaskTexture = this.sprite.texture.source;\n    filterManager.applyFilter(this, input, output, clearMode);\n  }\n}\nconst _BatcherPipe = class _BatcherPipe2 {\n  constructor(renderer, adaptor) {\n    this.state = State.for2d();\n    this._batchersByInstructionSet = /* @__PURE__ */ Object.create(null);\n    this._activeBatches = /* @__PURE__ */ Object.create(null);\n    this.renderer = renderer;\n    this._adaptor = adaptor;\n    this._adaptor.init?.(this);\n  }\n  static getBatcher(name) {\n    return new this._availableBatchers[name]();\n  }\n  buildStart(instructionSet) {\n    let batchers = this._batchersByInstructionSet[instructionSet.uid];\n    if (!batchers) {\n      batchers = this._batchersByInstructionSet[instructionSet.uid] = /* @__PURE__ */ Object.create(null);\n      batchers.default || (batchers.default = new DefaultBatcher());\n    }\n    this._activeBatches = batchers;\n    this._activeBatch = this._activeBatches.default;\n    for (const i in this._activeBatches) {\n      this._activeBatches[i].begin();\n    }\n  }\n  addToBatch(batchableObject, instructionSet) {\n    if (this._activeBatch.name !== batchableObject.batcherName) {\n      this._activeBatch.break(instructionSet);\n      let batch = this._activeBatches[batchableObject.batcherName];\n      if (!batch) {\n        batch = this._activeBatches[batchableObject.batcherName] = _BatcherPipe2.getBatcher(batchableObject.batcherName);\n        batch.begin();\n      }\n      this._activeBatch = batch;\n    }\n    this._activeBatch.add(batchableObject);\n  }\n  break(instructionSet) {\n    this._activeBatch.break(instructionSet);\n  }\n  buildEnd(instructionSet) {\n    this._activeBatch.break(instructionSet);\n    const batches = this._activeBatches;\n    for (const i in batches) {\n      const batch = batches[i];\n      const geometry = batch.geometry;\n      geometry.indexBuffer.setDataWithSize(batch.indexBuffer, batch.indexSize, true);\n      geometry.buffers[0].setDataWithSize(batch.attributeBuffer.float32View, batch.attributeSize, false);\n    }\n  }\n  upload(instructionSet) {\n    const batchers = this._batchersByInstructionSet[instructionSet.uid];\n    for (const i in batchers) {\n      const batcher = batchers[i];\n      const geometry = batcher.geometry;\n      if (batcher.dirty) {\n        batcher.dirty = false;\n        geometry.buffers[0].update(batcher.attributeSize * 4);\n      }\n    }\n  }\n  execute(batch) {\n    if (batch.action === \"startBatch\") {\n      const batcher = batch.batcher;\n      const geometry = batcher.geometry;\n      const shader = batcher.shader;\n      this._adaptor.start(this, geometry, shader);\n    }\n    this._adaptor.execute(this, batch);\n  }\n  destroy() {\n    this.state = null;\n    this.renderer = null;\n    this._adaptor = null;\n    for (const i in this._activeBatches) {\n      this._activeBatches[i].destroy();\n    }\n    this._activeBatches = null;\n  }\n};\n_BatcherPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"batch\"\n};\n_BatcherPipe._availableBatchers = /* @__PURE__ */ Object.create(null);\nlet BatcherPipe = _BatcherPipe;\nextensions.handleByMap(ExtensionType.Batcher, BatcherPipe._availableBatchers);\nextensions.add(DefaultBatcher);\nconst textureBit = {\n  name: \"texture-bit\",\n  vertex: {\n    header: (\n      /* wgsl */\n      `\n\n        struct TextureUniforms {\n            uTextureMatrix:mat3x3<f32>,\n        }\n\n        @group(2) @binding(2) var<uniform> textureUniforms : TextureUniforms;\n        `\n    ),\n    main: (\n      /* wgsl */\n      `\n            uv = (textureUniforms.uTextureMatrix * vec3(uv, 1.0)).xy;\n        `\n    )\n  },\n  fragment: {\n    header: (\n      /* wgsl */\n      `\n            @group(2) @binding(0) var uTexture: texture_2d<f32>;\n            @group(2) @binding(1) var uSampler: sampler;\n\n         \n        `\n    ),\n    main: (\n      /* wgsl */\n      `\n            outColor = textureSample(uTexture, uSampler, vUV);\n        `\n    )\n  }\n};\nconst textureBitGl = {\n  name: \"texture-bit\",\n  vertex: {\n    header: (\n      /* glsl */\n      `\n            uniform mat3 uTextureMatrix;\n        `\n    ),\n    main: (\n      /* glsl */\n      `\n            uv = (uTextureMatrix * vec3(uv, 1.0)).xy;\n        `\n    )\n  },\n  fragment: {\n    header: (\n      /* glsl */\n      `\n        uniform sampler2D uTexture;\n\n         \n        `\n    ),\n    main: (\n      /* glsl */\n      `\n            outColor = texture(uTexture, vUV);\n        `\n    )\n  }\n};\nconst tempBounds$1 = new Bounds();\nclass AlphaMaskEffect extends FilterEffect {\n  constructor() {\n    super();\n    this.filters = [new MaskFilter({\n      sprite: new Sprite(Texture.EMPTY),\n      inverse: false,\n      resolution: \"inherit\",\n      antialias: \"inherit\"\n    })];\n  }\n  get sprite() {\n    return this.filters[0].sprite;\n  }\n  set sprite(value) {\n    this.filters[0].sprite = value;\n  }\n  get inverse() {\n    return this.filters[0].inverse;\n  }\n  set inverse(value) {\n    this.filters[0].inverse = value;\n  }\n}\nclass AlphaMaskPipe {\n  constructor(renderer) {\n    this._activeMaskStage = [];\n    this._renderer = renderer;\n  }\n  push(mask, maskedContainer, instructionSet) {\n    const renderer = this._renderer;\n    renderer.renderPipes.batch.break(instructionSet);\n    instructionSet.add({\n      renderPipeId: \"alphaMask\",\n      action: \"pushMaskBegin\",\n      mask,\n      inverse: maskedContainer._maskOptions.inverse,\n      canBundle: false,\n      maskedContainer\n    });\n    mask.inverse = maskedContainer._maskOptions.inverse;\n    if (mask.renderMaskToTexture) {\n      const maskContainer = mask.mask;\n      maskContainer.includeInBuild = true;\n      maskContainer.collectRenderables(\n        instructionSet,\n        renderer,\n        null\n      );\n      maskContainer.includeInBuild = false;\n    }\n    renderer.renderPipes.batch.break(instructionSet);\n    instructionSet.add({\n      renderPipeId: \"alphaMask\",\n      action: \"pushMaskEnd\",\n      mask,\n      maskedContainer,\n      inverse: maskedContainer._maskOptions.inverse,\n      canBundle: false\n    });\n  }\n  pop(mask, _maskedContainer, instructionSet) {\n    const renderer = this._renderer;\n    renderer.renderPipes.batch.break(instructionSet);\n    instructionSet.add({\n      renderPipeId: \"alphaMask\",\n      action: \"popMaskEnd\",\n      mask,\n      inverse: _maskedContainer._maskOptions.inverse,\n      canBundle: false\n    });\n  }\n  execute(instruction) {\n    const renderer = this._renderer;\n    const renderMask = instruction.mask.renderMaskToTexture;\n    if (instruction.action === \"pushMaskBegin\") {\n      const filterEffect = BigPool.get(AlphaMaskEffect);\n      filterEffect.inverse = instruction.inverse;\n      if (renderMask) {\n        instruction.mask.mask.measurable = true;\n        const bounds = getGlobalBounds(instruction.mask.mask, true, tempBounds$1);\n        instruction.mask.mask.measurable = false;\n        bounds.ceil();\n        const colorTextureSource = renderer.renderTarget.renderTarget.colorTexture.source;\n        const filterTexture = TexturePool.getOptimalTexture(\n          bounds.width,\n          bounds.height,\n          colorTextureSource._resolution,\n          colorTextureSource.antialias\n        );\n        renderer.renderTarget.push(filterTexture, true);\n        renderer.globalUniforms.push({\n          offset: bounds,\n          worldColor: 4294967295\n        });\n        const sprite = filterEffect.sprite;\n        sprite.texture = filterTexture;\n        sprite.worldTransform.tx = bounds.minX;\n        sprite.worldTransform.ty = bounds.minY;\n        this._activeMaskStage.push({\n          filterEffect,\n          maskedContainer: instruction.maskedContainer,\n          filterTexture\n        });\n      } else {\n        filterEffect.sprite = instruction.mask.mask;\n        this._activeMaskStage.push({\n          filterEffect,\n          maskedContainer: instruction.maskedContainer\n        });\n      }\n    } else if (instruction.action === \"pushMaskEnd\") {\n      const maskData = this._activeMaskStage[this._activeMaskStage.length - 1];\n      if (renderMask) {\n        if (renderer.type === RendererType.WEBGL) {\n          renderer.renderTarget.finishRenderPass();\n        }\n        renderer.renderTarget.pop();\n        renderer.globalUniforms.pop();\n      }\n      renderer.filter.push({\n        renderPipeId: \"filter\",\n        action: \"pushFilter\",\n        container: maskData.maskedContainer,\n        filterEffect: maskData.filterEffect,\n        canBundle: false\n      });\n    } else if (instruction.action === \"popMaskEnd\") {\n      renderer.filter.pop();\n      const maskData = this._activeMaskStage.pop();\n      if (renderMask) {\n        TexturePool.returnTexture(maskData.filterTexture);\n      }\n      BigPool.return(maskData.filterEffect);\n    }\n  }\n  destroy() {\n    this._renderer = null;\n    this._activeMaskStage = null;\n  }\n}\nAlphaMaskPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"alphaMask\"\n};\nclass ColorMaskPipe {\n  constructor(renderer) {\n    this._colorStack = [];\n    this._colorStackIndex = 0;\n    this._currentColor = 0;\n    this._renderer = renderer;\n  }\n  buildStart() {\n    this._colorStack[0] = 15;\n    this._colorStackIndex = 1;\n    this._currentColor = 15;\n  }\n  push(mask, _container, instructionSet) {\n    const renderer = this._renderer;\n    renderer.renderPipes.batch.break(instructionSet);\n    const colorStack = this._colorStack;\n    colorStack[this._colorStackIndex] = colorStack[this._colorStackIndex - 1] & mask.mask;\n    const currentColor = this._colorStack[this._colorStackIndex];\n    if (currentColor !== this._currentColor) {\n      this._currentColor = currentColor;\n      instructionSet.add({\n        renderPipeId: \"colorMask\",\n        colorMask: currentColor,\n        canBundle: false\n      });\n    }\n    this._colorStackIndex++;\n  }\n  pop(_mask, _container, instructionSet) {\n    const renderer = this._renderer;\n    renderer.renderPipes.batch.break(instructionSet);\n    const colorStack = this._colorStack;\n    this._colorStackIndex--;\n    const currentColor = colorStack[this._colorStackIndex - 1];\n    if (currentColor !== this._currentColor) {\n      this._currentColor = currentColor;\n      instructionSet.add({\n        renderPipeId: \"colorMask\",\n        colorMask: currentColor,\n        canBundle: false\n      });\n    }\n  }\n  execute(instruction) {\n    const renderer = this._renderer;\n    renderer.colorMask.setMask(instruction.colorMask);\n  }\n  destroy() {\n    this._colorStack = null;\n  }\n}\nColorMaskPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"colorMask\"\n};\nclass StencilMaskPipe {\n  constructor(renderer) {\n    this._maskStackHash = {};\n    this._maskHash = /* @__PURE__ */ new WeakMap();\n    this._renderer = renderer;\n  }\n  push(mask, _container, instructionSet) {\n    var _a;\n    const effect = mask;\n    const renderer = this._renderer;\n    renderer.renderPipes.batch.break(instructionSet);\n    renderer.renderPipes.blendMode.setBlendMode(effect.mask, \"none\", instructionSet);\n    instructionSet.add({\n      renderPipeId: \"stencilMask\",\n      action: \"pushMaskBegin\",\n      mask,\n      inverse: _container._maskOptions.inverse,\n      canBundle: false\n    });\n    const maskContainer = effect.mask;\n    maskContainer.includeInBuild = true;\n    if (!this._maskHash.has(effect)) {\n      this._maskHash.set(effect, {\n        instructionsStart: 0,\n        instructionsLength: 0\n      });\n    }\n    const maskData = this._maskHash.get(effect);\n    maskData.instructionsStart = instructionSet.instructionSize;\n    maskContainer.collectRenderables(\n      instructionSet,\n      renderer,\n      null\n    );\n    maskContainer.includeInBuild = false;\n    renderer.renderPipes.batch.break(instructionSet);\n    instructionSet.add({\n      renderPipeId: \"stencilMask\",\n      action: \"pushMaskEnd\",\n      mask,\n      inverse: _container._maskOptions.inverse,\n      canBundle: false\n    });\n    const instructionsLength = instructionSet.instructionSize - maskData.instructionsStart - 1;\n    maskData.instructionsLength = instructionsLength;\n    const renderTargetUid = renderer.renderTarget.renderTarget.uid;\n    (_a = this._maskStackHash)[renderTargetUid] ?? (_a[renderTargetUid] = 0);\n  }\n  pop(mask, _container, instructionSet) {\n    const effect = mask;\n    const renderer = this._renderer;\n    renderer.renderPipes.batch.break(instructionSet);\n    renderer.renderPipes.blendMode.setBlendMode(effect.mask, \"none\", instructionSet);\n    instructionSet.add({\n      renderPipeId: \"stencilMask\",\n      action: \"popMaskBegin\",\n      inverse: _container._maskOptions.inverse,\n      canBundle: false\n    });\n    const maskData = this._maskHash.get(mask);\n    for (let i = 0; i < maskData.instructionsLength; i++) {\n      instructionSet.instructions[instructionSet.instructionSize++] = instructionSet.instructions[maskData.instructionsStart++];\n    }\n    instructionSet.add({\n      renderPipeId: \"stencilMask\",\n      action: \"popMaskEnd\",\n      canBundle: false\n    });\n  }\n  execute(instruction) {\n    var _a;\n    const renderer = this._renderer;\n    const renderTargetUid = renderer.renderTarget.renderTarget.uid;\n    let maskStackIndex = (_a = this._maskStackHash)[renderTargetUid] ?? (_a[renderTargetUid] = 0);\n    if (instruction.action === \"pushMaskBegin\") {\n      renderer.renderTarget.ensureDepthStencil();\n      renderer.stencil.setStencilMode(STENCIL_MODES.RENDERING_MASK_ADD, maskStackIndex);\n      maskStackIndex++;\n      renderer.colorMask.setMask(0);\n    } else if (instruction.action === \"pushMaskEnd\") {\n      if (instruction.inverse) {\n        renderer.stencil.setStencilMode(STENCIL_MODES.INVERSE_MASK_ACTIVE, maskStackIndex);\n      } else {\n        renderer.stencil.setStencilMode(STENCIL_MODES.MASK_ACTIVE, maskStackIndex);\n      }\n      renderer.colorMask.setMask(15);\n    } else if (instruction.action === \"popMaskBegin\") {\n      renderer.colorMask.setMask(0);\n      if (maskStackIndex !== 0) {\n        renderer.stencil.setStencilMode(STENCIL_MODES.RENDERING_MASK_REMOVE, maskStackIndex);\n      } else {\n        renderer.renderTarget.clear(null, CLEAR.STENCIL);\n        renderer.stencil.setStencilMode(STENCIL_MODES.DISABLED, maskStackIndex);\n      }\n      maskStackIndex--;\n    } else if (instruction.action === \"popMaskEnd\") {\n      if (instruction.inverse) {\n        renderer.stencil.setStencilMode(STENCIL_MODES.INVERSE_MASK_ACTIVE, maskStackIndex);\n      } else {\n        renderer.stencil.setStencilMode(STENCIL_MODES.MASK_ACTIVE, maskStackIndex);\n      }\n      renderer.colorMask.setMask(15);\n    }\n    this._maskStackHash[renderTargetUid] = maskStackIndex;\n  }\n  destroy() {\n    this._renderer = null;\n    this._maskStackHash = null;\n    this._maskHash = null;\n  }\n}\nStencilMaskPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"stencilMask\"\n};\nfunction ensureAttributes(geometry, extractedData) {\n  for (const i in geometry.attributes) {\n    const attribute = geometry.attributes[i];\n    const attributeData = extractedData[i];\n    if (attributeData) {\n      attribute.format ?? (attribute.format = attributeData.format);\n      attribute.offset ?? (attribute.offset = attributeData.offset);\n      attribute.instance ?? (attribute.instance = attributeData.instance);\n    } else {\n      warn(`Attribute ${i} is not present in the shader, but is present in the geometry. Unable to infer attribute details.`);\n    }\n  }\n  ensureStartAndStride(geometry);\n}\nfunction ensureStartAndStride(geometry) {\n  const { buffers, attributes } = geometry;\n  const tempStride = {};\n  const tempStart = {};\n  for (const j in buffers) {\n    const buffer = buffers[j];\n    tempStride[buffer.uid] = 0;\n    tempStart[buffer.uid] = 0;\n  }\n  for (const j in attributes) {\n    const attribute = attributes[j];\n    tempStride[attribute.buffer.uid] += getAttributeInfoFromFormat(attribute.format).stride;\n  }\n  for (const j in attributes) {\n    const attribute = attributes[j];\n    attribute.stride ?? (attribute.stride = tempStride[attribute.buffer.uid]);\n    attribute.start ?? (attribute.start = tempStart[attribute.buffer.uid]);\n    tempStart[attribute.buffer.uid] += getAttributeInfoFromFormat(attribute.format).stride;\n  }\n}\nconst GpuStencilModesToPixi = [];\nGpuStencilModesToPixi[STENCIL_MODES.NONE] = void 0;\nGpuStencilModesToPixi[STENCIL_MODES.DISABLED] = {\n  stencilWriteMask: 0,\n  stencilReadMask: 0\n};\nGpuStencilModesToPixi[STENCIL_MODES.RENDERING_MASK_ADD] = {\n  stencilFront: {\n    compare: \"equal\",\n    passOp: \"increment-clamp\"\n  },\n  stencilBack: {\n    compare: \"equal\",\n    passOp: \"increment-clamp\"\n  }\n};\nGpuStencilModesToPixi[STENCIL_MODES.RENDERING_MASK_REMOVE] = {\n  stencilFront: {\n    compare: \"equal\",\n    passOp: \"decrement-clamp\"\n  },\n  stencilBack: {\n    compare: \"equal\",\n    passOp: \"decrement-clamp\"\n  }\n};\nGpuStencilModesToPixi[STENCIL_MODES.MASK_ACTIVE] = {\n  stencilWriteMask: 0,\n  stencilFront: {\n    compare: \"equal\",\n    passOp: \"keep\"\n  },\n  stencilBack: {\n    compare: \"equal\",\n    passOp: \"keep\"\n  }\n};\nGpuStencilModesToPixi[STENCIL_MODES.INVERSE_MASK_ACTIVE] = {\n  stencilWriteMask: 0,\n  stencilFront: {\n    compare: \"not-equal\",\n    passOp: \"replace\"\n  },\n  stencilBack: {\n    compare: \"not-equal\",\n    passOp: \"replace\"\n  }\n};\nclass UboSystem {\n  constructor(adaptor) {\n    this._syncFunctionHash = /* @__PURE__ */ Object.create(null);\n    this._adaptor = adaptor;\n    this._systemCheck();\n  }\n  /**\n   * Overridable function by `pixi.js/unsafe-eval` to silence\n   * throwing an error if platform doesn't support unsafe-evals.\n   * @private\n   */\n  _systemCheck() {\n    if (!unsafeEvalSupported()) {\n      throw new Error(\"Current environment does not allow unsafe-eval, please use pixi.js/unsafe-eval module to enable support.\");\n    }\n  }\n  ensureUniformGroup(uniformGroup) {\n    const uniformData = this.getUniformGroupData(uniformGroup);\n    uniformGroup.buffer || (uniformGroup.buffer = new Buffer({\n      data: new Float32Array(uniformData.layout.size / 4),\n      usage: BufferUsage.UNIFORM | BufferUsage.COPY_DST\n    }));\n  }\n  getUniformGroupData(uniformGroup) {\n    return this._syncFunctionHash[uniformGroup._signature] || this._initUniformGroup(uniformGroup);\n  }\n  _initUniformGroup(uniformGroup) {\n    const uniformGroupSignature = uniformGroup._signature;\n    let uniformData = this._syncFunctionHash[uniformGroupSignature];\n    if (!uniformData) {\n      const elements = Object.keys(uniformGroup.uniformStructures).map((i) => uniformGroup.uniformStructures[i]);\n      const layout = this._adaptor.createUboElements(elements);\n      const syncFunction = this._generateUboSync(layout.uboElements);\n      uniformData = this._syncFunctionHash[uniformGroupSignature] = {\n        layout,\n        syncFunction\n      };\n    }\n    return this._syncFunctionHash[uniformGroupSignature];\n  }\n  _generateUboSync(uboElements) {\n    return this._adaptor.generateUboSync(uboElements);\n  }\n  syncUniformGroup(uniformGroup, data, offset) {\n    const uniformGroupData = this.getUniformGroupData(uniformGroup);\n    uniformGroup.buffer || (uniformGroup.buffer = new Buffer({\n      data: new Float32Array(uniformGroupData.layout.size / 4),\n      usage: BufferUsage.UNIFORM | BufferUsage.COPY_DST\n    }));\n    let dataInt32 = null;\n    if (!data) {\n      data = uniformGroup.buffer.data;\n      dataInt32 = uniformGroup.buffer.dataInt32;\n    }\n    offset || (offset = 0);\n    uniformGroupData.syncFunction(uniformGroup.uniforms, data, dataInt32, offset);\n    return true;\n  }\n  updateUniformGroup(uniformGroup) {\n    if (uniformGroup.isStatic && !uniformGroup._dirtyId)\n      return false;\n    uniformGroup._dirtyId = 0;\n    const synced = this.syncUniformGroup(uniformGroup);\n    uniformGroup.buffer.update();\n    return synced;\n  }\n  destroy() {\n    this._syncFunctionHash = null;\n  }\n}\nconst uniformParsers = [\n  // uploading pixi matrix object to mat3\n  {\n    type: \"mat3x3<f32>\",\n    test: (data) => {\n      const value = data.value;\n      return value.a !== void 0;\n    },\n    ubo: `\n            var matrix = uv[name].toArray(true);\n            data[offset] = matrix[0];\n            data[offset + 1] = matrix[1];\n            data[offset + 2] = matrix[2];\n            data[offset + 4] = matrix[3];\n            data[offset + 5] = matrix[4];\n            data[offset + 6] = matrix[5];\n            data[offset + 8] = matrix[6];\n            data[offset + 9] = matrix[7];\n            data[offset + 10] = matrix[8];\n        `,\n    uniform: `\n            gl.uniformMatrix3fv(ud[name].location, false, uv[name].toArray(true));\n        `\n  },\n  // uploading a pixi rectangle as a vec4\n  {\n    type: \"vec4<f32>\",\n    test: (data) => data.type === \"vec4<f32>\" && data.size === 1 && data.value.width !== void 0,\n    ubo: `\n            v = uv[name];\n            data[offset] = v.x;\n            data[offset + 1] = v.y;\n            data[offset + 2] = v.width;\n            data[offset + 3] = v.height;\n        `,\n    uniform: `\n            cv = ud[name].value;\n            v = uv[name];\n            if (cv[0] !== v.x || cv[1] !== v.y || cv[2] !== v.width || cv[3] !== v.height) {\n                cv[0] = v.x;\n                cv[1] = v.y;\n                cv[2] = v.width;\n                cv[3] = v.height;\n                gl.uniform4f(ud[name].location, v.x, v.y, v.width, v.height);\n            }\n        `\n  },\n  // uploading a pixi point as a vec2\n  {\n    type: \"vec2<f32>\",\n    test: (data) => data.type === \"vec2<f32>\" && data.size === 1 && data.value.x !== void 0,\n    ubo: `\n            v = uv[name];\n            data[offset] = v.x;\n            data[offset + 1] = v.y;\n        `,\n    uniform: `\n            cv = ud[name].value;\n            v = uv[name];\n            if (cv[0] !== v.x || cv[1] !== v.y) {\n                cv[0] = v.x;\n                cv[1] = v.y;\n                gl.uniform2f(ud[name].location, v.x, v.y);\n            }\n        `\n  },\n  // uploading a pixi color as a vec4\n  {\n    type: \"vec4<f32>\",\n    test: (data) => data.type === \"vec4<f32>\" && data.size === 1 && data.value.red !== void 0,\n    ubo: `\n            v = uv[name];\n            data[offset] = v.red;\n            data[offset + 1] = v.green;\n            data[offset + 2] = v.blue;\n            data[offset + 3] = v.alpha;\n        `,\n    uniform: `\n            cv = ud[name].value;\n            v = uv[name];\n            if (cv[0] !== v.red || cv[1] !== v.green || cv[2] !== v.blue || cv[3] !== v.alpha) {\n                cv[0] = v.red;\n                cv[1] = v.green;\n                cv[2] = v.blue;\n                cv[3] = v.alpha;\n                gl.uniform4f(ud[name].location, v.red, v.green, v.blue, v.alpha);\n            }\n        `\n  },\n  // uploading a pixi color as a vec3\n  {\n    type: \"vec3<f32>\",\n    test: (data) => data.type === \"vec3<f32>\" && data.size === 1 && data.value.red !== void 0,\n    ubo: `\n            v = uv[name];\n            data[offset] = v.red;\n            data[offset + 1] = v.green;\n            data[offset + 2] = v.blue;\n        `,\n    uniform: `\n            cv = ud[name].value;\n            v = uv[name];\n            if (cv[0] !== v.red || cv[1] !== v.green || cv[2] !== v.blue) {\n                cv[0] = v.red;\n                cv[1] = v.green;\n                cv[2] = v.blue;\n                gl.uniform3f(ud[name].location, v.red, v.green, v.blue);\n            }\n        `\n  }\n];\nfunction createUboSyncFunction(uboElements, parserCode, arrayGenerationFunction, singleSettersMap) {\n  const funcFragments = [`\n        var v = null;\n        var v2 = null;\n        var t = 0;\n        var index = 0;\n        var name = null;\n        var arrayOffset = null;\n    `];\n  let prev = 0;\n  for (let i = 0; i < uboElements.length; i++) {\n    const uboElement = uboElements[i];\n    const name = uboElement.data.name;\n    let parsed = false;\n    let offset = 0;\n    for (let j = 0; j < uniformParsers.length; j++) {\n      const uniformParser = uniformParsers[j];\n      if (uniformParser.test(uboElement.data)) {\n        offset = uboElement.offset / 4;\n        funcFragments.push(\n          `name = \"${name}\";`,\n          `offset += ${offset - prev};`,\n          uniformParsers[j][parserCode] || uniformParsers[j].ubo\n        );\n        parsed = true;\n        break;\n      }\n    }\n    if (!parsed) {\n      if (uboElement.data.size > 1) {\n        offset = uboElement.offset / 4;\n        funcFragments.push(arrayGenerationFunction(uboElement, offset - prev));\n      } else {\n        const template = singleSettersMap[uboElement.data.type];\n        offset = uboElement.offset / 4;\n        funcFragments.push(\n          /* wgsl */\n          `\n                    v = uv.${name};\n                    offset += ${offset - prev};\n                    ${template};\n                `\n        );\n      }\n    }\n    prev = offset;\n  }\n  const fragmentSrc = funcFragments.join(\"\\n\");\n  return new Function(\n    \"uv\",\n    \"data\",\n    \"dataInt32\",\n    \"offset\",\n    fragmentSrc\n  );\n}\nfunction loopMatrix(col, row) {\n  const total = col * row;\n  return `\n        for (let i = 0; i < ${total}; i++) {\n            data[offset + (((i / ${col})|0) * 4) + (i % ${col})] = v[i];\n        }\n    `;\n}\nconst uboSyncFunctionsSTD40 = {\n  f32: `\n        data[offset] = v;`,\n  i32: `\n        dataInt32[offset] = v;`,\n  \"vec2<f32>\": `\n        data[offset] = v[0];\n        data[offset + 1] = v[1];`,\n  \"vec3<f32>\": `\n        data[offset] = v[0];\n        data[offset + 1] = v[1];\n        data[offset + 2] = v[2];`,\n  \"vec4<f32>\": `\n        data[offset] = v[0];\n        data[offset + 1] = v[1];\n        data[offset + 2] = v[2];\n        data[offset + 3] = v[3];`,\n  \"vec2<i32>\": `\n        dataInt32[offset] = v[0];\n        dataInt32[offset + 1] = v[1];`,\n  \"vec3<i32>\": `\n        dataInt32[offset] = v[0];\n        dataInt32[offset + 1] = v[1];\n        dataInt32[offset + 2] = v[2];`,\n  \"vec4<i32>\": `\n        dataInt32[offset] = v[0];\n        dataInt32[offset + 1] = v[1];\n        dataInt32[offset + 2] = v[2];\n        dataInt32[offset + 3] = v[3];`,\n  \"mat2x2<f32>\": `\n        data[offset] = v[0];\n        data[offset + 1] = v[1];\n        data[offset + 4] = v[2];\n        data[offset + 5] = v[3];`,\n  \"mat3x3<f32>\": `\n        data[offset] = v[0];\n        data[offset + 1] = v[1];\n        data[offset + 2] = v[2];\n        data[offset + 4] = v[3];\n        data[offset + 5] = v[4];\n        data[offset + 6] = v[5];\n        data[offset + 8] = v[6];\n        data[offset + 9] = v[7];\n        data[offset + 10] = v[8];`,\n  \"mat4x4<f32>\": `\n        for (let i = 0; i < 16; i++) {\n            data[offset + i] = v[i];\n        }`,\n  \"mat3x2<f32>\": loopMatrix(3, 2),\n  \"mat4x2<f32>\": loopMatrix(4, 2),\n  \"mat2x3<f32>\": loopMatrix(2, 3),\n  \"mat4x3<f32>\": loopMatrix(4, 3),\n  \"mat2x4<f32>\": loopMatrix(2, 4),\n  \"mat3x4<f32>\": loopMatrix(3, 4)\n};\nconst uboSyncFunctionsWGSL = {\n  ...uboSyncFunctionsSTD40,\n  \"mat2x2<f32>\": `\n        data[offset] = v[0];\n        data[offset + 1] = v[1];\n        data[offset + 2] = v[2];\n        data[offset + 3] = v[3];\n    `\n};\nfunction calculateProjection(pm, x, y, width, height, flipY) {\n  const sign = flipY ? 1 : -1;\n  pm.identity();\n  pm.a = 1 / width * 2;\n  pm.d = sign * (1 / height * 2);\n  pm.tx = -1 - x * pm.a;\n  pm.ty = -sign - y * pm.d;\n  return pm;\n}\nconst canvasCache = /* @__PURE__ */ new Map();\nfunction getCanvasTexture(canvas, options) {\n  if (!canvasCache.has(canvas)) {\n    const texture = new Texture({\n      source: new CanvasSource({\n        resource: canvas,\n        ...options\n      })\n    });\n    const onDestroy = () => {\n      if (canvasCache.get(canvas) === texture) {\n        canvasCache.delete(canvas);\n      }\n    };\n    texture.once(\"destroy\", onDestroy);\n    texture.source.once(\"destroy\", onDestroy);\n    canvasCache.set(canvas, texture);\n  }\n  return canvasCache.get(canvas);\n}\nfunction isRenderingToScreen(renderTarget) {\n  const resource = renderTarget.colorTexture.source.resource;\n  return globalThis.HTMLCanvasElement && resource instanceof HTMLCanvasElement && document.body.contains(resource);\n}\nconst _RenderTarget = class _RenderTarget2 {\n  /**\n   * @param [descriptor] - Options for creating a render target.\n   */\n  constructor(descriptor = {}) {\n    this.uid = uid$1(\"renderTarget\");\n    this.colorTextures = [];\n    this.dirtyId = 0;\n    this.isRoot = false;\n    this._size = new Float32Array(2);\n    this._managedColorTextures = false;\n    descriptor = { ..._RenderTarget2.defaultOptions, ...descriptor };\n    this.stencil = descriptor.stencil;\n    this.depth = descriptor.depth;\n    this.isRoot = descriptor.isRoot;\n    if (typeof descriptor.colorTextures === \"number\") {\n      this._managedColorTextures = true;\n      for (let i = 0; i < descriptor.colorTextures; i++) {\n        this.colorTextures.push(\n          new TextureSource({\n            width: descriptor.width,\n            height: descriptor.height,\n            resolution: descriptor.resolution,\n            antialias: descriptor.antialias\n          })\n        );\n      }\n    } else {\n      this.colorTextures = [...descriptor.colorTextures.map((texture) => texture.source)];\n      const colorSource = this.colorTexture.source;\n      this.resize(colorSource.width, colorSource.height, colorSource._resolution);\n    }\n    this.colorTexture.source.on(\"resize\", this.onSourceResize, this);\n    if (descriptor.depthStencilTexture || this.stencil) {\n      if (descriptor.depthStencilTexture instanceof Texture || descriptor.depthStencilTexture instanceof TextureSource) {\n        this.depthStencilTexture = descriptor.depthStencilTexture.source;\n      } else {\n        this.ensureDepthStencilTexture();\n      }\n    }\n  }\n  get size() {\n    const _size = this._size;\n    _size[0] = this.pixelWidth;\n    _size[1] = this.pixelHeight;\n    return _size;\n  }\n  get width() {\n    return this.colorTexture.source.width;\n  }\n  get height() {\n    return this.colorTexture.source.height;\n  }\n  get pixelWidth() {\n    return this.colorTexture.source.pixelWidth;\n  }\n  get pixelHeight() {\n    return this.colorTexture.source.pixelHeight;\n  }\n  get resolution() {\n    return this.colorTexture.source._resolution;\n  }\n  get colorTexture() {\n    return this.colorTextures[0];\n  }\n  onSourceResize(source2) {\n    this.resize(source2.width, source2.height, source2._resolution, true);\n  }\n  /**\n   * This will ensure a depthStencil texture is created for this render target.\n   * Most likely called by the mask system to make sure we have stencil buffer added.\n   * @internal\n   * @ignore\n   */\n  ensureDepthStencilTexture() {\n    if (!this.depthStencilTexture) {\n      this.depthStencilTexture = new TextureSource({\n        width: this.width,\n        height: this.height,\n        resolution: this.resolution,\n        format: \"depth24plus-stencil8\",\n        autoGenerateMipmaps: false,\n        antialias: false,\n        mipLevelCount: 1\n        // sampleCount: handled by the render target system..\n      });\n    }\n  }\n  resize(width, height, resolution = this.resolution, skipColorTexture = false) {\n    this.dirtyId++;\n    this.colorTextures.forEach((colorTexture, i) => {\n      if (skipColorTexture && i === 0)\n        return;\n      colorTexture.source.resize(width, height, resolution);\n    });\n    if (this.depthStencilTexture) {\n      this.depthStencilTexture.source.resize(width, height, resolution);\n    }\n  }\n  destroy() {\n    this.colorTexture.source.off(\"resize\", this.onSourceResize, this);\n    if (this._managedColorTextures) {\n      this.colorTextures.forEach((texture) => {\n        texture.destroy();\n      });\n    }\n    if (this.depthStencilTexture) {\n      this.depthStencilTexture.destroy();\n      delete this.depthStencilTexture;\n    }\n  }\n};\n_RenderTarget.defaultOptions = {\n  /** the width of the RenderTarget */\n  width: 0,\n  /** the height of the RenderTarget */\n  height: 0,\n  /** the resolution of the RenderTarget */\n  resolution: 1,\n  /** an array of textures, or a number indicating how many color textures there should be */\n  colorTextures: 1,\n  /** should this render target have a stencil buffer? */\n  stencil: false,\n  /** should this render target have a depth buffer? */\n  depth: false,\n  /** should this render target be antialiased? */\n  antialias: false,\n  // save on perf by default!\n  /** is this a root element, true if this is gl context owners render target */\n  isRoot: false\n};\nlet RenderTarget = _RenderTarget;\nclass RenderTargetSystem {\n  constructor(renderer) {\n    this.rootViewPort = new Rectangle();\n    this.viewport = new Rectangle();\n    this.onRenderTargetChange = new SystemRunner(\"onRenderTargetChange\");\n    this.projectionMatrix = new Matrix();\n    this.defaultClearColor = [0, 0, 0, 0];\n    this._renderSurfaceToRenderTargetHash = /* @__PURE__ */ new Map();\n    this._gpuRenderTargetHash = /* @__PURE__ */ Object.create(null);\n    this._renderTargetStack = [];\n    this._renderer = renderer;\n    renderer.renderableGC.addManagedHash(this, \"_gpuRenderTargetHash\");\n  }\n  /** called when dev wants to finish a render pass */\n  finishRenderPass() {\n    this.adaptor.finishRenderPass(this.renderTarget);\n  }\n  /**\n   * called when the renderer starts to render a scene.\n   * @param options\n   * @param options.target - the render target to render to\n   * @param options.clear - the clear mode to use. Can be true or a CLEAR number 'COLOR | DEPTH | STENCIL' 0b111\n   * @param options.clearColor - the color to clear to\n   * @param options.frame - the frame to render to\n   */\n  renderStart({\n    target,\n    clear,\n    clearColor,\n    frame\n  }) {\n    this._renderTargetStack.length = 0;\n    this.push(\n      target,\n      clear,\n      clearColor,\n      frame\n    );\n    this.rootViewPort.copyFrom(this.viewport);\n    this.rootRenderTarget = this.renderTarget;\n    this.renderingToScreen = isRenderingToScreen(this.rootRenderTarget);\n    this.adaptor.prerender?.(this.rootRenderTarget);\n  }\n  postrender() {\n    this.adaptor.postrender?.(this.rootRenderTarget);\n  }\n  /**\n   * Binding a render surface! This is the main function of the render target system.\n   * It will take the RenderSurface (which can be a texture, canvas, or render target) and bind it to the renderer.\n   * Once bound all draw calls will be rendered to the render surface.\n   *\n   * If a frame is not provide and the render surface is a texture, the frame of the texture will be used.\n   * @param renderSurface - the render surface to bind\n   * @param clear - the clear mode to use. Can be true or a CLEAR number 'COLOR | DEPTH | STENCIL' 0b111\n   * @param clearColor - the color to clear to\n   * @param frame - the frame to render to\n   * @returns the render target that was bound\n   */\n  bind(renderSurface, clear = true, clearColor, frame) {\n    const renderTarget = this.getRenderTarget(renderSurface);\n    const didChange = this.renderTarget !== renderTarget;\n    this.renderTarget = renderTarget;\n    this.renderSurface = renderSurface;\n    const gpuRenderTarget = this.getGpuRenderTarget(renderTarget);\n    if (renderTarget.pixelWidth !== gpuRenderTarget.width || renderTarget.pixelHeight !== gpuRenderTarget.height) {\n      this.adaptor.resizeGpuRenderTarget(renderTarget);\n      gpuRenderTarget.width = renderTarget.pixelWidth;\n      gpuRenderTarget.height = renderTarget.pixelHeight;\n    }\n    const source2 = renderTarget.colorTexture;\n    const viewport = this.viewport;\n    const pixelWidth = source2.pixelWidth;\n    const pixelHeight = source2.pixelHeight;\n    if (!frame && renderSurface instanceof Texture) {\n      frame = renderSurface.frame;\n    }\n    if (frame) {\n      const resolution = source2._resolution;\n      viewport.x = frame.x * resolution + 0.5 | 0;\n      viewport.y = frame.y * resolution + 0.5 | 0;\n      viewport.width = frame.width * resolution + 0.5 | 0;\n      viewport.height = frame.height * resolution + 0.5 | 0;\n    } else {\n      viewport.x = 0;\n      viewport.y = 0;\n      viewport.width = pixelWidth;\n      viewport.height = pixelHeight;\n    }\n    calculateProjection(\n      this.projectionMatrix,\n      0,\n      0,\n      viewport.width / source2.resolution,\n      viewport.height / source2.resolution,\n      !renderTarget.isRoot\n    );\n    this.adaptor.startRenderPass(renderTarget, clear, clearColor, viewport);\n    if (didChange) {\n      this.onRenderTargetChange.emit(renderTarget);\n    }\n    return renderTarget;\n  }\n  clear(target, clear = CLEAR.ALL, clearColor) {\n    if (!clear)\n      return;\n    if (target) {\n      target = this.getRenderTarget(target);\n    }\n    this.adaptor.clear(\n      target || this.renderTarget,\n      clear,\n      clearColor,\n      this.viewport\n    );\n  }\n  contextChange() {\n    this._gpuRenderTargetHash = /* @__PURE__ */ Object.create(null);\n  }\n  /**\n   * Push a render surface to the renderer. This will bind the render surface to the renderer,\n   * @param renderSurface - the render surface to push\n   * @param clear - the clear mode to use. Can be true or a CLEAR number 'COLOR | DEPTH | STENCIL' 0b111\n   * @param clearColor - the color to clear to\n   * @param frame - the frame to use when rendering to the render surface\n   */\n  push(renderSurface, clear = CLEAR.ALL, clearColor, frame) {\n    const renderTarget = this.bind(renderSurface, clear, clearColor, frame);\n    this._renderTargetStack.push({\n      renderTarget,\n      frame\n    });\n    return renderTarget;\n  }\n  /** Pops the current render target from the renderer and restores the previous render target. */\n  pop() {\n    this._renderTargetStack.pop();\n    const currentRenderTargetData = this._renderTargetStack[this._renderTargetStack.length - 1];\n    this.bind(currentRenderTargetData.renderTarget, false, null, currentRenderTargetData.frame);\n  }\n  /**\n   * Gets the render target from the provide render surface. Eg if its a texture,\n   * it will return the render target for the texture.\n   * If its a render target, it will return the same render target.\n   * @param renderSurface - the render surface to get the render target for\n   * @returns the render target for the render surface\n   */\n  getRenderTarget(renderSurface) {\n    if (renderSurface.isTexture) {\n      renderSurface = renderSurface.source;\n    }\n    return this._renderSurfaceToRenderTargetHash.get(renderSurface) ?? this._initRenderTarget(renderSurface);\n  }\n  /**\n   * Copies a render surface to another texture\n   * @param sourceRenderSurfaceTexture - the render surface to copy from\n   * @param destinationTexture - the texture to copy to\n   * @param originSrc - the origin of the copy\n   * @param originSrc.x - the x origin of the copy\n   * @param originSrc.y - the y origin of the copy\n   * @param size - the size of the copy\n   * @param size.width - the width of the copy\n   * @param size.height - the height of the copy\n   * @param originDest - the destination origin (top left to paste from!)\n   * @param originDest.x - the x origin of the paste\n   * @param originDest.y - the y origin of the paste\n   */\n  copyToTexture(sourceRenderSurfaceTexture, destinationTexture, originSrc, size, originDest) {\n    if (originSrc.x < 0) {\n      size.width += originSrc.x;\n      originDest.x -= originSrc.x;\n      originSrc.x = 0;\n    }\n    if (originSrc.y < 0) {\n      size.height += originSrc.y;\n      originDest.y -= originSrc.y;\n      originSrc.y = 0;\n    }\n    const { pixelWidth, pixelHeight } = sourceRenderSurfaceTexture;\n    size.width = Math.min(size.width, pixelWidth - originSrc.x);\n    size.height = Math.min(size.height, pixelHeight - originSrc.y);\n    return this.adaptor.copyToTexture(\n      sourceRenderSurfaceTexture,\n      destinationTexture,\n      originSrc,\n      size,\n      originDest\n    );\n  }\n  /**\n   * ensures that we have a depth stencil buffer available to render to\n   * This is used by the mask system to make sure we have a stencil buffer.\n   */\n  ensureDepthStencil() {\n    if (!this.renderTarget.stencil) {\n      this.renderTarget.stencil = true;\n      this.adaptor.startRenderPass(this.renderTarget, false, null, this.viewport);\n    }\n  }\n  /** nukes the render target system */\n  destroy() {\n    this._renderer = null;\n    this._renderSurfaceToRenderTargetHash.forEach((renderTarget, key) => {\n      if (renderTarget !== key) {\n        renderTarget.destroy();\n      }\n    });\n    this._renderSurfaceToRenderTargetHash.clear();\n    this._gpuRenderTargetHash = /* @__PURE__ */ Object.create(null);\n  }\n  _initRenderTarget(renderSurface) {\n    let renderTarget = null;\n    if (CanvasSource.test(renderSurface)) {\n      renderSurface = getCanvasTexture(renderSurface).source;\n    }\n    if (renderSurface instanceof RenderTarget) {\n      renderTarget = renderSurface;\n    } else if (renderSurface instanceof TextureSource) {\n      renderTarget = new RenderTarget({\n        colorTextures: [renderSurface]\n      });\n      if (CanvasSource.test(renderSurface.source.resource)) {\n        renderTarget.isRoot = true;\n      }\n      renderSurface.once(\"destroy\", () => {\n        renderTarget.destroy();\n        this._renderSurfaceToRenderTargetHash.delete(renderSurface);\n        const gpuRenderTarget = this._gpuRenderTargetHash[renderTarget.uid];\n        if (gpuRenderTarget) {\n          this._gpuRenderTargetHash[renderTarget.uid] = null;\n          this.adaptor.destroyGpuRenderTarget(gpuRenderTarget);\n        }\n      });\n    }\n    this._renderSurfaceToRenderTargetHash.set(renderSurface, renderTarget);\n    return renderTarget;\n  }\n  getGpuRenderTarget(renderTarget) {\n    return this._gpuRenderTargetHash[renderTarget.uid] || (this._gpuRenderTargetHash[renderTarget.uid] = this.adaptor.initGpuRenderTarget(renderTarget));\n  }\n  resetState() {\n    this.renderTarget = null;\n    this.renderSurface = null;\n  }\n}\nclass BufferResource extends EventEmitter {\n  /**\n   * Create a new Buffer Resource.\n   * @param options - The options for the buffer resource\n   * @param options.buffer - The underlying buffer that this resource is using\n   * @param options.offset - The offset of the buffer this resource is using.\n   * If not provided, then it will use the offset of the buffer.\n   * @param options.size - The size of the buffer this resource is using.\n   * If not provided, then it will use the size of the buffer.\n   */\n  constructor({ buffer, offset, size }) {\n    super();\n    this.uid = uid$1(\"buffer\");\n    this._resourceType = \"bufferResource\";\n    this._touched = 0;\n    this._resourceId = uid$1(\"resource\");\n    this._bufferResource = true;\n    this.destroyed = false;\n    this.buffer = buffer;\n    this.offset = offset | 0;\n    this.size = size;\n    this.buffer.on(\"change\", this.onBufferChange, this);\n  }\n  onBufferChange() {\n    this._resourceId = uid$1(\"resource\");\n    this.emit(\"change\", this);\n  }\n  /**\n   * Destroys this resource. Make sure the underlying buffer is not used anywhere else\n   * if you want to destroy it as well, or code will explode\n   * @param destroyBuffer - Should the underlying buffer be destroyed as well?\n   */\n  destroy(destroyBuffer = false) {\n    this.destroyed = true;\n    if (destroyBuffer) {\n      this.buffer.destroy();\n    }\n    this.emit(\"change\", this);\n    this.buffer = null;\n  }\n}\nclass CustomRenderPipe {\n  constructor(renderer) {\n    this._renderer = renderer;\n  }\n  updateRenderable() {\n  }\n  destroyRenderable() {\n  }\n  validateRenderable() {\n    return false;\n  }\n  addRenderable(container, instructionSet) {\n    this._renderer.renderPipes.batch.break(instructionSet);\n    instructionSet.add(container);\n  }\n  execute(container) {\n    if (!container.isRenderable)\n      return;\n    container.render(this._renderer);\n  }\n  destroy() {\n    this._renderer = null;\n  }\n}\nCustomRenderPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"customRender\"\n};\nfunction executeInstructions(renderGroup, renderer) {\n  const instructionSet = renderGroup.instructionSet;\n  const instructions = instructionSet.instructions;\n  for (let i = 0; i < instructionSet.instructionSize; i++) {\n    const instruction = instructions[i];\n    renderer[instruction.renderPipeId].execute(instruction);\n  }\n}\nconst tempMatrix$1 = new Matrix();\nclass RenderGroupPipe {\n  constructor(renderer) {\n    this._renderer = renderer;\n  }\n  addRenderGroup(renderGroup, instructionSet) {\n    if (renderGroup.isCachedAsTexture) {\n      this._addRenderableCacheAsTexture(renderGroup, instructionSet);\n    } else {\n      this._addRenderableDirect(renderGroup, instructionSet);\n    }\n  }\n  execute(renderGroup) {\n    if (!renderGroup.isRenderable)\n      return;\n    if (renderGroup.isCachedAsTexture) {\n      this._executeCacheAsTexture(renderGroup);\n    } else {\n      this._executeDirect(renderGroup);\n    }\n  }\n  destroy() {\n    this._renderer = null;\n  }\n  _addRenderableDirect(renderGroup, instructionSet) {\n    this._renderer.renderPipes.batch.break(instructionSet);\n    if (renderGroup._batchableRenderGroup) {\n      BigPool.return(renderGroup._batchableRenderGroup);\n      renderGroup._batchableRenderGroup = null;\n    }\n    instructionSet.add(renderGroup);\n  }\n  _addRenderableCacheAsTexture(renderGroup, instructionSet) {\n    const batchableRenderGroup = renderGroup._batchableRenderGroup ?? (renderGroup._batchableRenderGroup = BigPool.get(BatchableSprite));\n    batchableRenderGroup.renderable = renderGroup.root;\n    batchableRenderGroup.transform = renderGroup.root.relativeGroupTransform;\n    batchableRenderGroup.texture = renderGroup.texture;\n    batchableRenderGroup.bounds = renderGroup._textureBounds;\n    instructionSet.add(renderGroup);\n    this._renderer.renderPipes.batch.addToBatch(batchableRenderGroup, instructionSet);\n  }\n  _executeCacheAsTexture(renderGroup) {\n    if (renderGroup.textureNeedsUpdate) {\n      renderGroup.textureNeedsUpdate = false;\n      const worldTransformMatrix = tempMatrix$1.identity().translate(\n        -renderGroup._textureBounds.x,\n        -renderGroup._textureBounds.y\n      );\n      this._renderer.renderTarget.push(renderGroup.texture, true, null, renderGroup.texture.frame);\n      this._renderer.globalUniforms.push({\n        worldTransformMatrix,\n        worldColor: 4294967295\n      });\n      executeInstructions(renderGroup, this._renderer.renderPipes);\n      this._renderer.renderTarget.finishRenderPass();\n      this._renderer.renderTarget.pop();\n      this._renderer.globalUniforms.pop();\n    }\n    renderGroup._batchableRenderGroup._batcher.updateElement(renderGroup._batchableRenderGroup);\n    renderGroup._batchableRenderGroup._batcher.geometry.buffers[0].update();\n  }\n  _executeDirect(renderGroup) {\n    this._renderer.globalUniforms.push({\n      worldTransformMatrix: renderGroup.inverseParentTextureTransform,\n      worldColor: renderGroup.worldColorAlpha\n    });\n    executeInstructions(renderGroup, this._renderer.renderPipes);\n    this._renderer.globalUniforms.pop();\n  }\n}\nRenderGroupPipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"renderGroup\"\n};\nfunction clearList(list, index) {\n  index || (index = 0);\n  for (let j = index; j < list.length; j++) {\n    if (list[j]) {\n      list[j] = null;\n    } else {\n      break;\n    }\n  }\n}\nconst tempContainer = new Container();\nconst UPDATE_BLEND_COLOR_VISIBLE = UPDATE_VISIBLE | UPDATE_COLOR | UPDATE_BLEND;\nfunction updateRenderGroupTransforms(renderGroup, updateChildRenderGroups = false) {\n  updateRenderGroupTransform(renderGroup);\n  const childrenToUpdate = renderGroup.childrenToUpdate;\n  const updateTick = renderGroup.updateTick++;\n  for (const j in childrenToUpdate) {\n    const renderGroupDepth = Number(j);\n    const childrenAtDepth = childrenToUpdate[j];\n    const list = childrenAtDepth.list;\n    const index = childrenAtDepth.index;\n    for (let i = 0; i < index; i++) {\n      const child = list[i];\n      if (child.parentRenderGroup === renderGroup && child.relativeRenderGroupDepth === renderGroupDepth) {\n        updateTransformAndChildren(child, updateTick, 0);\n      }\n    }\n    clearList(list, index);\n    childrenAtDepth.index = 0;\n  }\n  if (updateChildRenderGroups) {\n    for (let i = 0; i < renderGroup.renderGroupChildren.length; i++) {\n      updateRenderGroupTransforms(renderGroup.renderGroupChildren[i], updateChildRenderGroups);\n    }\n  }\n}\nfunction updateRenderGroupTransform(renderGroup) {\n  const root = renderGroup.root;\n  let worldAlpha;\n  if (renderGroup.renderGroupParent) {\n    const renderGroupParent = renderGroup.renderGroupParent;\n    renderGroup.worldTransform.appendFrom(\n      root.relativeGroupTransform,\n      renderGroupParent.worldTransform\n    );\n    renderGroup.worldColor = multiplyColors(\n      root.groupColor,\n      renderGroupParent.worldColor\n    );\n    worldAlpha = root.groupAlpha * renderGroupParent.worldAlpha;\n  } else {\n    renderGroup.worldTransform.copyFrom(root.localTransform);\n    renderGroup.worldColor = root.localColor;\n    worldAlpha = root.localAlpha;\n  }\n  worldAlpha = worldAlpha < 0 ? 0 : worldAlpha > 1 ? 1 : worldAlpha;\n  renderGroup.worldAlpha = worldAlpha;\n  renderGroup.worldColorAlpha = renderGroup.worldColor + ((worldAlpha * 255 | 0) << 24);\n}\nfunction updateTransformAndChildren(container, updateTick, updateFlags) {\n  if (updateTick === container.updateTick)\n    return;\n  container.updateTick = updateTick;\n  container.didChange = false;\n  const localTransform = container.localTransform;\n  container.updateLocalTransform();\n  const parent = container.parent;\n  if (parent && !parent.renderGroup) {\n    updateFlags |= container._updateFlags;\n    container.relativeGroupTransform.appendFrom(\n      localTransform,\n      parent.relativeGroupTransform\n    );\n    if (updateFlags & UPDATE_BLEND_COLOR_VISIBLE) {\n      updateColorBlendVisibility(container, parent, updateFlags);\n    }\n  } else {\n    updateFlags = container._updateFlags;\n    container.relativeGroupTransform.copyFrom(localTransform);\n    if (updateFlags & UPDATE_BLEND_COLOR_VISIBLE) {\n      updateColorBlendVisibility(container, tempContainer, updateFlags);\n    }\n  }\n  if (!container.renderGroup) {\n    const children = container.children;\n    const length = children.length;\n    for (let i = 0; i < length; i++) {\n      updateTransformAndChildren(children[i], updateTick, updateFlags);\n    }\n    const renderGroup = container.parentRenderGroup;\n    const renderable = container;\n    if (renderable.renderPipeId && !renderGroup.structureDidChange) {\n      renderGroup.updateRenderable(renderable);\n    }\n  }\n}\nfunction updateColorBlendVisibility(container, parent, updateFlags) {\n  if (updateFlags & UPDATE_COLOR) {\n    container.groupColor = multiplyColors(\n      container.localColor,\n      parent.groupColor\n    );\n    let groupAlpha = container.localAlpha * parent.groupAlpha;\n    groupAlpha = groupAlpha < 0 ? 0 : groupAlpha > 1 ? 1 : groupAlpha;\n    container.groupAlpha = groupAlpha;\n    container.groupColorAlpha = container.groupColor + ((groupAlpha * 255 | 0) << 24);\n  }\n  if (updateFlags & UPDATE_BLEND) {\n    container.groupBlendMode = container.localBlendMode === \"inherit\" ? parent.groupBlendMode : container.localBlendMode;\n  }\n  if (updateFlags & UPDATE_VISIBLE) {\n    container.globalDisplayStatus = container.localDisplayStatus & parent.globalDisplayStatus;\n  }\n  container._updateFlags = 0;\n}\nfunction validateRenderables(renderGroup, renderPipes) {\n  const { list, index } = renderGroup.childrenRenderablesToUpdate;\n  let rebuildRequired = false;\n  for (let i = 0; i < index; i++) {\n    const container = list[i];\n    const renderable = container;\n    const pipe = renderPipes[renderable.renderPipeId];\n    rebuildRequired = pipe.validateRenderable(container);\n    if (rebuildRequired) {\n      break;\n    }\n  }\n  renderGroup.structureDidChange = rebuildRequired;\n  return rebuildRequired;\n}\nconst tempMatrix = new Matrix();\nclass RenderGroupSystem {\n  constructor(renderer) {\n    this._renderer = renderer;\n  }\n  render({ container, transform }) {\n    const parent = container.parent;\n    const renderGroupParent = container.renderGroup.renderGroupParent;\n    container.parent = null;\n    container.renderGroup.renderGroupParent = null;\n    const renderer = this._renderer;\n    let originalLocalTransform = tempMatrix;\n    if (transform) {\n      originalLocalTransform = originalLocalTransform.copyFrom(container.renderGroup.localTransform);\n      container.renderGroup.localTransform.copyFrom(transform);\n    }\n    const renderPipes = renderer.renderPipes;\n    this._updateCachedRenderGroups(container.renderGroup, null);\n    this._updateRenderGroups(container.renderGroup);\n    renderer.globalUniforms.start({\n      worldTransformMatrix: transform ? container.renderGroup.localTransform : container.renderGroup.worldTransform,\n      worldColor: container.renderGroup.worldColorAlpha\n    });\n    executeInstructions(container.renderGroup, renderPipes);\n    if (renderPipes.uniformBatch) {\n      renderPipes.uniformBatch.renderEnd();\n    }\n    if (transform) {\n      container.renderGroup.localTransform.copyFrom(originalLocalTransform);\n    }\n    container.parent = parent;\n    container.renderGroup.renderGroupParent = renderGroupParent;\n  }\n  destroy() {\n    this._renderer = null;\n  }\n  _updateCachedRenderGroups(renderGroup, closestCacheAsTexture) {\n    if (renderGroup.isCachedAsTexture) {\n      if (!renderGroup.updateCacheTexture)\n        return;\n      closestCacheAsTexture = renderGroup;\n    }\n    renderGroup._parentCacheAsTextureRenderGroup = closestCacheAsTexture;\n    for (let i = renderGroup.renderGroupChildren.length - 1; i >= 0; i--) {\n      this._updateCachedRenderGroups(renderGroup.renderGroupChildren[i], closestCacheAsTexture);\n    }\n    renderGroup.invalidateMatrices();\n    if (renderGroup.isCachedAsTexture) {\n      if (renderGroup.textureNeedsUpdate) {\n        const bounds = renderGroup.root.getLocalBounds();\n        bounds.ceil();\n        const lastTexture = renderGroup.texture;\n        if (renderGroup.texture) {\n          TexturePool.returnTexture(renderGroup.texture);\n        }\n        const renderer = this._renderer;\n        const resolution = renderGroup.textureOptions.resolution || renderer.view.resolution;\n        const antialias = renderGroup.textureOptions.antialias ?? renderer.view.antialias;\n        renderGroup.texture = TexturePool.getOptimalTexture(\n          bounds.width,\n          bounds.height,\n          resolution,\n          antialias\n        );\n        renderGroup._textureBounds || (renderGroup._textureBounds = new Bounds());\n        renderGroup._textureBounds.copyFrom(bounds);\n        if (lastTexture !== renderGroup.texture) {\n          if (renderGroup.renderGroupParent) {\n            renderGroup.renderGroupParent.structureDidChange = true;\n          }\n        }\n      }\n    } else if (renderGroup.texture) {\n      TexturePool.returnTexture(renderGroup.texture);\n      renderGroup.texture = null;\n    }\n  }\n  _updateRenderGroups(renderGroup) {\n    const renderer = this._renderer;\n    const renderPipes = renderer.renderPipes;\n    renderGroup.runOnRender(renderer);\n    renderGroup.instructionSet.renderPipes = renderPipes;\n    if (!renderGroup.structureDidChange) {\n      validateRenderables(renderGroup, renderPipes);\n    } else {\n      clearList(renderGroup.childrenRenderablesToUpdate.list, 0);\n    }\n    updateRenderGroupTransforms(renderGroup);\n    if (renderGroup.structureDidChange) {\n      renderGroup.structureDidChange = false;\n      this._buildInstructions(renderGroup, renderer);\n    } else {\n      this._updateRenderables(renderGroup);\n    }\n    renderGroup.childrenRenderablesToUpdate.index = 0;\n    renderer.renderPipes.batch.upload(renderGroup.instructionSet);\n    if (renderGroup.isCachedAsTexture && !renderGroup.textureNeedsUpdate)\n      return;\n    for (let i = 0; i < renderGroup.renderGroupChildren.length; i++) {\n      this._updateRenderGroups(renderGroup.renderGroupChildren[i]);\n    }\n  }\n  _updateRenderables(renderGroup) {\n    const { list, index } = renderGroup.childrenRenderablesToUpdate;\n    for (let i = 0; i < index; i++) {\n      const container = list[i];\n      if (container.didViewUpdate) {\n        renderGroup.updateRenderable(container);\n      }\n    }\n    clearList(list, index);\n  }\n  _buildInstructions(renderGroup, rendererOrPipes) {\n    const root = renderGroup.root;\n    const instructionSet = renderGroup.instructionSet;\n    instructionSet.reset();\n    const renderer = rendererOrPipes.renderPipes ? rendererOrPipes : rendererOrPipes.batch.renderer;\n    const renderPipes = renderer.renderPipes;\n    renderPipes.batch.buildStart(instructionSet);\n    renderPipes.blendMode.buildStart();\n    renderPipes.colorMask.buildStart();\n    if (root.sortableChildren) {\n      root.sortChildren();\n    }\n    root.collectRenderablesWithEffects(instructionSet, renderer, null);\n    renderPipes.batch.buildEnd(instructionSet);\n    renderPipes.blendMode.buildEnd(instructionSet);\n  }\n}\nRenderGroupSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem,\n    ExtensionType.CanvasSystem\n  ],\n  name: \"renderGroup\"\n};\nclass SpritePipe {\n  constructor(renderer) {\n    this._gpuSpriteHash = /* @__PURE__ */ Object.create(null);\n    this._destroyRenderableBound = this.destroyRenderable.bind(this);\n    this._renderer = renderer;\n    this._renderer.renderableGC.addManagedHash(this, \"_gpuSpriteHash\");\n  }\n  addRenderable(sprite, instructionSet) {\n    const gpuSprite = this._getGpuSprite(sprite);\n    if (sprite.didViewUpdate)\n      this._updateBatchableSprite(sprite, gpuSprite);\n    this._renderer.renderPipes.batch.addToBatch(gpuSprite, instructionSet);\n  }\n  updateRenderable(sprite) {\n    const gpuSprite = this._gpuSpriteHash[sprite.uid];\n    if (sprite.didViewUpdate)\n      this._updateBatchableSprite(sprite, gpuSprite);\n    gpuSprite._batcher.updateElement(gpuSprite);\n  }\n  validateRenderable(sprite) {\n    const gpuSprite = this._getGpuSprite(sprite);\n    return !gpuSprite._batcher.checkAndUpdateTexture(\n      gpuSprite,\n      sprite._texture\n    );\n  }\n  destroyRenderable(sprite) {\n    const batchableSprite = this._gpuSpriteHash[sprite.uid];\n    BigPool.return(batchableSprite);\n    this._gpuSpriteHash[sprite.uid] = null;\n    sprite.off(\"destroyed\", this._destroyRenderableBound);\n  }\n  _updateBatchableSprite(sprite, batchableSprite) {\n    batchableSprite.bounds = sprite.visualBounds;\n    batchableSprite.texture = sprite._texture;\n  }\n  _getGpuSprite(sprite) {\n    return this._gpuSpriteHash[sprite.uid] || this._initGPUSprite(sprite);\n  }\n  _initGPUSprite(sprite) {\n    const batchableSprite = BigPool.get(BatchableSprite);\n    batchableSprite.renderable = sprite;\n    batchableSprite.transform = sprite.groupTransform;\n    batchableSprite.texture = sprite._texture;\n    batchableSprite.bounds = sprite.visualBounds;\n    batchableSprite.roundPixels = this._renderer._roundPixels | sprite._roundPixels;\n    this._gpuSpriteHash[sprite.uid] = batchableSprite;\n    sprite.on(\"destroyed\", this._destroyRenderableBound);\n    return batchableSprite;\n  }\n  destroy() {\n    for (const i in this._gpuSpriteHash) {\n      BigPool.return(this._gpuSpriteHash[i]);\n    }\n    this._gpuSpriteHash = null;\n    this._renderer = null;\n  }\n}\nSpritePipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"sprite\"\n};\nconst _BackgroundSystem = class _BackgroundSystem2 {\n  constructor() {\n    this.clearBeforeRender = true;\n    this._backgroundColor = new Color(0);\n    this.color = this._backgroundColor;\n    this.alpha = 1;\n  }\n  /**\n   * initiates the background system\n   * @param options - the options for the background colors\n   */\n  init(options) {\n    options = { ..._BackgroundSystem2.defaultOptions, ...options };\n    this.clearBeforeRender = options.clearBeforeRender;\n    this.color = options.background || options.backgroundColor || this._backgroundColor;\n    this.alpha = options.backgroundAlpha;\n    this._backgroundColor.setAlpha(options.backgroundAlpha);\n  }\n  /** The background color to fill if not transparent */\n  get color() {\n    return this._backgroundColor;\n  }\n  set color(value) {\n    this._backgroundColor.setValue(value);\n  }\n  /** The background color alpha. Setting this to 0 will make the canvas transparent. */\n  get alpha() {\n    return this._backgroundColor.alpha;\n  }\n  set alpha(value) {\n    this._backgroundColor.setAlpha(value);\n  }\n  /** The background color as an [R, G, B, A] array. */\n  get colorRgba() {\n    return this._backgroundColor.toArray();\n  }\n  /**\n   * destroys the background system\n   * @internal\n   * @ignore\n   */\n  destroy() {\n  }\n};\n_BackgroundSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem,\n    ExtensionType.CanvasSystem\n  ],\n  name: \"background\",\n  priority: 0\n};\n_BackgroundSystem.defaultOptions = {\n  /**\n   * {@link WebGLOptions.backgroundAlpha}\n   * @default 1\n   */\n  backgroundAlpha: 1,\n  /**\n   * {@link WebGLOptions.backgroundColor}\n   * @default 0x000000\n   */\n  backgroundColor: 0,\n  /**\n   * {@link WebGLOptions.clearBeforeRender}\n   * @default true\n   */\n  clearBeforeRender: true\n};\nlet BackgroundSystem = _BackgroundSystem;\nconst BLEND_MODE_FILTERS = {};\nextensions.handle(ExtensionType.BlendMode, (value) => {\n  if (!value.name) {\n    throw new Error(\"BlendMode extension must have a name property\");\n  }\n  BLEND_MODE_FILTERS[value.name] = value.ref;\n}, (value) => {\n  delete BLEND_MODE_FILTERS[value.name];\n});\nclass BlendModePipe {\n  constructor(renderer) {\n    this._isAdvanced = false;\n    this._filterHash = /* @__PURE__ */ Object.create(null);\n    this._renderer = renderer;\n    this._renderer.runners.prerender.add(this);\n  }\n  prerender() {\n    this._activeBlendMode = \"normal\";\n    this._isAdvanced = false;\n  }\n  /**\n   * This ensures that a blendMode switch is added to the instruction set if the blend mode has changed.\n   * @param renderable - The renderable we are adding to the instruction set\n   * @param blendMode - The blend mode of the renderable\n   * @param instructionSet - The instruction set we are adding to\n   */\n  setBlendMode(renderable, blendMode, instructionSet) {\n    if (this._activeBlendMode === blendMode) {\n      if (this._isAdvanced)\n        this._renderableList.push(renderable);\n      return;\n    }\n    this._activeBlendMode = blendMode;\n    if (this._isAdvanced) {\n      this._endAdvancedBlendMode(instructionSet);\n    }\n    this._isAdvanced = !!BLEND_MODE_FILTERS[blendMode];\n    if (this._isAdvanced) {\n      this._beginAdvancedBlendMode(instructionSet);\n      this._renderableList.push(renderable);\n    }\n  }\n  _beginAdvancedBlendMode(instructionSet) {\n    this._renderer.renderPipes.batch.break(instructionSet);\n    const blendMode = this._activeBlendMode;\n    if (!BLEND_MODE_FILTERS[blendMode]) {\n      warn(`Unable to assign BlendMode: '${blendMode}'. You may want to include: import 'pixi.js/advanced-blend-modes'`);\n      return;\n    }\n    let filterEffect = this._filterHash[blendMode];\n    if (!filterEffect) {\n      filterEffect = this._filterHash[blendMode] = new FilterEffect();\n      filterEffect.filters = [new BLEND_MODE_FILTERS[blendMode]()];\n    }\n    const instruction = {\n      renderPipeId: \"filter\",\n      action: \"pushFilter\",\n      renderables: [],\n      filterEffect,\n      canBundle: false\n    };\n    this._renderableList = instruction.renderables;\n    instructionSet.add(instruction);\n  }\n  _endAdvancedBlendMode(instructionSet) {\n    this._renderableList = null;\n    this._renderer.renderPipes.batch.break(instructionSet);\n    instructionSet.add({\n      renderPipeId: \"filter\",\n      action: \"popFilter\",\n      canBundle: false\n    });\n  }\n  /**\n   * called when the instruction build process is starting this will reset internally to the default blend mode\n   * @internal\n   * @ignore\n   */\n  buildStart() {\n    this._isAdvanced = false;\n  }\n  /**\n   * called when the instruction build process is finished, ensuring that if there is an advanced blend mode\n   * active, we add the final render instructions added to the instruction set\n   * @param instructionSet - The instruction set we are adding to\n   * @internal\n   * @ignore\n   */\n  buildEnd(instructionSet) {\n    if (this._isAdvanced) {\n      this._endAdvancedBlendMode(instructionSet);\n    }\n  }\n  /**\n   * @internal\n   * @ignore\n   */\n  destroy() {\n    this._renderer = null;\n    this._renderableList = null;\n    for (const i in this._filterHash) {\n      this._filterHash[i].destroy();\n    }\n    this._filterHash = null;\n  }\n}\nBlendModePipe.extension = {\n  type: [\n    ExtensionType.WebGLPipes,\n    ExtensionType.WebGPUPipes,\n    ExtensionType.CanvasPipes\n  ],\n  name: \"blendMode\"\n};\nconst imageTypes = {\n  png: \"image/png\",\n  jpg: \"image/jpeg\",\n  webp: \"image/webp\"\n};\nconst _ExtractSystem = class _ExtractSystem2 {\n  /** @param renderer - The renderer this System works for. */\n  constructor(renderer) {\n    this._renderer = renderer;\n  }\n  _normalizeOptions(options, defaults = {}) {\n    if (options instanceof Container || options instanceof Texture) {\n      return {\n        target: options,\n        ...defaults\n      };\n    }\n    return {\n      ...defaults,\n      ...options\n    };\n  }\n  /**\n   * Will return a HTML Image of the target\n   * @param options - The options for creating the image, or the target to extract\n   * @returns - HTML Image of the target\n   */\n  async image(options) {\n    const image = new Image();\n    image.src = await this.base64(options);\n    return image;\n  }\n  /**\n   * Will return a base64 encoded string of this target. It works by calling\n   * `Extract.canvas` and then running toDataURL on that.\n   * @param options - The options for creating the image, or the target to extract\n   */\n  async base64(options) {\n    options = this._normalizeOptions(\n      options,\n      _ExtractSystem2.defaultImageOptions\n    );\n    const { format, quality } = options;\n    const canvas = this.canvas(options);\n    if (canvas.toBlob !== void 0) {\n      return new Promise((resolve, reject) => {\n        canvas.toBlob((blob) => {\n          if (!blob) {\n            reject(new Error(\"ICanvas.toBlob failed!\"));\n            return;\n          }\n          const reader = new FileReader();\n          reader.onload = () => resolve(reader.result);\n          reader.onerror = reject;\n          reader.readAsDataURL(blob);\n        }, imageTypes[format], quality);\n      });\n    }\n    if (canvas.toDataURL !== void 0) {\n      return canvas.toDataURL(imageTypes[format], quality);\n    }\n    if (canvas.convertToBlob !== void 0) {\n      const blob = await canvas.convertToBlob({ type: imageTypes[format], quality });\n      return new Promise((resolve, reject) => {\n        const reader = new FileReader();\n        reader.onload = () => resolve(reader.result);\n        reader.onerror = reject;\n        reader.readAsDataURL(blob);\n      });\n    }\n    throw new Error(\"Extract.base64() requires ICanvas.toDataURL, ICanvas.toBlob, or ICanvas.convertToBlob to be implemented\");\n  }\n  /**\n   * Creates a Canvas element, renders this target to it and then returns it.\n   * @param options - The options for creating the canvas, or the target to extract\n   * @returns - A Canvas element with the texture rendered on.\n   */\n  canvas(options) {\n    options = this._normalizeOptions(options);\n    const target = options.target;\n    const renderer = this._renderer;\n    if (target instanceof Texture) {\n      return renderer.texture.generateCanvas(target);\n    }\n    const texture = renderer.textureGenerator.generateTexture(options);\n    const canvas = renderer.texture.generateCanvas(texture);\n    texture.destroy(true);\n    return canvas;\n  }\n  /**\n   * Will return a one-dimensional array containing the pixel data of the entire texture in RGBA\n   * order, with integer values between 0 and 255 (included).\n   * @param options - The options for extracting the image, or the target to extract\n   * @returns - One-dimensional array containing the pixel data of the entire texture\n   */\n  pixels(options) {\n    options = this._normalizeOptions(options);\n    const target = options.target;\n    const renderer = this._renderer;\n    const texture = target instanceof Texture ? target : renderer.textureGenerator.generateTexture(options);\n    const pixelInfo = renderer.texture.getPixels(texture);\n    if (target instanceof Container) {\n      texture.destroy(true);\n    }\n    return pixelInfo;\n  }\n  /**\n   * Will return a texture of the target\n   * @param options - The options for creating the texture, or the target to extract\n   * @returns - A texture of the target\n   */\n  texture(options) {\n    options = this._normalizeOptions(options);\n    if (options.target instanceof Texture)\n      return options.target;\n    return this._renderer.textureGenerator.generateTexture(options);\n  }\n  /**\n   * Will extract a HTMLImage of the target and download it\n   * @param options - The options for downloading and extracting the image, or the target to extract\n   */\n  download(options) {\n    options = this._normalizeOptions(options);\n    const canvas = this.canvas(options);\n    const link = document.createElement(\"a\");\n    link.download = options.filename ?? \"image.png\";\n    link.href = canvas.toDataURL(\"image/png\");\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  /**\n   * Logs the target to the console as an image. This is a useful way to debug what's happening in the renderer.\n   * @param options - The options for logging the image, or the target to log\n   */\n  log(options) {\n    const width = options.width ?? 200;\n    options = this._normalizeOptions(options);\n    const canvas = this.canvas(options);\n    const base64 = canvas.toDataURL();\n    console.log(`[Pixi Texture] ${canvas.width}px ${canvas.height}px`);\n    const style = [\n      \"font-size: 1px;\",\n      `padding: ${width}px ${300}px;`,\n      `background: url(${base64}) no-repeat;`,\n      \"background-size: contain;\"\n    ].join(\" \");\n    console.log(\"%c \", style);\n  }\n  destroy() {\n    this._renderer = null;\n  }\n};\n_ExtractSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"extract\"\n};\n_ExtractSystem.defaultImageOptions = {\n  /** The format of the image. */\n  format: \"png\",\n  /** The quality of the image. */\n  quality: 1\n};\nlet ExtractSystem = _ExtractSystem;\nconst tempRect = new Rectangle();\nconst tempBounds = new Bounds();\nconst noColor = [0, 0, 0, 0];\nclass GenerateTextureSystem {\n  constructor(renderer) {\n    this._renderer = renderer;\n  }\n  /**\n   * A Useful function that returns a texture of the display object that can then be used to create sprites\n   * This can be quite useful if your container is complicated and needs to be reused multiple times.\n   * @param {GenerateTextureOptions | Container} options - Generate texture options.\n   * @param {Container} [options.container] - If not given, the renderer's resolution is used.\n   * @param {Rectangle} options.region - The region of the container, that shall be rendered,\n   * @param {number} [options.resolution] - The resolution of the texture being generated.\n   *        if no region is specified, defaults to the local bounds of the container.\n   * @param {GenerateTextureSourceOptions} [options.textureSourceOptions] - Texture options for GPU.\n   * @returns a shiny new texture of the container passed in\n   */\n  generateTexture(options) {\n    if (options instanceof Container) {\n      options = {\n        target: options,\n        frame: void 0,\n        textureSourceOptions: {},\n        resolution: void 0\n      };\n    }\n    const resolution = options.resolution || this._renderer.resolution;\n    const antialias = options.antialias || this._renderer.view.antialias;\n    const container = options.target;\n    let clearColor = options.clearColor;\n    if (clearColor) {\n      const isRGBAArray = Array.isArray(clearColor) && clearColor.length === 4;\n      clearColor = isRGBAArray ? clearColor : Color.shared.setValue(clearColor).toArray();\n    } else {\n      clearColor = noColor;\n    }\n    const region = options.frame?.copyTo(tempRect) || getLocalBounds(container, tempBounds).rectangle;\n    region.width = Math.max(region.width, 1 / resolution) | 0;\n    region.height = Math.max(region.height, 1 / resolution) | 0;\n    const target = RenderTexture.create({\n      ...options.textureSourceOptions,\n      width: region.width,\n      height: region.height,\n      resolution,\n      antialias\n    });\n    const transform = Matrix.shared.translate(-region.x, -region.y);\n    this._renderer.render({\n      container,\n      transform,\n      target,\n      clearColor\n    });\n    target.source.updateMipmaps();\n    return target;\n  }\n  destroy() {\n    this._renderer = null;\n  }\n}\nGenerateTextureSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"textureGenerator\"\n};\nclass GlobalUniformSystem {\n  constructor(renderer) {\n    this._stackIndex = 0;\n    this._globalUniformDataStack = [];\n    this._uniformsPool = [];\n    this._activeUniforms = [];\n    this._bindGroupPool = [];\n    this._activeBindGroups = [];\n    this._renderer = renderer;\n  }\n  reset() {\n    this._stackIndex = 0;\n    for (let i = 0; i < this._activeUniforms.length; i++) {\n      this._uniformsPool.push(this._activeUniforms[i]);\n    }\n    for (let i = 0; i < this._activeBindGroups.length; i++) {\n      this._bindGroupPool.push(this._activeBindGroups[i]);\n    }\n    this._activeUniforms.length = 0;\n    this._activeBindGroups.length = 0;\n  }\n  start(options) {\n    this.reset();\n    this.push(options);\n  }\n  bind({\n    size,\n    projectionMatrix,\n    worldTransformMatrix,\n    worldColor,\n    offset\n  }) {\n    const renderTarget = this._renderer.renderTarget.renderTarget;\n    const currentGlobalUniformData = this._stackIndex ? this._globalUniformDataStack[this._stackIndex - 1] : {\n      projectionData: renderTarget,\n      worldTransformMatrix: new Matrix(),\n      worldColor: 4294967295,\n      offset: new Point()\n    };\n    const globalUniformData = {\n      projectionMatrix: projectionMatrix || this._renderer.renderTarget.projectionMatrix,\n      resolution: size || renderTarget.size,\n      worldTransformMatrix: worldTransformMatrix || currentGlobalUniformData.worldTransformMatrix,\n      worldColor: worldColor || currentGlobalUniformData.worldColor,\n      offset: offset || currentGlobalUniformData.offset,\n      bindGroup: null\n    };\n    const uniformGroup = this._uniformsPool.pop() || this._createUniforms();\n    this._activeUniforms.push(uniformGroup);\n    const uniforms = uniformGroup.uniforms;\n    uniforms.uProjectionMatrix = globalUniformData.projectionMatrix;\n    uniforms.uResolution = globalUniformData.resolution;\n    uniforms.uWorldTransformMatrix.copyFrom(globalUniformData.worldTransformMatrix);\n    uniforms.uWorldTransformMatrix.tx -= globalUniformData.offset.x;\n    uniforms.uWorldTransformMatrix.ty -= globalUniformData.offset.y;\n    color32BitToUniform(\n      globalUniformData.worldColor,\n      uniforms.uWorldColorAlpha,\n      0\n    );\n    uniformGroup.update();\n    let bindGroup;\n    if (this._renderer.renderPipes.uniformBatch) {\n      bindGroup = this._renderer.renderPipes.uniformBatch.getUniformBindGroup(uniformGroup, false);\n    } else {\n      bindGroup = this._bindGroupPool.pop() || new BindGroup();\n      this._activeBindGroups.push(bindGroup);\n      bindGroup.setResource(uniformGroup, 0);\n    }\n    globalUniformData.bindGroup = bindGroup;\n    this._currentGlobalUniformData = globalUniformData;\n  }\n  push(options) {\n    this.bind(options);\n    this._globalUniformDataStack[this._stackIndex++] = this._currentGlobalUniformData;\n  }\n  pop() {\n    this._currentGlobalUniformData = this._globalUniformDataStack[--this._stackIndex - 1];\n    if (this._renderer.type === RendererType.WEBGL) {\n      this._currentGlobalUniformData.bindGroup.resources[0].update();\n    }\n  }\n  get bindGroup() {\n    return this._currentGlobalUniformData.bindGroup;\n  }\n  get globalUniformData() {\n    return this._currentGlobalUniformData;\n  }\n  get uniformGroup() {\n    return this._currentGlobalUniformData.bindGroup.resources[0];\n  }\n  _createUniforms() {\n    const globalUniforms = new UniformGroup({\n      uProjectionMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      uWorldTransformMatrix: { value: new Matrix(), type: \"mat3x3<f32>\" },\n      // TODO - someone smart - set this to be a unorm8x4 rather than a vec4<f32>\n      uWorldColorAlpha: { value: new Float32Array(4), type: \"vec4<f32>\" },\n      uResolution: { value: [0, 0], type: \"vec2<f32>\" }\n    }, {\n      isStatic: true\n    });\n    return globalUniforms;\n  }\n  destroy() {\n    this._renderer = null;\n  }\n}\nGlobalUniformSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem,\n    ExtensionType.CanvasSystem\n  ],\n  name: \"globalUniforms\"\n};\nlet uid = 1;\nclass SchedulerSystem {\n  constructor() {\n    this._tasks = [];\n    this._offset = 0;\n  }\n  /** Initializes the scheduler system and starts the ticker. */\n  init() {\n    Ticker.system.add(this._update, this);\n  }\n  /**\n   * Schedules a repeating task.\n   * @param func - The function to execute.\n   * @param duration - The interval duration in milliseconds.\n   * @param useOffset - this will spread out tasks so that they do not all run at the same time\n   * @returns The unique identifier for the scheduled task.\n   */\n  repeat(func, duration, useOffset = true) {\n    const id = uid++;\n    let offset = 0;\n    if (useOffset) {\n      this._offset += 1e3;\n      offset = this._offset;\n    }\n    this._tasks.push({\n      func,\n      duration,\n      start: performance.now(),\n      offset,\n      last: performance.now(),\n      repeat: true,\n      id\n    });\n    return id;\n  }\n  /**\n   * Cancels a scheduled task.\n   * @param id - The unique identifier of the task to cancel.\n   */\n  cancel(id) {\n    for (let i = 0; i < this._tasks.length; i++) {\n      if (this._tasks[i].id === id) {\n        this._tasks.splice(i, 1);\n        return;\n      }\n    }\n  }\n  /**\n   * Updates and executes the scheduled tasks.\n   * @private\n   */\n  _update() {\n    const now = performance.now();\n    for (let i = 0; i < this._tasks.length; i++) {\n      const task = this._tasks[i];\n      if (now - task.offset - task.last >= task.duration) {\n        const elapsed = now - task.start;\n        task.func(elapsed);\n        task.last = now;\n      }\n    }\n  }\n  /**\n   * Destroys the scheduler system and removes all tasks.\n   * @internal\n   * @ignore\n   */\n  destroy() {\n    Ticker.system.remove(this._update, this);\n    this._tasks.length = 0;\n  }\n}\nSchedulerSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem,\n    ExtensionType.CanvasSystem\n  ],\n  name: \"scheduler\",\n  priority: 0\n};\nlet saidHello = false;\nfunction sayHello(type) {\n  if (saidHello) {\n    return;\n  }\n  if (DOMAdapter.get().getNavigator().userAgent.toLowerCase().indexOf(\"chrome\") > -1) {\n    const args = [\n      `%c  %c  %c  %c  %c PixiJS %c v${VERSION} (${type}) http://www.pixijs.com/\n\n`,\n      \"background: #E72264; padding:5px 0;\",\n      \"background: #6CA2EA; padding:5px 0;\",\n      \"background: #B5D33D; padding:5px 0;\",\n      \"background: #FED23F; padding:5px 0;\",\n      \"color: #FFFFFF; background: #E72264; padding:5px 0;\",\n      \"color: #E72264; background: #FFFFFF; padding:5px 0;\"\n    ];\n    globalThis.console.log(...args);\n  } else if (globalThis.console) {\n    globalThis.console.log(`PixiJS ${VERSION} - ${type} - http://www.pixijs.com/`);\n  }\n  saidHello = true;\n}\nclass HelloSystem {\n  constructor(renderer) {\n    this._renderer = renderer;\n  }\n  /**\n   * It all starts here! This initiates every system, passing in the options for any system by name.\n   * @param options - the config for the renderer and all its systems\n   */\n  init(options) {\n    if (options.hello) {\n      let name = this._renderer.name;\n      if (this._renderer.type === RendererType.WEBGL) {\n        name += ` ${this._renderer.context.webGLVersion}`;\n      }\n      sayHello(name);\n    }\n  }\n}\nHelloSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem,\n    ExtensionType.CanvasSystem\n  ],\n  name: \"hello\",\n  priority: -2\n};\nHelloSystem.defaultOptions = {\n  /** {@link WebGLOptions.hello} */\n  hello: false\n};\nfunction cleanHash(hash) {\n  let clean = false;\n  for (const i in hash) {\n    if (hash[i] == void 0) {\n      clean = true;\n      break;\n    }\n  }\n  if (!clean)\n    return hash;\n  const cleanHash2 = /* @__PURE__ */ Object.create(null);\n  for (const i in hash) {\n    const value = hash[i];\n    if (value) {\n      cleanHash2[i] = value;\n    }\n  }\n  return cleanHash2;\n}\nfunction cleanArray(arr) {\n  let offset = 0;\n  for (let i = 0; i < arr.length; i++) {\n    if (arr[i] == void 0) {\n      offset++;\n    } else {\n      arr[i - offset] = arr[i];\n    }\n  }\n  arr.length -= offset;\n  return arr;\n}\nlet renderableGCTick = 0;\nconst _RenderableGCSystem = class _RenderableGCSystem2 {\n  /**\n   * Creates a new RenderableGCSystem instance.\n   * @param renderer - The renderer this garbage collection system works for\n   */\n  constructor(renderer) {\n    this._managedRenderables = [];\n    this._managedHashes = [];\n    this._managedArrays = [];\n    this._renderer = renderer;\n  }\n  /**\n   * Initializes the garbage collection system with the provided options.\n   * @param options - Configuration options for the renderer\n   */\n  init(options) {\n    options = { ..._RenderableGCSystem2.defaultOptions, ...options };\n    this.maxUnusedTime = options.renderableGCMaxUnusedTime;\n    this._frequency = options.renderableGCFrequency;\n    this.enabled = options.renderableGCActive;\n  }\n  /**\n   * Gets whether the garbage collection system is currently enabled.\n   * @returns True if GC is enabled, false otherwise\n   */\n  get enabled() {\n    return !!this._handler;\n  }\n  /**\n   * Enables or disables the garbage collection system.\n   * When enabled, schedules periodic cleanup of resources.\n   * When disabled, cancels all scheduled cleanups.\n   */\n  set enabled(value) {\n    if (this.enabled === value)\n      return;\n    if (value) {\n      this._handler = this._renderer.scheduler.repeat(\n        () => this.run(),\n        this._frequency,\n        false\n      );\n      this._hashHandler = this._renderer.scheduler.repeat(\n        () => {\n          for (const hash of this._managedHashes) {\n            hash.context[hash.hash] = cleanHash(hash.context[hash.hash]);\n          }\n        },\n        this._frequency\n      );\n      this._arrayHandler = this._renderer.scheduler.repeat(\n        () => {\n          for (const array of this._managedArrays) {\n            cleanArray(array.context[array.hash]);\n          }\n        },\n        this._frequency\n      );\n    } else {\n      this._renderer.scheduler.cancel(this._handler);\n      this._renderer.scheduler.cancel(this._hashHandler);\n      this._renderer.scheduler.cancel(this._arrayHandler);\n    }\n  }\n  /**\n   * Adds a hash table to be managed by the garbage collector.\n   * @param context - The object containing the hash table\n   * @param hash - The property name of the hash table\n   */\n  addManagedHash(context, hash) {\n    this._managedHashes.push({ context, hash });\n  }\n  /**\n   * Adds an array to be managed by the garbage collector.\n   * @param context - The object containing the array\n   * @param hash - The property name of the array\n   */\n  addManagedArray(context, hash) {\n    this._managedArrays.push({ context, hash });\n  }\n  /**\n   * Updates the GC timestamp and tracking before rendering.\n   * @param options - The render options\n   * @param options.container - The container to render\n   */\n  prerender({\n    container\n  }) {\n    this._now = performance.now();\n    container.renderGroup.gcTick = renderableGCTick++;\n    this._updateInstructionGCTick(container.renderGroup, container.renderGroup.gcTick);\n  }\n  /**\n   * Starts tracking a renderable for garbage collection.\n   * @param renderable - The renderable to track\n   */\n  addRenderable(renderable) {\n    if (!this.enabled)\n      return;\n    if (renderable._lastUsed === -1) {\n      this._managedRenderables.push(renderable);\n      renderable.once(\"destroyed\", this._removeRenderable, this);\n    }\n    renderable._lastUsed = this._now;\n  }\n  /**\n   * Performs garbage collection by cleaning up unused renderables.\n   * Removes renderables that haven't been used for longer than maxUnusedTime.\n   */\n  run() {\n    const now = this._now;\n    const managedRenderables = this._managedRenderables;\n    const renderPipes = this._renderer.renderPipes;\n    let offset = 0;\n    for (let i = 0; i < managedRenderables.length; i++) {\n      const renderable = managedRenderables[i];\n      if (renderable === null) {\n        offset++;\n        continue;\n      }\n      const renderGroup = renderable.renderGroup ?? renderable.parentRenderGroup;\n      const currentTick = renderGroup?.instructionSet?.gcTick ?? -1;\n      if ((renderGroup?.gcTick ?? 0) === currentTick) {\n        renderable._lastUsed = now;\n      }\n      if (now - renderable._lastUsed > this.maxUnusedTime) {\n        if (!renderable.destroyed) {\n          const rp = renderPipes;\n          if (renderGroup)\n            renderGroup.structureDidChange = true;\n          rp[renderable.renderPipeId].destroyRenderable(renderable);\n        }\n        renderable._lastUsed = -1;\n        offset++;\n        renderable.off(\"destroyed\", this._removeRenderable, this);\n      } else {\n        managedRenderables[i - offset] = renderable;\n      }\n    }\n    managedRenderables.length -= offset;\n  }\n  /** Cleans up the garbage collection system. Disables GC and removes all tracked resources. */\n  destroy() {\n    this.enabled = false;\n    this._renderer = null;\n    this._managedRenderables.length = 0;\n    this._managedHashes.length = 0;\n    this._managedArrays.length = 0;\n  }\n  /**\n   * Removes a renderable from being tracked when it's destroyed.\n   * @param renderable - The renderable to stop tracking\n   */\n  _removeRenderable(renderable) {\n    const index = this._managedRenderables.indexOf(renderable);\n    if (index >= 0) {\n      renderable.off(\"destroyed\", this._removeRenderable, this);\n      this._managedRenderables[index] = null;\n    }\n  }\n  /**\n   * Updates the GC tick counter for a render group and its children.\n   * @param renderGroup - The render group to update\n   * @param gcTick - The new tick value\n   */\n  _updateInstructionGCTick(renderGroup, gcTick) {\n    renderGroup.instructionSet.gcTick = gcTick;\n    for (const child of renderGroup.renderGroupChildren) {\n      this._updateInstructionGCTick(child, gcTick);\n    }\n  }\n};\n_RenderableGCSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"renderableGC\",\n  priority: 0\n};\n_RenderableGCSystem.defaultOptions = {\n  /** Enable/disable the garbage collector */\n  renderableGCActive: true,\n  /** Time in ms before an unused resource is collected (default 1 minute) */\n  renderableGCMaxUnusedTime: 6e4,\n  /** How often to run garbage collection in ms (default 30 seconds) */\n  renderableGCFrequency: 3e4\n};\nlet RenderableGCSystem = _RenderableGCSystem;\nconst _TextureGCSystem = class _TextureGCSystem2 {\n  /** @param renderer - The renderer this System works for. */\n  constructor(renderer) {\n    this._renderer = renderer;\n    this.count = 0;\n    this.checkCount = 0;\n  }\n  init(options) {\n    options = { ..._TextureGCSystem2.defaultOptions, ...options };\n    this.checkCountMax = options.textureGCCheckCountMax;\n    this.maxIdle = options.textureGCAMaxIdle ?? options.textureGCMaxIdle;\n    this.active = options.textureGCActive;\n  }\n  /**\n   * Checks to see when the last time a texture was used.\n   * If the texture has not been used for a specified amount of time, it will be removed from the GPU.\n   */\n  postrender() {\n    if (!this._renderer.renderingToScreen) {\n      return;\n    }\n    this.count++;\n    if (!this.active)\n      return;\n    this.checkCount++;\n    if (this.checkCount > this.checkCountMax) {\n      this.checkCount = 0;\n      this.run();\n    }\n  }\n  /**\n   * Checks to see when the last time a texture was used.\n   * If the texture has not been used for a specified amount of time, it will be removed from the GPU.\n   */\n  run() {\n    const managedTextures = this._renderer.texture.managedTextures;\n    for (let i = 0; i < managedTextures.length; i++) {\n      const texture = managedTextures[i];\n      if (texture.autoGarbageCollect && texture.resource && texture._touched > -1 && this.count - texture._touched > this.maxIdle) {\n        texture._touched = -1;\n        texture.unload();\n      }\n    }\n  }\n  destroy() {\n    this._renderer = null;\n  }\n};\n_TextureGCSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem\n  ],\n  name: \"textureGC\"\n};\n_TextureGCSystem.defaultOptions = {\n  /**\n   * If set to true, this will enable the garbage collector on the GPU.\n   * @default true\n   */\n  textureGCActive: true,\n  /**\n   * @deprecated since 8.3.0\n   * @see {@link TextureGCSystem.textureGCMaxIdle}\n   */\n  textureGCAMaxIdle: null,\n  /**\n   * The maximum idle frames before a texture is destroyed by garbage collection.\n   * @default 60 * 60\n   */\n  textureGCMaxIdle: 60 * 60,\n  /**\n   * Frames between two garbage collections.\n   * @default 600\n   */\n  textureGCCheckCountMax: 600\n};\nlet TextureGCSystem = _TextureGCSystem;\nconst _ViewSystem = class _ViewSystem2 {\n  /**\n   * Whether CSS dimensions of canvas view should be resized to screen dimensions automatically.\n   * @member {boolean}\n   */\n  get autoDensity() {\n    return this.texture.source.autoDensity;\n  }\n  set autoDensity(value) {\n    this.texture.source.autoDensity = value;\n  }\n  /** The resolution / device pixel ratio of the renderer. */\n  get resolution() {\n    return this.texture.source._resolution;\n  }\n  set resolution(value) {\n    this.texture.source.resize(\n      this.texture.source.width,\n      this.texture.source.height,\n      value\n    );\n  }\n  /**\n   * initiates the view system\n   * @param options - the options for the view\n   */\n  init(options) {\n    options = {\n      ..._ViewSystem2.defaultOptions,\n      ...options\n    };\n    if (options.view) {\n      deprecation(v8_0_0, \"ViewSystem.view has been renamed to ViewSystem.canvas\");\n      options.canvas = options.view;\n    }\n    this.screen = new Rectangle(0, 0, options.width, options.height);\n    this.canvas = options.canvas || DOMAdapter.get().createCanvas();\n    this.antialias = !!options.antialias;\n    this.texture = getCanvasTexture(this.canvas, options);\n    this.renderTarget = new RenderTarget({\n      colorTextures: [this.texture],\n      depth: !!options.depth,\n      isRoot: true\n    });\n    this.texture.source.transparent = options.backgroundAlpha < 1;\n    this.resolution = options.resolution;\n  }\n  /**\n   * Resizes the screen and canvas to the specified dimensions.\n   * @param desiredScreenWidth - The new width of the screen.\n   * @param desiredScreenHeight - The new height of the screen.\n   * @param resolution\n   */\n  resize(desiredScreenWidth, desiredScreenHeight, resolution) {\n    this.texture.source.resize(desiredScreenWidth, desiredScreenHeight, resolution);\n    this.screen.width = this.texture.frame.width;\n    this.screen.height = this.texture.frame.height;\n  }\n  /**\n   * Destroys this System and optionally removes the canvas from the dom.\n   * @param {options | false} options - The options for destroying the view, or \"false\".\n   * @param options.removeView - Whether to remove the view element from the DOM. Defaults to `false`.\n   */\n  destroy(options = false) {\n    const removeView = typeof options === \"boolean\" ? options : !!options?.removeView;\n    if (removeView && this.canvas.parentNode) {\n      this.canvas.parentNode.removeChild(this.canvas);\n    }\n  }\n};\n_ViewSystem.extension = {\n  type: [\n    ExtensionType.WebGLSystem,\n    ExtensionType.WebGPUSystem,\n    ExtensionType.CanvasSystem\n  ],\n  name: \"view\",\n  priority: 0\n};\n_ViewSystem.defaultOptions = {\n  /**\n   * {@link WebGLOptions.width}\n   * @default 800\n   */\n  width: 800,\n  /**\n   * {@link WebGLOptions.height}\n   * @default 600\n   */\n  height: 600,\n  /**\n   * {@link WebGLOptions.autoDensity}\n   * @default false\n   */\n  autoDensity: false,\n  /**\n   * {@link WebGLOptions.antialias}\n   * @default false\n   */\n  antialias: false\n};\nlet ViewSystem = _ViewSystem;\nconst SharedSystems = [\n  BackgroundSystem,\n  GlobalUniformSystem,\n  HelloSystem,\n  ViewSystem,\n  RenderGroupSystem,\n  TextureGCSystem,\n  GenerateTextureSystem,\n  ExtractSystem,\n  RendererInitHook,\n  RenderableGCSystem,\n  SchedulerSystem\n];\nconst SharedRenderPipes = [\n  BlendModePipe,\n  BatcherPipe,\n  SpritePipe,\n  RenderGroupPipe,\n  AlphaMaskPipe,\n  StencilMaskPipe,\n  ColorMaskPipe,\n  CustomRenderPipe\n];\nexport {\n  BufferResource as B,\n  GpuStencilModesToPixi as G,\n  RenderTargetSystem as R,\n  SharedSystems as S,\n  UboSystem as U,\n  SharedRenderPipes as a,\n  uboSyncFunctionsSTD40 as b,\n  createUboSyncFunction as c,\n  uniformParsers as d,\n  ensureAttributes as e,\n  textureBitGl as f,\n  textureBit as t,\n  uboSyncFunctionsWGSL as u\n};\n"], "names": [], "mappings": ";;;AAEA,IAAI,QAAQ,GAAG,0zBAA0zB,CAAC;AAC10B,IAAI,MAAM,GAAG,k6BAAk6B,CAAC;AACh7B,IAAI,MAAM,GAAG,wjFAAwjF,CAAC;AACtkF,MAAM,UAAU,SAAS,MAAM,CAAC;AAChC,EAAE,WAAW,CAAC,OAAO,EAAE;AACvB,IAAI,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACxC,IAAI,MAAM,aAAa,GAAG,IAAI,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC5D,IAAI,MAAM,cAAc,GAAG,IAAI,YAAY,CAAC;AAC5C,MAAM,aAAa,EAAE,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;AACjE,MAAM,UAAU,EAAE,EAAE,KAAK,EAAE,aAAa,CAAC,WAAW,EAAE,IAAI,EAAE,WAAW,EAAE;AACzE,MAAM,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;AACvC,MAAM,QAAQ,EAAE,EAAE,KAAK,EAAE,OAAO,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE;AAC/D,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC;AACvC,MAAM,MAAM,EAAE;AACd,QAAQ,MAAM;AACd,QAAQ,UAAU,EAAE,YAAY;AAChC,OAAO;AACP,MAAM,QAAQ,EAAE;AAChB,QAAQ,MAAM;AACd,QAAQ,UAAU,EAAE,cAAc;AAClC,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC;AACrC,MAAM,MAAM;AACZ,MAAM,QAAQ;AACd,MAAM,IAAI,EAAE,aAAa;AACzB,KAAK,CAAC,CAAC;AACP,IAAI,KAAK,CAAC;AACV,MAAM,GAAG,IAAI;AACb,MAAM,UAAU;AAChB,MAAM,SAAS;AACf,MAAM,SAAS,EAAE;AACjB,QAAQ,cAAc;AACtB,QAAQ,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM;AAC3C,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,cAAc,GAAG,aAAa,CAAC;AACxC,GAAG;AACH,EAAE,IAAI,OAAO,CAAC,KAAK,EAAE;AACrB,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;AACpE,GAAG;AACH,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,QAAQ,KAAK,CAAC,CAAC;AACjE,GAAG;AACH,EAAE,KAAK,CAAC,aAAa,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE;AACjD,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;AACtD,IAAI,aAAa,CAAC,qBAAqB;AACvC,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,aAAa;AAC1D,MAAM,IAAI,CAAC,MAAM;AACjB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;AAC5C,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC;AAC7D,IAAI,aAAa,CAAC,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;AAC9D,GAAG;AACH,CAAC;AACD,MAAM,YAAY,GAAG,MAAM,aAAa,CAAC;AACzC,EAAE,WAAW,CAAC,QAAQ,EAAE,OAAO,EAAE;AACjC,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AAC/B,IAAI,IAAI,CAAC,yBAAyB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACzE,IAAI,IAAI,CAAC,cAAc,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9D,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,IAAI,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;AAC5B,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;AAC/B,GAAG;AACH,EAAE,OAAO,UAAU,CAAC,IAAI,EAAE;AAC1B,IAAI,OAAO,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;AAC/C,GAAG;AACH,EAAE,UAAU,CAAC,cAAc,EAAE;AAC7B,IAAI,IAAI,QAAQ,GAAG,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AACtE,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB,MAAM,QAAQ,GAAG,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,GAAG,CAAC,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC1G,MAAM,QAAQ,CAAC,OAAO,KAAK,QAAQ,CAAC,OAAO,GAAG,IAAI,cAAc,EAAE,CAAC,CAAC;AACpE,KAAK;AACL,IAAI,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC;AACnC,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC;AACpD,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE;AACzC,MAAM,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;AACrC,KAAK;AACL,GAAG;AACH,EAAE,UAAU,CAAC,eAAe,EAAE,cAAc,EAAE;AAC9C,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,KAAK,eAAe,CAAC,WAAW,EAAE;AAChE,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC9C,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;AACnE,MAAM,IAAI,CAAC,KAAK,EAAE;AAClB,QAAQ,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,WAAW,CAAC,GAAG,aAAa,CAAC,UAAU,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;AACzH,QAAQ,KAAK,CAAC,KAAK,EAAE,CAAC;AACtB,OAAO;AACP,MAAM,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAChC,KAAK;AACL,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;AAC3C,GAAG;AACH,EAAE,KAAK,CAAC,cAAc,EAAE;AACxB,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC5C,GAAG;AACH,EAAE,QAAQ,CAAC,cAAc,EAAE;AAC3B,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC5C,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC;AACxC,IAAI,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE;AAC7B,MAAM,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAC/B,MAAM,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;AACtC,MAAM,QAAQ,CAAC,WAAW,CAAC,eAAe,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;AACrF,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC,eAAe,CAAC,WAAW,EAAE,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;AACzG,KAAK;AACL,GAAG;AACH,EAAE,MAAM,CAAC,cAAc,EAAE;AACzB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,yBAAyB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;AACxE,IAAI,KAAK,MAAM,CAAC,IAAI,QAAQ,EAAE;AAC9B,MAAM,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClC,MAAM,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AACxC,MAAM,IAAI,OAAO,CAAC,KAAK,EAAE;AACzB,QAAQ,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AAC9B,QAAQ,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC;AAC9D,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,OAAO,CAAC,KAAK,EAAE;AACjB,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,YAAY,EAAE;AACvC,MAAM,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AACpC,MAAM,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;AACxC,MAAM,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AACpC,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;AAClD,KAAK;AACL,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACvC,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACtB,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE;AACzC,MAAM,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AACvC,KAAK;AACL,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,GAAG;AACH,CAAC,CAAC;AACF,YAAY,CAAC,SAAS,GAAG;AACzB,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,UAAU;AAC5B,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,OAAO;AACf,CAAC,CAAC;AACF,YAAY,CAAC,kBAAkB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACtE,IAAI,WAAW,GAAG,YAAY,CAAC;AAC/B,UAAU,CAAC,WAAW,CAAC,aAAa,CAAC,OAAO,EAAE,WAAW,CAAC,kBAAkB,CAAC,CAAC;AAC9E,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;AAC1B,MAAC,UAAU,GAAG;AACnB,EAAE,IAAI,EAAE,aAAa;AACrB,EAAE,MAAM,EAAE;AACV,IAAI,MAAM;AACV;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,IAAI,IAAI;AACR;AACA,MAAM,CAAC;AACP;AACA,QAAQ,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,MAAM;AACV;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,IAAI,IAAI;AACR;AACA,MAAM,CAAC;AACP;AACA,QAAQ,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE;AACG,MAAC,YAAY,GAAG;AACrB,EAAE,IAAI,EAAE,aAAa;AACrB,EAAE,MAAM,EAAE;AACV,IAAI,MAAM;AACV;AACA,MAAM,CAAC;AACP;AACA,QAAQ,CAAC;AACT,KAAK;AACL,IAAI,IAAI;AACR;AACA,MAAM,CAAC;AACP;AACA,QAAQ,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,MAAM;AACV;AACA,MAAM,CAAC;AACP;AACA;AACA;AACA,QAAQ,CAAC;AACT,KAAK;AACL,IAAI,IAAI;AACR;AACA,MAAM,CAAC;AACP;AACA,QAAQ,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE;AACF,MAAM,YAAY,GAAG,IAAI,MAAM,EAAE,CAAC;AAClC,MAAM,eAAe,SAAS,YAAY,CAAC;AAC3C,EAAE,WAAW,GAAG;AAChB,IAAI,KAAK,EAAE,CAAC;AACZ,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,UAAU,CAAC;AACnC,MAAM,MAAM,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AACvC,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,UAAU,EAAE,SAAS;AAC3B,MAAM,SAAS,EAAE,SAAS;AAC1B,KAAK,CAAC,CAAC,CAAC;AACR,GAAG;AACH,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;AAClC,GAAG;AACH,EAAE,IAAI,MAAM,CAAC,KAAK,EAAE;AACpB,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC;AACnC,GAAG;AACH,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;AACnC,GAAG;AACH,EAAE,IAAI,OAAO,CAAC,KAAK,EAAE;AACrB,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,KAAK,CAAC;AACpC,GAAG;AACH,CAAC;AACD,MAAM,aAAa,CAAC;AACpB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;AAC/B,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH,EAAE,IAAI,CAAC,IAAI,EAAE,eAAe,EAAE,cAAc,EAAE;AAC9C,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACrD,IAAI,cAAc,CAAC,GAAG,CAAC;AACvB,MAAM,YAAY,EAAE,WAAW;AAC/B,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,IAAI;AACV,MAAM,OAAO,EAAE,eAAe,CAAC,YAAY,CAAC,OAAO;AACnD,MAAM,SAAS,EAAE,KAAK;AACtB,MAAM,eAAe;AACrB,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,OAAO,GAAG,eAAe,CAAC,YAAY,CAAC,OAAO,CAAC;AACxD,IAAI,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAClC,MAAM,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC;AACtC,MAAM,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC;AAC1C,MAAM,aAAa,CAAC,kBAAkB;AACtC,QAAQ,cAAc;AACtB,QAAQ,QAAQ;AAChB,QAAQ,IAAI;AACZ,OAAO,CAAC;AACR,MAAM,aAAa,CAAC,cAAc,GAAG,KAAK,CAAC;AAC3C,KAAK;AACL,IAAI,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACrD,IAAI,cAAc,CAAC,GAAG,CAAC;AACvB,MAAM,YAAY,EAAE,WAAW;AAC/B,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,IAAI;AACV,MAAM,eAAe;AACrB,MAAM,OAAO,EAAE,eAAe,CAAC,YAAY,CAAC,OAAO;AACnD,MAAM,SAAS,EAAE,KAAK;AACtB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,GAAG,CAAC,IAAI,EAAE,gBAAgB,EAAE,cAAc,EAAE;AAC9C,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACrD,IAAI,cAAc,CAAC,GAAG,CAAC;AACvB,MAAM,YAAY,EAAE,WAAW;AAC/B,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,IAAI;AACV,MAAM,OAAO,EAAE,gBAAgB,CAAC,YAAY,CAAC,OAAO;AACpD,MAAM,SAAS,EAAE,KAAK;AACtB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,CAAC,WAAW,EAAE;AACvB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,MAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,mBAAmB,CAAC;AAC5D,IAAI,IAAI,WAAW,CAAC,MAAM,KAAK,eAAe,EAAE;AAChD,MAAM,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;AACxD,MAAM,YAAY,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;AACjD,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAChD,QAAQ,MAAM,MAAM,GAAG,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,CAAC;AAClF,QAAQ,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;AACjD,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;AACtB,QAAQ,MAAM,kBAAkB,GAAG,QAAQ,CAAC,YAAY,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC;AAC1F,QAAQ,MAAM,aAAa,GAAG,WAAW,CAAC,iBAAiB;AAC3D,UAAU,MAAM,CAAC,KAAK;AACtB,UAAU,MAAM,CAAC,MAAM;AACvB,UAAU,kBAAkB,CAAC,WAAW;AACxC,UAAU,kBAAkB,CAAC,SAAS;AACtC,SAAS,CAAC;AACV,QAAQ,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;AACxD,QAAQ,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC;AACrC,UAAU,MAAM,EAAE,MAAM;AACxB,UAAU,UAAU,EAAE,UAAU;AAChC,SAAS,CAAC,CAAC;AACX,QAAQ,MAAM,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC;AAC3C,QAAQ,MAAM,CAAC,OAAO,GAAG,aAAa,CAAC;AACvC,QAAQ,MAAM,CAAC,cAAc,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC;AAC/C,QAAQ,MAAM,CAAC,cAAc,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC;AAC/C,QAAQ,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AACnC,UAAU,YAAY;AACtB,UAAU,eAAe,EAAE,WAAW,CAAC,eAAe;AACtD,UAAU,aAAa;AACvB,SAAS,CAAC,CAAC;AACX,OAAO,MAAM;AACb,QAAQ,YAAY,CAAC,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC;AACpD,QAAQ,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;AACnC,UAAU,YAAY;AACtB,UAAU,eAAe,EAAE,WAAW,CAAC,eAAe;AACtD,SAAS,CAAC,CAAC;AACX,OAAO;AACP,KAAK,MAAM,IAAI,WAAW,CAAC,MAAM,KAAK,aAAa,EAAE;AACrD,MAAM,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC/E,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,IAAI,QAAQ,CAAC,IAAI,KAAK,YAAY,CAAC,KAAK,EAAE;AAClD,UAAU,QAAQ,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;AACnD,SAAS;AACT,QAAQ,QAAQ,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;AACpC,QAAQ,QAAQ,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC;AACtC,OAAO;AACP,MAAM,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;AAC3B,QAAQ,YAAY,EAAE,QAAQ;AAC9B,QAAQ,MAAM,EAAE,YAAY;AAC5B,QAAQ,SAAS,EAAE,QAAQ,CAAC,eAAe;AAC3C,QAAQ,YAAY,EAAE,QAAQ,CAAC,YAAY;AAC3C,QAAQ,SAAS,EAAE,KAAK;AACxB,OAAO,CAAC,CAAC;AACT,KAAK,MAAM,IAAI,WAAW,CAAC,MAAM,KAAK,YAAY,EAAE;AACpD,MAAM,QAAQ,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC;AAC5B,MAAM,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,CAAC;AACnD,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,WAAW,CAAC,aAAa,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;AAC1D,OAAO;AACP,MAAM,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;AAC5C,KAAK;AACL,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;AACjC,GAAG;AACH,CAAC;AACD,aAAa,CAAC,SAAS,GAAG;AAC1B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,UAAU;AAC5B,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,WAAW;AACnB,CAAC,CAAC;AACF,MAAM,aAAa,CAAC;AACpB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;AAC1B,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,aAAa,GAAG,CAAC,CAAC;AAC3B,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAC7B,IAAI,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AAC5B,GAAG;AACH,EAAE,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE;AACzC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACrD,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;AACxC,IAAI,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;AAC1F,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACjE,IAAI,IAAI,YAAY,KAAK,IAAI,CAAC,aAAa,EAAE;AAC7C,MAAM,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;AACxC,MAAM,cAAc,CAAC,GAAG,CAAC;AACzB,QAAQ,YAAY,EAAE,WAAW;AACjC,QAAQ,SAAS,EAAE,YAAY;AAC/B,QAAQ,SAAS,EAAE,KAAK;AACxB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC5B,GAAG;AACH,EAAE,GAAG,CAAC,KAAK,EAAE,UAAU,EAAE,cAAc,EAAE;AACzC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACrD,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;AACxC,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;AAC5B,IAAI,MAAM,YAAY,GAAG,UAAU,CAAC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC;AAC/D,IAAI,IAAI,YAAY,KAAK,IAAI,CAAC,aAAa,EAAE;AAC7C,MAAM,IAAI,CAAC,aAAa,GAAG,YAAY,CAAC;AACxC,MAAM,cAAc,CAAC,GAAG,CAAC;AACzB,QAAQ,YAAY,EAAE,WAAW;AACjC,QAAQ,SAAS,EAAE,YAAY;AAC/B,QAAQ,SAAS,EAAE,KAAK;AACxB,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE,OAAO,CAAC,WAAW,EAAE;AACvB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AACtD,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B,GAAG;AACH,CAAC;AACD,aAAa,CAAC,SAAS,GAAG;AAC1B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,UAAU;AAC5B,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,WAAW;AACnB,CAAC,CAAC;AACF,MAAM,eAAe,CAAC;AACtB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;AAC7B,IAAI,IAAI,CAAC,SAAS,mBAAmB,IAAI,OAAO,EAAE,CAAC;AACnD,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH,EAAE,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE;AACzC,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC;AACxB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACrD,IAAI,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;AACrF,IAAI,cAAc,CAAC,GAAG,CAAC;AACvB,MAAM,YAAY,EAAE,aAAa;AACjC,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,IAAI;AACV,MAAM,OAAO,EAAE,UAAU,CAAC,YAAY,CAAC,OAAO;AAC9C,MAAM,SAAS,EAAE,KAAK;AACtB,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,IAAI,CAAC;AACtC,IAAI,aAAa,CAAC,cAAc,GAAG,IAAI,CAAC;AACxC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;AACrC,MAAM,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,EAAE;AACjC,QAAQ,iBAAiB,EAAE,CAAC;AAC5B,QAAQ,kBAAkB,EAAE,CAAC;AAC7B,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AAChD,IAAI,QAAQ,CAAC,iBAAiB,GAAG,cAAc,CAAC,eAAe,CAAC;AAChE,IAAI,aAAa,CAAC,kBAAkB;AACpC,MAAM,cAAc;AACpB,MAAM,QAAQ;AACd,MAAM,IAAI;AACV,KAAK,CAAC;AACN,IAAI,aAAa,CAAC,cAAc,GAAG,KAAK,CAAC;AACzC,IAAI,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACrD,IAAI,cAAc,CAAC,GAAG,CAAC;AACvB,MAAM,YAAY,EAAE,aAAa;AACjC,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,IAAI;AACV,MAAM,OAAO,EAAE,UAAU,CAAC,YAAY,CAAC,OAAO;AAC9C,MAAM,SAAS,EAAE,KAAK;AACtB,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,kBAAkB,GAAG,cAAc,CAAC,eAAe,GAAG,QAAQ,CAAC,iBAAiB,GAAG,CAAC,CAAC;AAC/F,IAAI,QAAQ,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;AACrD,IAAI,MAAM,eAAe,GAAG,QAAQ,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC;AACnE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,eAAe,CAAC,KAAK,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7E,GAAG;AACH,EAAE,GAAG,CAAC,IAAI,EAAE,UAAU,EAAE,cAAc,EAAE;AACxC,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC;AACxB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AACrD,IAAI,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC;AACrF,IAAI,cAAc,CAAC,GAAG,CAAC;AACvB,MAAM,YAAY,EAAE,aAAa;AACjC,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,OAAO,EAAE,UAAU,CAAC,YAAY,CAAC,OAAO;AAC9C,MAAM,SAAS,EAAE,KAAK;AACtB,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC9C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,kBAAkB,EAAE,CAAC,EAAE,EAAE;AAC1D,MAAM,cAAc,CAAC,YAAY,CAAC,cAAc,CAAC,eAAe,EAAE,CAAC,GAAG,cAAc,CAAC,YAAY,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAC,CAAC;AAChI,KAAK;AACL,IAAI,cAAc,CAAC,GAAG,CAAC;AACvB,MAAM,YAAY,EAAE,aAAa;AACjC,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,SAAS,EAAE,KAAK;AACtB,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,OAAO,CAAC,WAAW,EAAE;AACvB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,MAAM,eAAe,GAAG,QAAQ,CAAC,YAAY,CAAC,YAAY,CAAC,GAAG,CAAC;AACnE,IAAI,IAAI,cAAc,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,eAAe,CAAC,KAAK,EAAE,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;AAClG,IAAI,IAAI,WAAW,CAAC,MAAM,KAAK,eAAe,EAAE;AAChD,MAAM,QAAQ,CAAC,YAAY,CAAC,kBAAkB,EAAE,CAAC;AACjD,MAAM,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,aAAa,CAAC,kBAAkB,EAAE,cAAc,CAAC,CAAC;AACxF,MAAM,cAAc,EAAE,CAAC;AACvB,MAAM,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACpC,KAAK,MAAM,IAAI,WAAW,CAAC,MAAM,KAAK,aAAa,EAAE;AACrD,MAAM,IAAI,WAAW,CAAC,OAAO,EAAE;AAC/B,QAAQ,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,aAAa,CAAC,mBAAmB,EAAE,cAAc,CAAC,CAAC;AAC3F,OAAO,MAAM;AACb,QAAQ,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,aAAa,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;AACnF,OAAO;AACP,MAAM,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACrC,KAAK,MAAM,IAAI,WAAW,CAAC,MAAM,KAAK,cAAc,EAAE;AACtD,MAAM,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACpC,MAAM,IAAI,cAAc,KAAK,CAAC,EAAE;AAChC,QAAQ,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,aAAa,CAAC,qBAAqB,EAAE,cAAc,CAAC,CAAC;AAC7F,OAAO,MAAM;AACb,QAAQ,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;AACzD,QAAQ,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,aAAa,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC;AAChF,OAAO;AACP,MAAM,cAAc,EAAE,CAAC;AACvB,KAAK,MAAM,IAAI,WAAW,CAAC,MAAM,KAAK,YAAY,EAAE;AACpD,MAAM,IAAI,WAAW,CAAC,OAAO,EAAE;AAC/B,QAAQ,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,aAAa,CAAC,mBAAmB,EAAE,cAAc,CAAC,CAAC;AAC3F,OAAO,MAAM;AACb,QAAQ,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,aAAa,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;AACnF,OAAO;AACP,MAAM,QAAQ,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;AACrC,KAAK;AACL,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,GAAG,cAAc,CAAC;AAC1D,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,CAAC;AACD,eAAe,CAAC,SAAS,GAAG;AAC5B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,UAAU;AAC5B,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,aAAa;AACrB,CAAC,CAAC;AACF,SAAS,gBAAgB,CAAC,QAAQ,EAAE,aAAa,EAAE;AACnD,EAAE,KAAK,MAAM,CAAC,IAAI,QAAQ,CAAC,UAAU,EAAE;AACvC,IAAI,MAAM,SAAS,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;AAC7C,IAAI,MAAM,aAAa,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC;AAC3C,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;AACpE,MAAM,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;AACpE,MAAM,SAAS,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAC,CAAC;AAC1E,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,iGAAiG,CAAC,CAAC,CAAC;AAC9H,KAAK;AACL,GAAG;AACH,EAAE,oBAAoB,CAAC,QAAQ,CAAC,CAAC;AACjC,CAAC;AACD,SAAS,oBAAoB,CAAC,QAAQ,EAAE;AACxC,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,QAAQ,CAAC;AAC3C,EAAE,MAAM,UAAU,GAAG,EAAE,CAAC;AACxB,EAAE,MAAM,SAAS,GAAG,EAAE,CAAC;AACvB,EAAE,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE;AAC3B,IAAI,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;AAC9B,IAAI,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC/B,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAC9B,GAAG;AACH,EAAE,KAAK,MAAM,CAAC,IAAI,UAAU,EAAE;AAC9B,IAAI,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACpC,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,0BAA0B,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;AAC5F,GAAG;AACH,EAAE,KAAK,MAAM,CAAC,IAAI,UAAU,EAAE;AAC9B,IAAI,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACpC,IAAI,SAAS,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9E,IAAI,SAAS,CAAC,KAAK,KAAK,SAAS,CAAC,KAAK,GAAG,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;AAC3E,IAAI,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,0BAA0B,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;AAC3F,GAAG;AACH,CAAC;AACI,MAAC,qBAAqB,GAAG,GAAG;AACjC,qBAAqB,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;AACnD,qBAAqB,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG;AAChD,EAAE,gBAAgB,EAAE,CAAC;AACrB,EAAE,eAAe,EAAE,CAAC;AACpB,CAAC,CAAC;AACF,qBAAqB,CAAC,aAAa,CAAC,kBAAkB,CAAC,GAAG;AAC1D,EAAE,YAAY,EAAE;AAChB,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,MAAM,EAAE,iBAAiB;AAC7B,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,MAAM,EAAE,iBAAiB;AAC7B,GAAG;AACH,CAAC,CAAC;AACF,qBAAqB,CAAC,aAAa,CAAC,qBAAqB,CAAC,GAAG;AAC7D,EAAE,YAAY,EAAE;AAChB,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,MAAM,EAAE,iBAAiB;AAC7B,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,MAAM,EAAE,iBAAiB;AAC7B,GAAG;AACH,CAAC,CAAC;AACF,qBAAqB,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG;AACnD,EAAE,gBAAgB,EAAE,CAAC;AACrB,EAAE,YAAY,EAAE;AAChB,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,MAAM,EAAE,MAAM;AAClB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,OAAO,EAAE,OAAO;AACpB,IAAI,MAAM,EAAE,MAAM;AAClB,GAAG;AACH,CAAC,CAAC;AACF,qBAAqB,CAAC,aAAa,CAAC,mBAAmB,CAAC,GAAG;AAC3D,EAAE,gBAAgB,EAAE,CAAC;AACrB,EAAE,YAAY,EAAE;AAChB,IAAI,OAAO,EAAE,WAAW;AACxB,IAAI,MAAM,EAAE,SAAS;AACrB,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,OAAO,EAAE,WAAW;AACxB,IAAI,MAAM,EAAE,SAAS;AACrB,GAAG;AACH,CAAC,CAAC;AACF,MAAM,SAAS,CAAC;AAChB,EAAE,WAAW,CAAC,OAAO,EAAE;AACvB,IAAI,IAAI,CAAC,iBAAiB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACjE,IAAI,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;AAC5B,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;AACxB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,GAAG;AACjB,IAAI,IAAI,CAAC,mBAAmB,EAAE,EAAE;AAChC,MAAM,MAAM,IAAI,KAAK,CAAC,0GAA0G,CAAC,CAAC;AAClI,KAAK;AACL,GAAG;AACH,EAAE,kBAAkB,CAAC,YAAY,EAAE;AACnC,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;AAC/D,IAAI,YAAY,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC;AAC7D,MAAM,IAAI,EAAE,IAAI,YAAY,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;AACzD,MAAM,KAAK,EAAE,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC,QAAQ;AACvD,KAAK,CAAC,CAAC,CAAC;AACR,GAAG;AACH,EAAE,mBAAmB,CAAC,YAAY,EAAE;AACpC,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;AACnG,GAAG;AACH,EAAE,iBAAiB,CAAC,YAAY,EAAE;AAClC,IAAI,MAAM,qBAAqB,GAAG,YAAY,CAAC,UAAU,CAAC;AAC1D,IAAI,IAAI,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;AACpE,IAAI,IAAI,CAAC,WAAW,EAAE;AACtB,MAAM,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,YAAY,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;AACjH,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AAC/D,MAAM,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AACrE,MAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,GAAG;AACpE,QAAQ,MAAM;AACd,QAAQ,YAAY;AACpB,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,CAAC;AACzD,GAAG;AACH,EAAE,gBAAgB,CAAC,WAAW,EAAE;AAChC,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,CAAC,CAAC;AACtD,GAAG;AACH,EAAE,gBAAgB,CAAC,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE;AAC/C,IAAI,MAAM,gBAAgB,GAAG,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC;AACpE,IAAI,YAAY,CAAC,MAAM,KAAK,YAAY,CAAC,MAAM,GAAG,IAAI,MAAM,CAAC;AAC7D,MAAM,IAAI,EAAE,IAAI,YAAY,CAAC,gBAAgB,CAAC,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;AAC9D,MAAM,KAAK,EAAE,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC,QAAQ;AACvD,KAAK,CAAC,CAAC,CAAC;AACR,IAAI,IAAI,SAAS,GAAG,IAAI,CAAC;AACzB,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,IAAI,GAAG,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC;AACtC,MAAM,SAAS,GAAG,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC;AAChD,KAAK;AACL,IAAI,MAAM,KAAK,MAAM,GAAG,CAAC,CAAC,CAAC;AAC3B,IAAI,gBAAgB,CAAC,YAAY,CAAC,YAAY,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;AAClF,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAE,kBAAkB,CAAC,YAAY,EAAE;AACnC,IAAI,IAAI,YAAY,CAAC,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ;AACvD,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,YAAY,CAAC,QAAQ,GAAG,CAAC,CAAC;AAC9B,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;AACvD,IAAI,YAAY,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;AACjC,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAClC,GAAG;AACH,CAAC;AACI,MAAC,cAAc,GAAG;AACvB;AACA,EAAE;AACF,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,IAAI,EAAE,CAAC,IAAI,KAAK;AACpB,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC/B,MAAM,OAAO,KAAK,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC;AAChC,KAAK;AACL,IAAI,GAAG,EAAE,CAAC;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,IAAI,OAAO,EAAE,CAAC;AACd;AACA,QAAQ,CAAC;AACT,GAAG;AACH;AACA,EAAE;AACF,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC;AAC/F,IAAI,GAAG,EAAE,CAAC;AACV;AACA;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,IAAI,OAAO,EAAE,CAAC;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,GAAG;AACH;AACA,EAAE;AACF,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,CAAC;AAC3F,IAAI,GAAG,EAAE,CAAC;AACV;AACA;AACA;AACA,QAAQ,CAAC;AACT,IAAI,OAAO,EAAE,CAAC;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,GAAG;AACH;AACA,EAAE;AACF,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC;AAC7F,IAAI,GAAG,EAAE,CAAC;AACV;AACA;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,IAAI,OAAO,EAAE,CAAC;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,GAAG;AACH;AACA,EAAE;AACF,IAAI,IAAI,EAAE,WAAW;AACrB,IAAI,IAAI,EAAE,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,WAAW,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC;AAC7F,IAAI,GAAG,EAAE,CAAC;AACV;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,IAAI,OAAO,EAAE,CAAC;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,QAAQ,CAAC;AACT,GAAG;AACH,EAAE;AACF,SAAS,qBAAqB,CAAC,WAAW,EAAE,UAAU,EAAE,uBAAuB,EAAE,gBAAgB,EAAE;AACnG,EAAE,MAAM,aAAa,GAAG,CAAC,CAAC;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC,CAAC,CAAC;AACP,EAAE,IAAI,IAAI,GAAG,CAAC,CAAC;AACf,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC/C,IAAI,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AACtC,IAAI,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC;AACtC,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpD,MAAM,MAAM,aAAa,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;AAC9C,MAAM,IAAI,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;AAC/C,QAAQ,MAAM,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;AACvC,QAAQ,aAAa,CAAC,IAAI;AAC1B,UAAU,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC;AAC7B,UAAU,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;AACvC,UAAU,cAAc,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,IAAI,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG;AAChE,SAAS,CAAC;AACV,QAAQ,MAAM,GAAG,IAAI,CAAC;AACtB,QAAQ,MAAM;AACd,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,EAAE;AACjB,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE;AACpC,QAAQ,MAAM,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;AACvC,QAAQ,aAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;AAC/E,OAAO,MAAM;AACb,QAAQ,MAAM,QAAQ,GAAG,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAChE,QAAQ,MAAM,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;AACvC,QAAQ,aAAa,CAAC,IAAI;AAC1B;AACA,UAAU,CAAC;AACX,2BAA2B,EAAE,IAAI,CAAC;AAClC,8BAA8B,EAAE,MAAM,GAAG,IAAI,CAAC;AAC9C,oBAAoB,EAAE,QAAQ,CAAC;AAC/B,gBAAgB,CAAC;AACjB,SAAS,CAAC;AACV,OAAO;AACP,KAAK;AACL,IAAI,IAAI,GAAG,MAAM,CAAC;AAClB,GAAG;AACH,EAAE,MAAM,WAAW,GAAG,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC/C,EAAE,OAAO,IAAI,QAAQ;AACrB,IAAI,IAAI;AACR,IAAI,MAAM;AACV,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,WAAW;AACf,GAAG,CAAC;AACJ,CAAC;AACD,SAAS,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE;AAC9B,EAAE,MAAM,KAAK,GAAG,GAAG,GAAG,GAAG,CAAC;AAC1B,EAAE,OAAO,CAAC;AACV,4BAA4B,EAAE,KAAK,CAAC;AACpC,iCAAiC,EAAE,GAAG,CAAC,iBAAiB,EAAE,GAAG,CAAC;AAC9D;AACA,IAAI,CAAC,CAAC;AACN,CAAC;AACI,MAAC,qBAAqB,GAAG;AAC9B,EAAE,GAAG,EAAE,CAAC;AACR,yBAAyB,CAAC;AAC1B,EAAE,GAAG,EAAE,CAAC;AACR,8BAA8B,CAAC;AAC/B,EAAE,WAAW,EAAE,CAAC;AAChB;AACA,gCAAgC,CAAC;AACjC,EAAE,WAAW,EAAE,CAAC;AAChB;AACA;AACA,gCAAgC,CAAC;AACjC,EAAE,WAAW,EAAE,CAAC;AAChB;AACA;AACA;AACA,gCAAgC,CAAC;AACjC,EAAE,WAAW,EAAE,CAAC;AAChB;AACA,qCAAqC,CAAC;AACtC,EAAE,WAAW,EAAE,CAAC;AAChB;AACA;AACA,qCAAqC,CAAC;AACtC,EAAE,WAAW,EAAE,CAAC;AAChB;AACA;AACA;AACA,qCAAqC,CAAC;AACtC,EAAE,aAAa,EAAE,CAAC;AAClB;AACA;AACA;AACA,gCAAgC,CAAC;AACjC,EAAE,aAAa,EAAE,CAAC;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC,CAAC;AAClC,EAAE,aAAa,EAAE,CAAC;AAClB;AACA;AACA,SAAS,CAAC;AACV,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;AACjC,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;AACjC,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;AACjC,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;AACjC,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;AACjC,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC;AACjC,EAAE;AACG,MAAC,oBAAoB,GAAG;AAC7B,EAAE,GAAG,qBAAqB;AAC1B,EAAE,aAAa,EAAE,CAAC;AAClB;AACA;AACA;AACA;AACA,IAAI,CAAC;AACL,EAAE;AACF,SAAS,mBAAmB,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;AAC7D,EAAE,MAAM,IAAI,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC9B,EAAE,EAAE,CAAC,QAAQ,EAAE,CAAC;AAChB,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;AACvB,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,CAAC,CAAC;AACjC,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACxB,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AAC3B,EAAE,OAAO,EAAE,CAAC;AACZ,CAAC;AACD,MAAM,WAAW,mBAAmB,IAAI,GAAG,EAAE,CAAC;AAC9C,SAAS,gBAAgB,CAAC,MAAM,EAAE,OAAO,EAAE;AAC3C,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;AAChC,IAAI,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC;AAChC,MAAM,MAAM,EAAE,IAAI,YAAY,CAAC;AAC/B,QAAQ,QAAQ,EAAE,MAAM;AACxB,QAAQ,GAAG,OAAO;AAClB,OAAO,CAAC;AACR,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,SAAS,GAAG,MAAM;AAC5B,MAAM,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,OAAO,EAAE;AAC/C,QAAQ,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACnC,OAAO;AACP,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACvC,IAAI,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AAC9C,IAAI,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AACrC,GAAG;AACH,EAAE,OAAO,WAAW,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;AACjC,CAAC;AACD,SAAS,mBAAmB,CAAC,YAAY,EAAE;AAC3C,EAAE,MAAM,QAAQ,GAAG,YAAY,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC;AAC7D,EAAE,OAAO,UAAU,CAAC,iBAAiB,IAAI,QAAQ,YAAY,iBAAiB,IAAI,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACnH,CAAC;AACD,MAAM,aAAa,GAAG,MAAM,cAAc,CAAC;AAC3C;AACA;AACA;AACA,EAAE,WAAW,CAAC,UAAU,GAAG,EAAE,EAAE;AAC/B,IAAI,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC;AACrC,IAAI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AAC5B,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;AACrB,IAAI,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;AACxB,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC;AACrC,IAAI,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;AACvC,IAAI,UAAU,GAAG,EAAE,GAAG,cAAc,CAAC,cAAc,EAAE,GAAG,UAAU,EAAE,CAAC;AACrE,IAAI,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;AACtC,IAAI,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;AAClC,IAAI,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;AACpC,IAAI,IAAI,OAAO,UAAU,CAAC,aAAa,KAAK,QAAQ,EAAE;AACtD,MAAM,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;AACxC,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,aAAa,EAAE,CAAC,EAAE,EAAE;AACzD,QAAQ,IAAI,CAAC,aAAa,CAAC,IAAI;AAC/B,UAAU,IAAI,aAAa,CAAC;AAC5B,YAAY,KAAK,EAAE,UAAU,CAAC,KAAK;AACnC,YAAY,MAAM,EAAE,UAAU,CAAC,MAAM;AACrC,YAAY,UAAU,EAAE,UAAU,CAAC,UAAU;AAC7C,YAAY,SAAS,EAAE,UAAU,CAAC,SAAS;AAC3C,WAAW,CAAC;AACZ,SAAS,CAAC;AACV,OAAO;AACP,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,UAAU,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AAC1F,MAAM,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;AACnD,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC;AAClF,KAAK;AACL,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACrE,IAAI,IAAI,UAAU,CAAC,mBAAmB,IAAI,IAAI,CAAC,OAAO,EAAE;AACxD,MAAM,IAAI,UAAU,CAAC,mBAAmB,YAAY,OAAO,IAAI,UAAU,CAAC,mBAAmB,YAAY,aAAa,EAAE;AACxH,QAAQ,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,mBAAmB,CAAC,MAAM,CAAC;AACzE,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,yBAAyB,EAAE,CAAC;AACzC,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,IAAI,IAAI,GAAG;AACb,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC7B,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;AAC/B,IAAI,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC;AAChC,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC;AAC1C,GAAG;AACH,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC;AAC3C,GAAG;AACH,EAAE,IAAI,UAAU,GAAG;AACnB,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,UAAU,CAAC;AAC/C,GAAG;AACH,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC;AAChD,GAAG;AACH,EAAE,IAAI,UAAU,GAAG;AACnB,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC;AAChD,GAAG;AACH,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;AACjC,GAAG;AACH,EAAE,cAAc,CAAC,OAAO,EAAE;AAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;AAC1E,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,yBAAyB,GAAG;AAC9B,IAAI,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;AACnC,MAAM,IAAI,CAAC,mBAAmB,GAAG,IAAI,aAAa,CAAC;AACnD,QAAQ,KAAK,EAAE,IAAI,CAAC,KAAK;AACzB,QAAQ,MAAM,EAAE,IAAI,CAAC,MAAM;AAC3B,QAAQ,UAAU,EAAE,IAAI,CAAC,UAAU;AACnC,QAAQ,MAAM,EAAE,sBAAsB;AACtC,QAAQ,mBAAmB,EAAE,KAAK;AAClC,QAAQ,SAAS,EAAE,KAAK;AACxB,QAAQ,aAAa,EAAE,CAAC;AACxB;AACA,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG;AACH,EAAE,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,gBAAgB,GAAG,KAAK,EAAE;AAChF,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;AACnB,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,CAAC,KAAK;AACpD,MAAM,IAAI,gBAAgB,IAAI,CAAC,KAAK,CAAC;AACrC,QAAQ,OAAO;AACf,MAAM,YAAY,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AAC5D,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAClC,MAAM,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;AACxE,KAAK;AACL,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACtE,IAAI,IAAI,IAAI,CAAC,qBAAqB,EAAE;AACpC,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK;AAC9C,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC;AAC1B,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,IAAI,IAAI,CAAC,mBAAmB,EAAE;AAClC,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;AACzC,MAAM,OAAO,IAAI,CAAC,mBAAmB,CAAC;AACtC,KAAK;AACL,GAAG;AACH,CAAC,CAAC;AACF,aAAa,CAAC,cAAc,GAAG;AAC/B;AACA,EAAE,KAAK,EAAE,CAAC;AACV;AACA,EAAE,MAAM,EAAE,CAAC;AACX;AACA,EAAE,UAAU,EAAE,CAAC;AACf;AACA,EAAE,aAAa,EAAE,CAAC;AAClB;AACA,EAAE,OAAO,EAAE,KAAK;AAChB;AACA,EAAE,KAAK,EAAE,KAAK;AACd;AACA,EAAE,SAAS,EAAE,KAAK;AAClB;AACA;AACA,EAAE,MAAM,EAAE,KAAK;AACf,CAAC,CAAC;AACF,IAAI,YAAY,GAAG,aAAa,CAAC;AACjC,MAAM,kBAAkB,CAAC;AACzB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,SAAS,EAAE,CAAC;AACxC,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC;AACpC,IAAI,IAAI,CAAC,oBAAoB,GAAG,IAAI,YAAY,CAAC,sBAAsB,CAAC,CAAC;AACzE,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,MAAM,EAAE,CAAC;AACzC,IAAI,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC1C,IAAI,IAAI,CAAC,gCAAgC,mBAAmB,IAAI,GAAG,EAAE,CAAC;AACtE,IAAI,IAAI,CAAC,oBAAoB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACpE,IAAI,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;AACjC,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,QAAQ,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,sBAAsB,CAAC,CAAC;AACvE,GAAG;AACH;AACA,EAAE,gBAAgB,GAAG;AACrB,IAAI,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACrD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC;AACd,IAAI,MAAM;AACV,IAAI,KAAK;AACT,IAAI,UAAU;AACd,IAAI,KAAK;AACT,GAAG,EAAE;AACL,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC;AACvC,IAAI,IAAI,CAAC,IAAI;AACb,MAAM,MAAM;AACZ,MAAM,KAAK;AACX,MAAM,UAAU;AAChB,MAAM,KAAK;AACX,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC9C,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC;AAC9C,IAAI,IAAI,CAAC,iBAAiB,GAAG,mBAAmB,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACxE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACpD,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACrD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,GAAG,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE;AACvD,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;AAC7D,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,KAAK,YAAY,CAAC;AACzD,IAAI,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACrC,IAAI,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;AACvC,IAAI,MAAM,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAC;AAClE,IAAI,IAAI,YAAY,CAAC,UAAU,KAAK,eAAe,CAAC,KAAK,IAAI,YAAY,CAAC,WAAW,KAAK,eAAe,CAAC,MAAM,EAAE;AAClH,MAAM,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;AACvD,MAAM,eAAe,CAAC,KAAK,GAAG,YAAY,CAAC,UAAU,CAAC;AACtD,MAAM,eAAe,CAAC,MAAM,GAAG,YAAY,CAAC,WAAW,CAAC;AACxD,KAAK;AACL,IAAI,MAAM,OAAO,GAAG,YAAY,CAAC,YAAY,CAAC;AAC9C,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACnC,IAAI,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;AAC1C,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;AAC5C,IAAI,IAAI,CAAC,KAAK,IAAI,aAAa,YAAY,OAAO,EAAE;AACpD,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC;AAClC,KAAK;AACL,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,MAAM,UAAU,GAAG,OAAO,CAAC,WAAW,CAAC;AAC7C,MAAM,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,UAAU,GAAG,GAAG,GAAG,CAAC,CAAC;AAClD,MAAM,QAAQ,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,UAAU,GAAG,GAAG,GAAG,CAAC,CAAC;AAClD,MAAM,QAAQ,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,UAAU,GAAG,GAAG,GAAG,CAAC,CAAC;AAC1D,MAAM,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,UAAU,GAAG,GAAG,GAAG,CAAC,CAAC;AAC5D,KAAK,MAAM;AACX,MAAM,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;AACrB,MAAM,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;AACrB,MAAM,QAAQ,CAAC,KAAK,GAAG,UAAU,CAAC;AAClC,MAAM,QAAQ,CAAC,MAAM,GAAG,WAAW,CAAC;AACpC,KAAK;AACL,IAAI,mBAAmB;AACvB,MAAM,IAAI,CAAC,gBAAgB;AAC3B,MAAM,CAAC;AACP,MAAM,CAAC;AACP,MAAM,QAAQ,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU;AACzC,MAAM,QAAQ,CAAC,MAAM,GAAG,OAAO,CAAC,UAAU;AAC1C,MAAM,CAAC,YAAY,CAAC,MAAM;AAC1B,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,YAAY,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;AAC5E,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACnD,KAAK;AACL,IAAI,OAAO,YAAY,CAAC;AACxB,GAAG;AACH,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,GAAG,KAAK,CAAC,GAAG,EAAE,UAAU,EAAE;AAC/C,IAAI,IAAI,CAAC,KAAK;AACd,MAAM,OAAO;AACb,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;AAC5C,KAAK;AACL,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK;AACtB,MAAM,MAAM,IAAI,IAAI,CAAC,YAAY;AACjC,MAAM,KAAK;AACX,MAAM,UAAU;AAChB,MAAM,IAAI,CAAC,QAAQ;AACnB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,aAAa,GAAG;AAClB,IAAI,IAAI,CAAC,oBAAoB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACpE,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,GAAG,KAAK,CAAC,GAAG,EAAE,UAAU,EAAE,KAAK,EAAE;AAC5D,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;AAC5E,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;AACjC,MAAM,YAAY;AAClB,MAAM,KAAK;AACX,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,YAAY,CAAC;AACxB,GAAG;AACH;AACA,EAAE,GAAG,GAAG;AACR,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,EAAE,CAAC;AAClC,IAAI,MAAM,uBAAuB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAChG,IAAI,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE,uBAAuB,CAAC,KAAK,CAAC,CAAC;AAChG,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,aAAa,EAAE;AACjC,IAAI,IAAI,aAAa,CAAC,SAAS,EAAE;AACjC,MAAM,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC;AAC3C,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,gCAAgC,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC,CAAC;AAC7G,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,0BAA0B,EAAE,kBAAkB,EAAE,SAAS,EAAE,IAAI,EAAE,UAAU,EAAE;AAC7F,IAAI,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE;AACzB,MAAM,IAAI,CAAC,KAAK,IAAI,SAAS,CAAC,CAAC,CAAC;AAChC,MAAM,UAAU,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC;AAClC,MAAM,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;AACtB,KAAK;AACL,IAAI,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE;AACzB,MAAM,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC;AACjC,MAAM,UAAU,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC;AAClC,MAAM,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC;AACtB,KAAK;AACL,IAAI,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,GAAG,0BAA0B,CAAC;AACnE,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AAChE,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;AACnE,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa;AACrC,MAAM,0BAA0B;AAChC,MAAM,kBAAkB;AACxB,MAAM,SAAS;AACf,MAAM,IAAI;AACV,MAAM,UAAU;AAChB,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,kBAAkB,GAAG;AACvB,IAAI,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;AACpC,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC;AACvC,MAAM,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;AAClF,KAAK;AACL,GAAG;AACH;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,gCAAgC,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,GAAG,KAAK;AACzE,MAAM,IAAI,YAAY,KAAK,GAAG,EAAE;AAChC,QAAQ,YAAY,CAAC,OAAO,EAAE,CAAC;AAC/B,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,gCAAgC,CAAC,KAAK,EAAE,CAAC;AAClD,IAAI,IAAI,CAAC,oBAAoB,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACpE,GAAG;AACH,EAAE,iBAAiB,CAAC,aAAa,EAAE;AACnC,IAAI,IAAI,YAAY,GAAG,IAAI,CAAC;AAC5B,IAAI,IAAI,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE;AAC1C,MAAM,aAAa,GAAG,gBAAgB,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC;AAC7D,KAAK;AACL,IAAI,IAAI,aAAa,YAAY,YAAY,EAAE;AAC/C,MAAM,YAAY,GAAG,aAAa,CAAC;AACnC,KAAK,MAAM,IAAI,aAAa,YAAY,aAAa,EAAE;AACvD,MAAM,YAAY,GAAG,IAAI,YAAY,CAAC;AACtC,QAAQ,aAAa,EAAE,CAAC,aAAa,CAAC;AACtC,OAAO,CAAC,CAAC;AACT,MAAM,IAAI,YAAY,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;AAC5D,QAAQ,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC;AACnC,OAAO;AACP,MAAM,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM;AAC1C,QAAQ,YAAY,CAAC,OAAO,EAAE,CAAC;AAC/B,QAAQ,IAAI,CAAC,gCAAgC,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AACpE,QAAQ,MAAM,eAAe,GAAG,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;AAC5E,QAAQ,IAAI,eAAe,EAAE;AAC7B,UAAU,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AAC7D,UAAU,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,eAAe,CAAC,CAAC;AAC/D,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,IAAI,CAAC,gCAAgC,CAAC,GAAG,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;AAC3E,IAAI,OAAO,YAAY,CAAC;AACxB,GAAG;AACH,EAAE,kBAAkB,CAAC,YAAY,EAAE;AACnC,IAAI,OAAO,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,oBAAoB,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,YAAY,CAAC,CAAC,CAAC;AACzJ,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;AAC7B,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;AAC9B,GAAG;AACH,CAAC;AACD,MAAM,cAAc,SAAS,YAAY,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE;AACxC,IAAI,KAAK,EAAE,CAAC;AACZ,IAAI,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC/B,IAAI,IAAI,CAAC,aAAa,GAAG,gBAAgB,CAAC;AAC1C,IAAI,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;AACtB,IAAI,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;AACzC,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,IAAI,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;AAC3B,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,CAAC,CAAC;AAC7B,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;AACxD,GAAG;AACH,EAAE,cAAc,GAAG;AACnB,IAAI,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC;AACzC,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAC9B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,aAAa,GAAG,KAAK,EAAE;AACjC,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;AAC5B,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AACvB,GAAG;AACH,CAAC;AACD,MAAM,gBAAgB,CAAC;AACvB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH,EAAE,gBAAgB,GAAG;AACrB,GAAG;AACH,EAAE,iBAAiB,GAAG;AACtB,GAAG;AACH,EAAE,kBAAkB,GAAG;AACvB,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,aAAa,CAAC,SAAS,EAAE,cAAc,EAAE;AAC3C,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC3D,IAAI,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;AAClC,GAAG;AACH,EAAE,OAAO,CAAC,SAAS,EAAE;AACrB,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY;AAC/B,MAAM,OAAO;AACb,IAAI,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrC,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,CAAC;AACD,gBAAgB,CAAC,SAAS,GAAG;AAC7B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,UAAU;AAC5B,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,cAAc;AACtB,CAAC,CAAC;AACF,SAAS,mBAAmB,CAAC,WAAW,EAAE,QAAQ,EAAE;AACpD,EAAE,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC;AACpD,EAAE,MAAM,YAAY,GAAG,cAAc,CAAC,YAAY,CAAC;AACnD,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE;AAC3D,IAAI,MAAM,WAAW,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;AACxC,IAAI,QAAQ,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;AAC5D,GAAG;AACH,CAAC;AACD,MAAM,YAAY,GAAG,IAAI,MAAM,EAAE,CAAC;AAClC,MAAM,eAAe,CAAC;AACtB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH,EAAE,cAAc,CAAC,WAAW,EAAE,cAAc,EAAE;AAC9C,IAAI,IAAI,WAAW,CAAC,iBAAiB,EAAE;AACvC,MAAM,IAAI,CAAC,4BAA4B,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;AACrE,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,cAAc,CAAC,CAAC;AAC7D,KAAK;AACL,GAAG;AACH,EAAE,OAAO,CAAC,WAAW,EAAE;AACvB,IAAI,IAAI,CAAC,WAAW,CAAC,YAAY;AACjC,MAAM,OAAO;AACb,IAAI,IAAI,WAAW,CAAC,iBAAiB,EAAE;AACvC,MAAM,IAAI,CAAC,sBAAsB,CAAC,WAAW,CAAC,CAAC;AAC/C,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;AACvC,KAAK;AACL,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,EAAE,oBAAoB,CAAC,WAAW,EAAE,cAAc,EAAE;AACpD,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC3D,IAAI,IAAI,WAAW,CAAC,qBAAqB,EAAE;AAC3C,MAAM,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;AACxD,MAAM,WAAW,CAAC,qBAAqB,GAAG,IAAI,CAAC;AAC/C,KAAK;AACL,IAAI,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AACpC,GAAG;AACH,EAAE,4BAA4B,CAAC,WAAW,EAAE,cAAc,EAAE;AAC5D,IAAI,MAAM,oBAAoB,GAAG,WAAW,CAAC,qBAAqB,KAAK,WAAW,CAAC,qBAAqB,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC;AACzI,IAAI,oBAAoB,CAAC,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC;AACvD,IAAI,oBAAoB,CAAC,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,sBAAsB,CAAC;AAC7E,IAAI,oBAAoB,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC;AACvD,IAAI,oBAAoB,CAAC,MAAM,GAAG,WAAW,CAAC,cAAc,CAAC;AAC7D,IAAI,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,oBAAoB,EAAE,cAAc,CAAC,CAAC;AACtF,GAAG;AACH,EAAE,sBAAsB,CAAC,WAAW,EAAE;AACtC,IAAI,IAAI,WAAW,CAAC,kBAAkB,EAAE;AACxC,MAAM,WAAW,CAAC,kBAAkB,GAAG,KAAK,CAAC;AAC7C,MAAM,MAAM,oBAAoB,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC,SAAS;AACpE,QAAQ,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;AACrC,QAAQ,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;AACrC,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACnG,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC;AACzC,QAAQ,oBAAoB;AAC5B,QAAQ,UAAU,EAAE,UAAU;AAC9B,OAAO,CAAC,CAAC;AACT,MAAM,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;AACnE,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,gBAAgB,EAAE,CAAC;AACrD,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;AACxC,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC;AAC1C,KAAK;AACL,IAAI,WAAW,CAAC,qBAAqB,CAAC,QAAQ,CAAC,aAAa,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;AAChG,IAAI,WAAW,CAAC,qBAAqB,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AAC5E,GAAG;AACH,EAAE,cAAc,CAAC,WAAW,EAAE;AAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC;AACvC,MAAM,oBAAoB,EAAE,WAAW,CAAC,6BAA6B;AACrE,MAAM,UAAU,EAAE,WAAW,CAAC,eAAe;AAC7C,KAAK,CAAC,CAAC;AACP,IAAI,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;AACjE,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC;AACxC,GAAG;AACH,CAAC;AACD,eAAe,CAAC,SAAS,GAAG;AAC5B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,UAAU;AAC5B,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,aAAa;AACrB,CAAC,CAAC;AACF,SAAS,SAAS,CAAC,IAAI,EAAE,KAAK,EAAE;AAChC,EAAE,KAAK,KAAK,KAAK,GAAG,CAAC,CAAC,CAAC;AACvB,EAAE,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE;AACjB,MAAM,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AACrB,KAAK,MAAM;AACX,MAAM,MAAM;AACZ,KAAK;AACL,GAAG;AACH,CAAC;AACD,MAAM,aAAa,GAAG,IAAI,SAAS,EAAE,CAAC;AACtC,MAAM,0BAA0B,GAAG,cAAc,GAAG,YAAY,GAAG,YAAY,CAAC;AAChF,SAAS,2BAA2B,CAAC,WAAW,EAAE,uBAAuB,GAAG,KAAK,EAAE;AACnF,EAAE,0BAA0B,CAAC,WAAW,CAAC,CAAC;AAC1C,EAAE,MAAM,gBAAgB,GAAG,WAAW,CAAC,gBAAgB,CAAC;AACxD,EAAE,MAAM,UAAU,GAAG,WAAW,CAAC,UAAU,EAAE,CAAC;AAC9C,EAAE,KAAK,MAAM,CAAC,IAAI,gBAAgB,EAAE;AACpC,IAAI,MAAM,gBAAgB,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACvC,IAAI,MAAM,eAAe,GAAG,gBAAgB,CAAC,CAAC,CAAC,CAAC;AAChD,IAAI,MAAM,IAAI,GAAG,eAAe,CAAC,IAAI,CAAC;AACtC,IAAI,MAAM,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC;AACxC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;AACpC,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5B,MAAM,IAAI,KAAK,CAAC,iBAAiB,KAAK,WAAW,IAAI,KAAK,CAAC,wBAAwB,KAAK,gBAAgB,EAAE;AAC1G,QAAQ,0BAA0B,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC;AACzD,OAAO;AACP,KAAK;AACL,IAAI,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC3B,IAAI,eAAe,CAAC,KAAK,GAAG,CAAC,CAAC;AAC9B,GAAG;AACH,EAAE,IAAI,uBAAuB,EAAE;AAC/B,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrE,MAAM,2BAA2B,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,uBAAuB,CAAC,CAAC;AAC/F,KAAK;AACL,GAAG;AACH,CAAC;AACD,SAAS,0BAA0B,CAAC,WAAW,EAAE;AACjD,EAAE,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;AAChC,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,WAAW,CAAC,iBAAiB,EAAE;AACrC,IAAI,MAAM,iBAAiB,GAAG,WAAW,CAAC,iBAAiB,CAAC;AAC5D,IAAI,WAAW,CAAC,cAAc,CAAC,UAAU;AACzC,MAAM,IAAI,CAAC,sBAAsB;AACjC,MAAM,iBAAiB,CAAC,cAAc;AACtC,KAAK,CAAC;AACN,IAAI,WAAW,CAAC,UAAU,GAAG,cAAc;AAC3C,MAAM,IAAI,CAAC,UAAU;AACrB,MAAM,iBAAiB,CAAC,UAAU;AAClC,KAAK,CAAC;AACN,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,GAAG,iBAAiB,CAAC,UAAU,CAAC;AAChE,GAAG,MAAM;AACT,IAAI,WAAW,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAC7D,IAAI,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AAC7C,IAAI,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AACjC,GAAG;AACH,EAAE,UAAU,GAAG,UAAU,GAAG,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;AACpE,EAAE,WAAW,CAAC,UAAU,GAAG,UAAU,CAAC;AACtC,EAAE,WAAW,CAAC,eAAe,GAAG,WAAW,CAAC,UAAU,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC;AACxF,CAAC;AACD,SAAS,0BAA0B,CAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE;AACxE,EAAE,IAAI,UAAU,KAAK,SAAS,CAAC,UAAU;AACzC,IAAI,OAAO;AACX,EAAE,SAAS,CAAC,UAAU,GAAG,UAAU,CAAC;AACpC,EAAE,SAAS,CAAC,SAAS,GAAG,KAAK,CAAC;AAC9B,EAAE,MAAM,cAAc,GAAG,SAAS,CAAC,cAAc,CAAC;AAClD,EAAE,SAAS,CAAC,oBAAoB,EAAE,CAAC;AACnC,EAAE,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;AAClC,EAAE,IAAI,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;AACrC,IAAI,WAAW,IAAI,SAAS,CAAC,YAAY,CAAC;AAC1C,IAAI,SAAS,CAAC,sBAAsB,CAAC,UAAU;AAC/C,MAAM,cAAc;AACpB,MAAM,MAAM,CAAC,sBAAsB;AACnC,KAAK,CAAC;AACN,IAAI,IAAI,WAAW,GAAG,0BAA0B,EAAE;AAClD,MAAM,0BAA0B,CAAC,SAAS,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;AACjE,KAAK;AACL,GAAG,MAAM;AACT,IAAI,WAAW,GAAG,SAAS,CAAC,YAAY,CAAC;AACzC,IAAI,SAAS,CAAC,sBAAsB,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;AAC9D,IAAI,IAAI,WAAW,GAAG,0BAA0B,EAAE;AAClD,MAAM,0BAA0B,CAAC,SAAS,EAAE,aAAa,EAAE,WAAW,CAAC,CAAC;AACxE,KAAK;AACL,GAAG;AACH,EAAE,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;AAC9B,IAAI,MAAM,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;AACxC,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;AACnC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;AACrC,MAAM,0BAA0B,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;AACvE,KAAK;AACL,IAAI,MAAM,WAAW,GAAG,SAAS,CAAC,iBAAiB,CAAC;AACpD,IAAI,MAAM,UAAU,GAAG,SAAS,CAAC;AACjC,IAAI,IAAI,UAAU,CAAC,YAAY,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE;AACpE,MAAM,WAAW,CAAC,gBAAgB,CAAC,UAAU,CAAC,CAAC;AAC/C,KAAK;AACL,GAAG;AACH,CAAC;AACD,SAAS,0BAA0B,CAAC,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE;AACpE,EAAE,IAAI,WAAW,GAAG,YAAY,EAAE;AAClC,IAAI,SAAS,CAAC,UAAU,GAAG,cAAc;AACzC,MAAM,SAAS,CAAC,UAAU;AAC1B,MAAM,MAAM,CAAC,UAAU;AACvB,KAAK,CAAC;AACN,IAAI,IAAI,UAAU,GAAG,SAAS,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;AAC9D,IAAI,UAAU,GAAG,UAAU,GAAG,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;AACtE,IAAI,SAAS,CAAC,UAAU,GAAG,UAAU,CAAC;AACtC,IAAI,SAAS,CAAC,eAAe,GAAG,SAAS,CAAC,UAAU,IAAI,CAAC,UAAU,GAAG,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC;AACtF,GAAG;AACH,EAAE,IAAI,WAAW,GAAG,YAAY,EAAE;AAClC,IAAI,SAAS,CAAC,cAAc,GAAG,SAAS,CAAC,cAAc,KAAK,SAAS,GAAG,MAAM,CAAC,cAAc,GAAG,SAAS,CAAC,cAAc,CAAC;AACzH,GAAG;AACH,EAAE,IAAI,WAAW,GAAG,cAAc,EAAE;AACpC,IAAI,SAAS,CAAC,mBAAmB,GAAG,SAAS,CAAC,kBAAkB,GAAG,MAAM,CAAC,mBAAmB,CAAC;AAC9F,GAAG;AACH,EAAE,SAAS,CAAC,YAAY,GAAG,CAAC,CAAC;AAC7B,CAAC;AACD,SAAS,mBAAmB,CAAC,WAAW,EAAE,WAAW,EAAE;AACvD,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,2BAA2B,CAAC;AAClE,EAAE,IAAI,eAAe,GAAG,KAAK,CAAC;AAC9B,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;AAClC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC9B,IAAI,MAAM,UAAU,GAAG,SAAS,CAAC;AACjC,IAAI,MAAM,IAAI,GAAG,WAAW,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;AACtD,IAAI,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;AACzD,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,MAAM;AACZ,KAAK;AACL,GAAG;AACH,EAAE,WAAW,CAAC,kBAAkB,GAAG,eAAe,CAAC;AACnD,EAAE,OAAO,eAAe,CAAC;AACzB,CAAC;AACD,MAAM,UAAU,GAAG,IAAI,MAAM,EAAE,CAAC;AAChC,MAAM,iBAAiB,CAAC;AACxB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH,EAAE,MAAM,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE;AACnC,IAAI,MAAM,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;AACpC,IAAI,MAAM,iBAAiB,GAAG,SAAS,CAAC,WAAW,CAAC,iBAAiB,CAAC;AACtE,IAAI,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC;AAC5B,IAAI,SAAS,CAAC,WAAW,CAAC,iBAAiB,GAAG,IAAI,CAAC;AACnD,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,IAAI,sBAAsB,GAAG,UAAU,CAAC;AAC5C,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,sBAAsB,GAAG,sBAAsB,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;AACrG,MAAM,SAAS,CAAC,WAAW,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;AAC/D,KAAK;AACL,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;AAC7C,IAAI,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;AAChE,IAAI,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;AACpD,IAAI,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC;AAClC,MAAM,oBAAoB,EAAE,SAAS,GAAG,SAAS,CAAC,WAAW,CAAC,cAAc,GAAG,SAAS,CAAC,WAAW,CAAC,cAAc;AACnH,MAAM,UAAU,EAAE,SAAS,CAAC,WAAW,CAAC,eAAe;AACvD,KAAK,CAAC,CAAC;AACP,IAAI,mBAAmB,CAAC,SAAS,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AAC5D,IAAI,IAAI,WAAW,CAAC,YAAY,EAAE;AAClC,MAAM,WAAW,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC;AAC3C,KAAK;AACL,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,SAAS,CAAC,WAAW,CAAC,cAAc,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;AAC5E,KAAK;AACL,IAAI,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC;AAC9B,IAAI,SAAS,CAAC,WAAW,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAChE,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,EAAE,yBAAyB,CAAC,WAAW,EAAE,qBAAqB,EAAE;AAChE,IAAI,IAAI,WAAW,CAAC,iBAAiB,EAAE;AACvC,MAAM,IAAI,CAAC,WAAW,CAAC,kBAAkB;AACzC,QAAQ,OAAO;AACf,MAAM,qBAAqB,GAAG,WAAW,CAAC;AAC1C,KAAK;AACL,IAAI,WAAW,CAAC,gCAAgC,GAAG,qBAAqB,CAAC;AACzE,IAAI,KAAK,IAAI,CAAC,GAAG,WAAW,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AAC1E,MAAM,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC,CAAC,EAAE,qBAAqB,CAAC,CAAC;AAChG,KAAK;AACL,IAAI,WAAW,CAAC,kBAAkB,EAAE,CAAC;AACrC,IAAI,IAAI,WAAW,CAAC,iBAAiB,EAAE;AACvC,MAAM,IAAI,WAAW,CAAC,kBAAkB,EAAE;AAC1C,QAAQ,MAAM,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;AACzD,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;AACtB,QAAQ,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC;AAChD,QAAQ,IAAI,WAAW,CAAC,OAAO,EAAE;AACjC,UAAU,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACzD,SAAS;AACT,QAAQ,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACxC,QAAQ,MAAM,UAAU,GAAG,WAAW,CAAC,cAAc,CAAC,UAAU,IAAI,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC;AAC7F,QAAQ,MAAM,SAAS,GAAG,WAAW,CAAC,cAAc,CAAC,SAAS,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC;AAC1F,QAAQ,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC,iBAAiB;AAC3D,UAAU,MAAM,CAAC,KAAK;AACtB,UAAU,MAAM,CAAC,MAAM;AACvB,UAAU,UAAU;AACpB,UAAU,SAAS;AACnB,SAAS,CAAC;AACV,QAAQ,WAAW,CAAC,cAAc,KAAK,WAAW,CAAC,cAAc,GAAG,IAAI,MAAM,EAAE,CAAC,CAAC;AAClF,QAAQ,WAAW,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACpD,QAAQ,IAAI,WAAW,KAAK,WAAW,CAAC,OAAO,EAAE;AACjD,UAAU,IAAI,WAAW,CAAC,iBAAiB,EAAE;AAC7C,YAAY,WAAW,CAAC,iBAAiB,CAAC,kBAAkB,GAAG,IAAI,CAAC;AACpE,WAAW;AACX,SAAS;AACT,OAAO;AACP,KAAK,MAAM,IAAI,WAAW,CAAC,OAAO,EAAE;AACpC,MAAM,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACrD,MAAM,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC;AACjC,KAAK;AACL,GAAG;AACH,EAAE,mBAAmB,CAAC,WAAW,EAAE;AACnC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;AAC7C,IAAI,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACtC,IAAI,WAAW,CAAC,cAAc,CAAC,WAAW,GAAG,WAAW,CAAC;AACzD,IAAI,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE;AACzC,MAAM,mBAAmB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AACpD,KAAK,MAAM;AACX,MAAM,SAAS,CAAC,WAAW,CAAC,2BAA2B,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;AACjE,KAAK;AACL,IAAI,2BAA2B,CAAC,WAAW,CAAC,CAAC;AAC7C,IAAI,IAAI,WAAW,CAAC,kBAAkB,EAAE;AACxC,MAAM,WAAW,CAAC,kBAAkB,GAAG,KAAK,CAAC;AAC7C,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;AACrD,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;AAC3C,KAAK;AACL,IAAI,WAAW,CAAC,2BAA2B,CAAC,KAAK,GAAG,CAAC,CAAC;AACtD,IAAI,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;AAClE,IAAI,IAAI,WAAW,CAAC,iBAAiB,IAAI,CAAC,WAAW,CAAC,kBAAkB;AACxE,MAAM,OAAO;AACb,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrE,MAAM,IAAI,CAAC,mBAAmB,CAAC,WAAW,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;AACnE,KAAK;AACL,GAAG;AACH,EAAE,kBAAkB,CAAC,WAAW,EAAE;AAClC,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,WAAW,CAAC,2BAA2B,CAAC;AACpE,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;AACpC,MAAM,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAChC,MAAM,IAAI,SAAS,CAAC,aAAa,EAAE;AACnC,QAAQ,WAAW,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;AAChD,OAAO;AACP,KAAK;AACL,IAAI,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAC3B,GAAG;AACH,EAAE,kBAAkB,CAAC,WAAW,EAAE,eAAe,EAAE;AACnD,IAAI,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;AAClC,IAAI,MAAM,cAAc,GAAG,WAAW,CAAC,cAAc,CAAC;AACtD,IAAI,cAAc,CAAC,KAAK,EAAE,CAAC;AAC3B,IAAI,MAAM,QAAQ,GAAG,eAAe,CAAC,WAAW,GAAG,eAAe,GAAG,eAAe,CAAC,KAAK,CAAC,QAAQ,CAAC;AACpG,IAAI,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC;AAC7C,IAAI,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC;AACjD,IAAI,WAAW,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;AACvC,IAAI,WAAW,CAAC,SAAS,CAAC,UAAU,EAAE,CAAC;AACvC,IAAI,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAC/B,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;AAC1B,KAAK;AACL,IAAI,IAAI,CAAC,6BAA6B,CAAC,cAAc,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;AACvE,IAAI,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;AAC/C,IAAI,WAAW,CAAC,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;AACnD,GAAG;AACH,CAAC;AACD,iBAAiB,CAAC,SAAS,GAAG;AAC9B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,YAAY;AAC9B,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,aAAa;AACrB,CAAC,CAAC;AACF,MAAM,UAAU,CAAC;AACjB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,cAAc,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC9D,IAAI,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrE,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,cAAc,CAAC,IAAI,EAAE,gBAAgB,CAAC,CAAC;AACvE,GAAG;AACH,EAAE,aAAa,CAAC,MAAM,EAAE,cAAc,EAAE;AACxC,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AACjD,IAAI,IAAI,MAAM,CAAC,aAAa;AAC5B,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AACrD,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;AAC3E,GAAG;AACH,EAAE,gBAAgB,CAAC,MAAM,EAAE;AAC3B,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AACtD,IAAI,IAAI,MAAM,CAAC,aAAa;AAC5B,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AACrD,IAAI,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;AAChD,GAAG;AACH,EAAE,kBAAkB,CAAC,MAAM,EAAE;AAC7B,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AACjD,IAAI,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,qBAAqB;AACpD,MAAM,SAAS;AACf,MAAM,MAAM,CAAC,QAAQ;AACrB,KAAK,CAAC;AACN,GAAG;AACH,EAAE,iBAAiB,CAAC,MAAM,EAAE;AAC5B,IAAI,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC5D,IAAI,OAAO,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;AAC3C,IAAI,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AAC1D,GAAG;AACH,EAAE,sBAAsB,CAAC,MAAM,EAAE,eAAe,EAAE;AAClD,IAAI,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC;AACjD,IAAI,eAAe,CAAC,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC9C,GAAG;AACH,EAAE,aAAa,CAAC,MAAM,EAAE;AACxB,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AAC1E,GAAG;AACH,EAAE,cAAc,CAAC,MAAM,EAAE;AACzB,IAAI,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;AACzD,IAAI,eAAe,CAAC,UAAU,GAAG,MAAM,CAAC;AACxC,IAAI,eAAe,CAAC,SAAS,GAAG,MAAM,CAAC,cAAc,CAAC;AACtD,IAAI,eAAe,CAAC,OAAO,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC9C,IAAI,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC;AACjD,IAAI,eAAe,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;AACpF,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,eAAe,CAAC;AACtD,IAAI,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;AACzD,IAAI,OAAO,eAAe,CAAC;AAC3B,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE;AACzC,MAAM,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,KAAK;AACL,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,CAAC;AACD,UAAU,CAAC,SAAS,GAAG;AACvB,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,UAAU;AAC5B,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,QAAQ;AAChB,CAAC,CAAC;AACF,MAAM,iBAAiB,GAAG,MAAM,kBAAkB,CAAC;AACnD,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAClC,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACzC,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACvC,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACnB,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,GAAG,EAAE,GAAG,kBAAkB,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;AACnE,IAAI,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,CAAC;AACvD,IAAI,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,eAAe,IAAI,IAAI,CAAC,gBAAgB,CAAC;AACxF,IAAI,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,eAAe,CAAC;AACzC,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;AAC5D,GAAG;AACH;AACA,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC;AACjC,GAAG;AACH,EAAE,IAAI,KAAK,CAAC,KAAK,EAAE;AACnB,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC1C,GAAG;AACH;AACA,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;AACvC,GAAG;AACH,EAAE,IAAI,KAAK,CAAC,KAAK,EAAE;AACnB,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC1C,GAAG;AACH;AACA,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;AAC3C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,GAAG;AACH,CAAC,CAAC;AACF,iBAAiB,CAAC,SAAS,GAAG;AAC9B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,YAAY;AAC9B,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,YAAY;AACpB,EAAE,QAAQ,EAAE,CAAC;AACb,CAAC,CAAC;AACF,iBAAiB,CAAC,cAAc,GAAG;AACnC;AACA;AACA;AACA;AACA,EAAE,eAAe,EAAE,CAAC;AACpB;AACA;AACA;AACA;AACA,EAAE,eAAe,EAAE,CAAC;AACpB;AACA;AACA;AACA;AACA,EAAE,iBAAiB,EAAE,IAAI;AACzB,CAAC,CAAC;AACF,IAAI,gBAAgB,GAAG,iBAAiB,CAAC;AACzC,MAAM,kBAAkB,GAAG,EAAE,CAAC;AAC9B,UAAU,CAAC,MAAM,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,KAAK,KAAK;AACtD,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;AACnB,IAAI,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;AACrE,GAAG;AACH,EAAE,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;AAC7C,CAAC,EAAE,CAAC,KAAK,KAAK;AACd,EAAE,OAAO,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACxC,CAAC,CAAC,CAAC;AACH,MAAM,aAAa,CAAC;AACpB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAC7B,IAAI,IAAI,CAAC,WAAW,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC/C,GAAG;AACH,EAAE,SAAS,GAAG;AACd,IAAI,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC;AACrC,IAAI,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE;AACtD,IAAI,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE;AAC7C,MAAM,IAAI,IAAI,CAAC,WAAW;AAC1B,QAAQ,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC9C,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;AACtC,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;AAC1B,MAAM,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;AACjD,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,kBAAkB,CAAC,SAAS,CAAC,CAAC;AACvD,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;AAC1B,MAAM,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;AACnD,MAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC5C,KAAK;AACL,GAAG;AACH,EAAE,uBAAuB,CAAC,cAAc,EAAE;AAC1C,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC3D,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAC5C,IAAI,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE;AACxC,MAAM,IAAI,CAAC,CAAC,6BAA6B,EAAE,SAAS,CAAC,iEAAiE,CAAC,CAAC,CAAC;AACzH,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AACnD,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,GAAG,IAAI,YAAY,EAAE,CAAC;AACtE,MAAM,YAAY,CAAC,OAAO,GAAG,CAAC,IAAI,kBAAkB,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;AACnE,KAAK;AACL,IAAI,MAAM,WAAW,GAAG;AACxB,MAAM,YAAY,EAAE,QAAQ;AAC5B,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,WAAW,EAAE,EAAE;AACrB,MAAM,YAAY;AAClB,MAAM,SAAS,EAAE,KAAK;AACtB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,WAAW,CAAC;AACnD,IAAI,cAAc,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;AACpC,GAAG;AACH,EAAE,qBAAqB,CAAC,cAAc,EAAE;AACxC,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;AAC3D,IAAI,cAAc,CAAC,GAAG,CAAC;AACvB,MAAM,YAAY,EAAE,QAAQ;AAC5B,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,SAAS,EAAE,KAAK;AACtB,KAAK,CAAC,CAAC;AACP,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;AAC7B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,cAAc,EAAE;AAC3B,IAAI,IAAI,IAAI,CAAC,WAAW,EAAE;AAC1B,MAAM,IAAI,CAAC,qBAAqB,CAAC,cAAc,CAAC,CAAC;AACjD,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC;AAChC,IAAI,KAAK,MAAM,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE;AACtC,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AACpC,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B,GAAG;AACH,CAAC;AACD,aAAa,CAAC,SAAS,GAAG;AAC1B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,UAAU;AAC5B,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,WAAW;AAC7B,GAAG;AACH,EAAE,IAAI,EAAE,WAAW;AACnB,CAAC,CAAC;AACF,MAAM,UAAU,GAAG;AACnB,EAAE,GAAG,EAAE,WAAW;AAClB,EAAE,GAAG,EAAE,YAAY;AACnB,EAAE,IAAI,EAAE,YAAY;AACpB,CAAC,CAAC;AACF,MAAM,cAAc,GAAG,MAAM,eAAe,CAAC;AAC7C;AACA,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH,EAAE,iBAAiB,CAAC,OAAO,EAAE,QAAQ,GAAG,EAAE,EAAE;AAC5C,IAAI,IAAI,OAAO,YAAY,SAAS,IAAI,OAAO,YAAY,OAAO,EAAE;AACpE,MAAM,OAAO;AACb,QAAQ,MAAM,EAAE,OAAO;AACvB,QAAQ,GAAG,QAAQ;AACnB,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO;AACX,MAAM,GAAG,QAAQ;AACjB,MAAM,GAAG,OAAO;AAChB,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,KAAK,CAAC,OAAO,EAAE;AACvB,IAAI,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC;AAC9B,IAAI,KAAK,CAAC,GAAG,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC3C,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,MAAM,CAAC,OAAO,EAAE;AACxB,IAAI,OAAO,GAAG,IAAI,CAAC,iBAAiB;AACpC,MAAM,OAAO;AACb,MAAM,eAAe,CAAC,mBAAmB;AACzC,KAAK,CAAC;AACN,IAAI,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;AACxC,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACxC,IAAI,IAAI,MAAM,CAAC,MAAM,KAAK,KAAK,CAAC,EAAE;AAClC,MAAM,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;AAC9C,QAAQ,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK;AAChC,UAAU,IAAI,CAAC,IAAI,EAAE;AACrB,YAAY,MAAM,CAAC,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC,CAAC;AACxD,YAAY,OAAO;AACnB,WAAW;AACX,UAAU,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;AAC1C,UAAU,MAAM,CAAC,MAAM,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACvD,UAAU,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC;AAClC,UAAU,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AACrC,SAAS,EAAE,UAAU,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;AACxC,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,SAAS,KAAK,KAAK,CAAC,EAAE;AACrC,MAAM,OAAO,MAAM,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;AAC3D,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,aAAa,KAAK,KAAK,CAAC,EAAE;AACzC,MAAM,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,EAAE,IAAI,EAAE,UAAU,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC;AACrF,MAAM,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;AAC9C,QAAQ,MAAM,MAAM,GAAG,IAAI,UAAU,EAAE,CAAC;AACxC,QAAQ,MAAM,CAAC,MAAM,GAAG,MAAM,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AACrD,QAAQ,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC;AAChC,QAAQ,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;AACnC,OAAO,CAAC,CAAC;AACT,KAAK;AACL,IAAI,MAAM,IAAI,KAAK,CAAC,yGAAyG,CAAC,CAAC;AAC/H,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,OAAO,EAAE;AAClB,IAAI,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;AAC9C,IAAI,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AAClC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,IAAI,MAAM,YAAY,OAAO,EAAE;AACnC,MAAM,OAAO,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;AACrD,KAAK;AACL,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,gBAAgB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AACvE,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;AAC5D,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC1B,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,OAAO,EAAE;AAClB,IAAI,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;AAC9C,IAAI,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;AAClC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;AACpC,IAAI,MAAM,OAAO,GAAG,MAAM,YAAY,OAAO,GAAG,MAAM,GAAG,QAAQ,CAAC,gBAAgB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AAC5G,IAAI,MAAM,SAAS,GAAG,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;AAC1D,IAAI,IAAI,MAAM,YAAY,SAAS,EAAE;AACrC,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC5B,KAAK;AACL,IAAI,OAAO,SAAS,CAAC;AACrB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,OAAO,EAAE;AACnB,IAAI,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;AAC9C,IAAI,IAAI,OAAO,CAAC,MAAM,YAAY,OAAO;AACzC,MAAM,OAAO,OAAO,CAAC,MAAM,CAAC;AAC5B,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AACpE,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,QAAQ,CAAC,OAAO,EAAE;AACpB,IAAI,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;AAC9C,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACxC,IAAI,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AAC7C,IAAI,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,IAAI,WAAW,CAAC;AACpD,IAAI,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;AAC9C,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;AACjB,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACpC,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,GAAG,CAAC,OAAO,EAAE;AACf,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,GAAG,CAAC;AACvC,IAAI,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;AAC9C,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACxC,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;AACtC,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;AACvE,IAAI,MAAM,KAAK,GAAG;AAClB,MAAM,iBAAiB;AACvB,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC;AACrC,MAAM,CAAC,gBAAgB,EAAE,MAAM,CAAC,YAAY,CAAC;AAC7C,MAAM,2BAA2B;AACjC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAChB,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC9B,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,CAAC,CAAC;AACF,cAAc,CAAC,SAAS,GAAG;AAC3B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,SAAS;AACjB,CAAC,CAAC;AACF,cAAc,CAAC,mBAAmB,GAAG;AACrC;AACA,EAAE,MAAM,EAAE,KAAK;AACf;AACA,EAAE,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AACF,IAAI,aAAa,GAAG,cAAc,CAAC;AACnC,MAAM,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAC;AACjC,MAAM,UAAU,GAAG,IAAI,MAAM,EAAE,CAAC;AAChC,MAAM,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAC7B,MAAM,qBAAqB,CAAC;AAC5B,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,OAAO,EAAE;AAC3B,IAAI,IAAI,OAAO,YAAY,SAAS,EAAE;AACtC,MAAM,OAAO,GAAG;AAChB,QAAQ,MAAM,EAAE,OAAO;AACvB,QAAQ,KAAK,EAAE,KAAK,CAAC;AACrB,QAAQ,oBAAoB,EAAE,EAAE;AAChC,QAAQ,UAAU,EAAE,KAAK,CAAC;AAC1B,OAAO,CAAC;AACR,KAAK;AACL,IAAI,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;AACvE,IAAI,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;AACzE,IAAI,MAAM,SAAS,GAAG,OAAO,CAAC,MAAM,CAAC;AACrC,IAAI,IAAI,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;AACxC,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC;AAC/E,MAAM,UAAU,GAAG,WAAW,GAAG,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAO,EAAE,CAAC;AAC1F,KAAK,MAAM;AACX,MAAM,UAAU,GAAG,OAAO,CAAC;AAC3B,KAAK;AACL,IAAI,MAAM,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,cAAc,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,SAAS,CAAC;AACtG,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AAC9D,IAAI,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AAChE,IAAI,MAAM,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;AACxC,MAAM,GAAG,OAAO,CAAC,oBAAoB;AACrC,MAAM,KAAK,EAAE,MAAM,CAAC,KAAK;AACzB,MAAM,MAAM,EAAE,MAAM,CAAC,MAAM;AAC3B,MAAM,UAAU;AAChB,MAAM,SAAS;AACf,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACpE,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;AAC1B,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,MAAM;AACZ,MAAM,UAAU;AAChB,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,CAAC,MAAM,CAAC,aAAa,EAAE,CAAC;AAClC,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,CAAC;AACD,qBAAqB,CAAC,SAAS,GAAG;AAClC,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,kBAAkB;AAC1B,CAAC,CAAC;AACF,MAAM,mBAAmB,CAAC;AAC1B,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;AACzB,IAAI,IAAI,CAAC,uBAAuB,GAAG,EAAE,CAAC;AACtC,IAAI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AAC5B,IAAI,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;AAC9B,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;AAC7B,IAAI,IAAI,CAAC,iBAAiB,GAAG,EAAE,CAAC;AAChC,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH,EAAE,KAAK,GAAG;AACV,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;AACzB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1D,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,KAAK;AACL,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5D,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;AAC1D,KAAK;AACL,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,CAAC;AACtC,GAAG;AACH,EAAE,KAAK,CAAC,OAAO,EAAE;AACjB,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;AACjB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACvB,GAAG;AACH,EAAE,IAAI,CAAC;AACP,IAAI,IAAI;AACR,IAAI,gBAAgB;AACpB,IAAI,oBAAoB;AACxB,IAAI,UAAU;AACd,IAAI,MAAM;AACV,GAAG,EAAE;AACL,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,YAAY,CAAC;AAClE,IAAI,MAAM,wBAAwB,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG;AAC7G,MAAM,cAAc,EAAE,YAAY;AAClC,MAAM,oBAAoB,EAAE,IAAI,MAAM,EAAE;AACxC,MAAM,UAAU,EAAE,UAAU;AAC5B,MAAM,MAAM,EAAE,IAAI,KAAK,EAAE;AACzB,KAAK,CAAC;AACN,IAAI,MAAM,iBAAiB,GAAG;AAC9B,MAAM,gBAAgB,EAAE,gBAAgB,IAAI,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,gBAAgB;AACxF,MAAM,UAAU,EAAE,IAAI,IAAI,YAAY,CAAC,IAAI;AAC3C,MAAM,oBAAoB,EAAE,oBAAoB,IAAI,wBAAwB,CAAC,oBAAoB;AACjG,MAAM,UAAU,EAAE,UAAU,IAAI,wBAAwB,CAAC,UAAU;AACnE,MAAM,MAAM,EAAE,MAAM,IAAI,wBAAwB,CAAC,MAAM;AACvD,MAAM,SAAS,EAAE,IAAI;AACrB,KAAK,CAAC;AACN,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,eAAe,EAAE,CAAC;AAC5E,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC5C,IAAI,MAAM,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;AAC3C,IAAI,QAAQ,CAAC,iBAAiB,GAAG,iBAAiB,CAAC,gBAAgB,CAAC;AACpE,IAAI,QAAQ,CAAC,WAAW,GAAG,iBAAiB,CAAC,UAAU,CAAC;AACxD,IAAI,QAAQ,CAAC,qBAAqB,CAAC,QAAQ,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,CAAC;AACpF,IAAI,QAAQ,CAAC,qBAAqB,CAAC,EAAE,IAAI,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;AACpE,IAAI,QAAQ,CAAC,qBAAqB,CAAC,EAAE,IAAI,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC;AACpE,IAAI,mBAAmB;AACvB,MAAM,iBAAiB,CAAC,UAAU;AAClC,MAAM,QAAQ,CAAC,gBAAgB;AAC/B,MAAM,CAAC;AACP,KAAK,CAAC;AACN,IAAI,YAAY,CAAC,MAAM,EAAE,CAAC;AAC1B,IAAI,IAAI,SAAS,CAAC;AAClB,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE;AACjD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,YAAY,CAAC,mBAAmB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;AACnG,KAAK,MAAM;AACX,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,IAAI,IAAI,SAAS,EAAE,CAAC;AAC/D,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAC7C,MAAM,SAAS,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC;AAC7C,KAAK;AACL,IAAI,iBAAiB,CAAC,SAAS,GAAG,SAAS,CAAC;AAC5C,IAAI,IAAI,CAAC,yBAAyB,GAAG,iBAAiB,CAAC;AACvD,GAAG;AACH,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACvB,IAAI,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,yBAAyB,CAAC;AACtF,GAAG;AACH,EAAE,GAAG,GAAG;AACR,IAAI,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,uBAAuB,CAAC,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;AAC1F,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,YAAY,CAAC,KAAK,EAAE;AACpD,MAAM,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC;AACrE,KAAK;AACL,GAAG;AACH,EAAE,IAAI,SAAS,GAAG;AAClB,IAAI,OAAO,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC;AACpD,GAAG;AACH,EAAE,IAAI,iBAAiB,GAAG;AAC1B,IAAI,OAAO,IAAI,CAAC,yBAAyB,CAAC;AAC1C,GAAG;AACH,EAAE,IAAI,YAAY,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AACjE,GAAG;AACH,EAAE,eAAe,GAAG;AACpB,IAAI,MAAM,cAAc,GAAG,IAAI,YAAY,CAAC;AAC5C,MAAM,iBAAiB,EAAE,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;AACrE,MAAM,qBAAqB,EAAE,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;AACzE;AACA,MAAM,gBAAgB,EAAE,EAAE,KAAK,EAAE,IAAI,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AACzE,MAAM,WAAW,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;AACvD,KAAK,EAAE;AACP,MAAM,QAAQ,EAAE,IAAI;AACpB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,cAAc,CAAC;AAC1B,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,CAAC;AACD,mBAAmB,CAAC,SAAS,GAAG;AAChC,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,YAAY;AAC9B,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,gBAAgB;AACxB,CAAC,CAAC;AACF,IAAI,GAAG,GAAG,CAAC,CAAC;AACZ,MAAM,eAAe,CAAC;AACtB,EAAE,WAAW,GAAG;AAChB,IAAI,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;AACrB,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;AACrB,GAAG;AACH;AACA,EAAE,IAAI,GAAG;AACT,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,IAAI,EAAE,QAAQ,EAAE,SAAS,GAAG,IAAI,EAAE;AAC3C,IAAI,MAAM,EAAE,GAAG,GAAG,EAAE,CAAC;AACrB,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,IAAI,CAAC,OAAO,IAAI,GAAG,CAAC;AAC1B,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;AAC5B,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;AACrB,MAAM,IAAI;AACV,MAAM,QAAQ;AACd,MAAM,KAAK,EAAE,WAAW,CAAC,GAAG,EAAE;AAC9B,MAAM,MAAM;AACZ,MAAM,IAAI,EAAE,WAAW,CAAC,GAAG,EAAE;AAC7B,MAAM,MAAM,EAAE,IAAI;AAClB,MAAM,EAAE;AACR,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,EAAE,EAAE;AACb,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;AACpC,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACjC,QAAQ,OAAO;AACf,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AAClC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,MAAM,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAClC,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC1D,QAAQ,MAAM,OAAO,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;AACzC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC;AACxB,OAAO;AACP,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AAC7C,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AAC3B,GAAG;AACH,CAAC;AACD,eAAe,CAAC,SAAS,GAAG;AAC5B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,YAAY;AAC9B,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,QAAQ,EAAE,CAAC;AACb,CAAC,CAAC;AACF,IAAI,SAAS,GAAG,KAAK,CAAC;AACtB,SAAS,QAAQ,CAAC,IAAI,EAAE;AACxB,EAAE,IAAI,SAAS,EAAE;AACjB,IAAI,OAAO;AACX,GAAG;AACH,EAAE,IAAI,UAAU,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;AACtF,IAAI,MAAM,IAAI,GAAG;AACjB,MAAM,CAAC,8BAA8B,EAAE,OAAO,CAAC,EAAE,EAAE,IAAI,CAAC;AACxD;AACA,CAAC;AACD,MAAM,qCAAqC;AAC3C,MAAM,qCAAqC;AAC3C,MAAM,qCAAqC;AAC3C,MAAM,qCAAqC;AAC3C,MAAM,qDAAqD;AAC3D,MAAM,qDAAqD;AAC3D,KAAK,CAAC;AACN,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;AACpC,GAAG,MAAM,IAAI,UAAU,CAAC,OAAO,EAAE;AACjC,IAAI,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,yBAAyB,CAAC,CAAC,CAAC;AACnF,GAAG;AACH,EAAE,SAAS,GAAG,IAAI,CAAC;AACnB,CAAC;AACD,MAAM,WAAW,CAAC;AAClB,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE;AACvB,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;AACrC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,YAAY,CAAC,KAAK,EAAE;AACtD,QAAQ,IAAI,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;AAC1D,OAAO;AACP,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAC;AACrB,KAAK;AACL,GAAG;AACH,CAAC;AACD,WAAW,CAAC,SAAS,GAAG;AACxB,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,YAAY;AAC9B,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,QAAQ,EAAE,CAAC,CAAC;AACd,CAAC,CAAC;AACF,WAAW,CAAC,cAAc,GAAG;AAC7B;AACA,EAAE,KAAK,EAAE,KAAK;AACd,CAAC,CAAC;AACF,SAAS,SAAS,CAAC,IAAI,EAAE;AACzB,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC;AACpB,EAAE,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE;AACxB,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE;AAC3B,MAAM,KAAK,GAAG,IAAI,CAAC;AACnB,MAAM,MAAM;AACZ,KAAK;AACL,GAAG;AACH,EAAE,IAAI,CAAC,KAAK;AACZ,IAAI,OAAO,IAAI,CAAC;AAChB,EAAE,MAAM,UAAU,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACzD,EAAE,KAAK,MAAM,CAAC,IAAI,IAAI,EAAE;AACxB,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1B,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,UAAU,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;AAC5B,KAAK;AACL,GAAG;AACH,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC;AACD,SAAS,UAAU,CAAC,GAAG,EAAE;AACzB,EAAE,IAAI,MAAM,GAAG,CAAC,CAAC;AACjB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACvC,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,EAAE;AAC1B,MAAM,MAAM,EAAE,CAAC;AACf,KAAK,MAAM;AACX,MAAM,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/B,KAAK;AACL,GAAG;AACH,EAAE,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC;AACvB,EAAE,OAAO,GAAG,CAAC;AACb,CAAC;AACD,IAAI,gBAAgB,GAAG,CAAC,CAAC;AACzB,MAAM,mBAAmB,GAAG,MAAM,oBAAoB,CAAC;AACvD;AACA;AACA;AACA;AACA,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;AAClC,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;AAC7B,IAAI,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;AAC7B,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,GAAG,EAAE,GAAG,oBAAoB,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;AACrE,IAAI,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,yBAAyB,CAAC;AAC3D,IAAI,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,qBAAqB,CAAC;AACpD,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,kBAAkB,CAAC;AAC9C,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,GAAG;AAChB,IAAI,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;AAC3B,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,IAAI,OAAO,CAAC,KAAK,EAAE;AACrB,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK;AAC9B,MAAM,OAAO;AACb,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM;AACrD,QAAQ,MAAM,IAAI,CAAC,GAAG,EAAE;AACxB,QAAQ,IAAI,CAAC,UAAU;AACvB,QAAQ,KAAK;AACb,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM;AACzD,QAAQ,MAAM;AACd,UAAU,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,cAAc,EAAE;AAClD,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AACzE,WAAW;AACX,SAAS;AACT,QAAQ,IAAI,CAAC,UAAU;AACvB,OAAO,CAAC;AACR,MAAM,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM;AAC1D,QAAQ,MAAM;AACd,UAAU,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,cAAc,EAAE;AACnD,YAAY,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;AAClD,WAAW;AACX,SAAS;AACT,QAAQ,IAAI,CAAC,UAAU;AACvB,OAAO,CAAC;AACR,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACrD,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AACzD,MAAM,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC1D,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE;AAChC,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;AAChD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,eAAe,CAAC,OAAO,EAAE,IAAI,EAAE;AACjC,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;AAChD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,SAAS,CAAC;AACZ,IAAI,SAAS;AACb,GAAG,EAAE;AACL,IAAI,IAAI,CAAC,IAAI,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AAClC,IAAI,SAAS,CAAC,WAAW,CAAC,MAAM,GAAG,gBAAgB,EAAE,CAAC;AACtD,IAAI,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,WAAW,EAAE,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;AACvF,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,aAAa,CAAC,UAAU,EAAE;AAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO;AACrB,MAAM,OAAO;AACb,IAAI,IAAI,UAAU,CAAC,SAAS,KAAK,CAAC,CAAC,EAAE;AACrC,MAAM,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAChD,MAAM,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;AACjE,KAAK;AACL,IAAI,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC;AACrC,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,GAAG,GAAG;AACR,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;AAC1B,IAAI,MAAM,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC;AACxD,IAAI,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;AACnD,IAAI,IAAI,MAAM,GAAG,CAAC,CAAC;AACnB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxD,MAAM,MAAM,UAAU,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAC/C,MAAM,IAAI,UAAU,KAAK,IAAI,EAAE;AAC/B,QAAQ,MAAM,EAAE,CAAC;AACjB,QAAQ,SAAS;AACjB,OAAO;AACP,MAAM,MAAM,WAAW,GAAG,UAAU,CAAC,WAAW,IAAI,UAAU,CAAC,iBAAiB,CAAC;AACjF,MAAM,MAAM,WAAW,GAAG,WAAW,EAAE,cAAc,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;AACpE,MAAM,IAAI,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,MAAM,WAAW,EAAE;AACtD,QAAQ,UAAU,CAAC,SAAS,GAAG,GAAG,CAAC;AACnC,OAAO;AACP,MAAM,IAAI,GAAG,GAAG,UAAU,CAAC,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE;AAC3D,QAAQ,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE;AACnC,UAAU,MAAM,EAAE,GAAG,WAAW,CAAC;AACjC,UAAU,IAAI,WAAW;AACzB,YAAY,WAAW,CAAC,kBAAkB,GAAG,IAAI,CAAC;AAClD,UAAU,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;AACpE,SAAS;AACT,QAAQ,UAAU,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC;AAClC,QAAQ,MAAM,EAAE,CAAC;AACjB,QAAQ,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;AAClE,OAAO,MAAM;AACb,QAAQ,kBAAkB,CAAC,CAAC,GAAG,MAAM,CAAC,GAAG,UAAU,CAAC;AACpD,OAAO;AACP,KAAK;AACL,IAAI,kBAAkB,CAAC,MAAM,IAAI,MAAM,CAAC;AACxC,GAAG;AACH;AACA,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;AACzB,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC;AACxC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;AACnC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,CAAC;AACnC,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,iBAAiB,CAAC,UAAU,EAAE;AAChC,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;AAC/D,IAAI,IAAI,KAAK,IAAI,CAAC,EAAE;AACpB,MAAM,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;AAChE,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;AAC7C,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,wBAAwB,CAAC,WAAW,EAAE,MAAM,EAAE;AAChD,IAAI,WAAW,CAAC,cAAc,CAAC,MAAM,GAAG,MAAM,CAAC;AAC/C,IAAI,KAAK,MAAM,KAAK,IAAI,WAAW,CAAC,mBAAmB,EAAE;AACzD,MAAM,IAAI,CAAC,wBAAwB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACnD,KAAK;AACL,GAAG;AACH,CAAC,CAAC;AACF,mBAAmB,CAAC,SAAS,GAAG;AAChC,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,cAAc;AACtB,EAAE,QAAQ,EAAE,CAAC;AACb,CAAC,CAAC;AACF,mBAAmB,CAAC,cAAc,GAAG;AACrC;AACA,EAAE,kBAAkB,EAAE,IAAI;AAC1B;AACA,EAAE,yBAAyB,EAAE,GAAG;AAChC;AACA,EAAE,qBAAqB,EAAE,GAAG;AAC5B,CAAC,CAAC;AACF,IAAI,kBAAkB,GAAG,mBAAmB,CAAC;AAC7C,MAAM,gBAAgB,GAAG,MAAM,iBAAiB,CAAC;AACjD;AACA,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;AAC9B,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACnB,IAAI,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AACxB,GAAG;AACH,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,GAAG,EAAE,GAAG,iBAAiB,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;AAClE,IAAI,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,sBAAsB,CAAC;AACxD,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,iBAAiB,IAAI,OAAO,CAAC,gBAAgB,CAAC;AACzE,IAAI,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,eAAe,CAAC;AAC1C,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE;AAC3C,MAAM,OAAO;AACb,KAAK;AACL,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;AACjB,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM;AACpB,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;AACtB,IAAI,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE;AAC9C,MAAM,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;AAC1B,MAAM,IAAI,CAAC,GAAG,EAAE,CAAC;AACjB,KAAK;AACL,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,GAAG,GAAG;AACR,IAAI,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,eAAe,CAAC;AACnE,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACrD,MAAM,MAAM,OAAO,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;AACzC,MAAM,IAAI,OAAO,CAAC,kBAAkB,IAAI,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,QAAQ,GAAG,IAAI,CAAC,OAAO,EAAE;AACnI,QAAQ,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;AAC9B,QAAQ,OAAO,CAAC,MAAM,EAAE,CAAC;AACzB,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,OAAO,GAAG;AACZ,IAAI,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;AAC1B,GAAG;AACH,CAAC,CAAC;AACF,gBAAgB,CAAC,SAAS,GAAG;AAC7B,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,WAAW;AACnB,CAAC,CAAC;AACF,gBAAgB,CAAC,cAAc,GAAG;AAClC;AACA;AACA;AACA;AACA,EAAE,eAAe,EAAE,IAAI;AACvB;AACA;AACA;AACA;AACA,EAAE,iBAAiB,EAAE,IAAI;AACzB;AACA;AACA;AACA;AACA,EAAE,gBAAgB,EAAE,EAAE,GAAG,EAAE;AAC3B;AACA;AACA;AACA;AACA,EAAE,sBAAsB,EAAE,GAAG;AAC7B,CAAC,CAAC;AACF,IAAI,eAAe,GAAG,gBAAgB,CAAC;AACvC,MAAM,WAAW,GAAG,MAAM,YAAY,CAAC;AACvC;AACA;AACA;AACA;AACA,EAAE,IAAI,WAAW,GAAG;AACpB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC;AAC3C,GAAG;AACH,EAAE,IAAI,WAAW,CAAC,KAAK,EAAE;AACzB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;AAC5C,GAAG;AACH;AACA,EAAE,IAAI,UAAU,GAAG;AACnB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,CAAC;AAC3C,GAAG;AACH,EAAE,IAAI,UAAU,CAAC,KAAK,EAAE;AACxB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM;AAC9B,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK;AAC/B,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM;AAChC,MAAM,KAAK;AACX,KAAK,CAAC;AACN,GAAG;AACH;AACA;AACA;AACA;AACA,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB,IAAI,OAAO,GAAG;AACd,MAAM,GAAG,YAAY,CAAC,cAAc;AACpC,MAAM,GAAG,OAAO;AAChB,KAAK,CAAC;AACN,IAAI,IAAI,OAAO,CAAC,IAAI,EAAE;AACtB,MAAM,WAAW,CAAC,MAAM,EAAE,uDAAuD,CAAC,CAAC;AACnF,MAAM,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;AACpC,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;AACrE,IAAI,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,UAAU,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,CAAC;AACpE,IAAI,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC;AACzC,IAAI,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;AAC1D,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,CAAC;AACzC,MAAM,aAAa,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;AACnC,MAAM,KAAK,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK;AAC5B,MAAM,MAAM,EAAE,IAAI;AAClB,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,WAAW,GAAG,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC;AAClE,IAAI,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;AACzC,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,UAAU,EAAE;AAC9D,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,UAAU,CAAC,CAAC;AACpF,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;AACjD,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC;AACnD,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,OAAO,CAAC,OAAO,GAAG,KAAK,EAAE;AAC3B,IAAI,MAAM,UAAU,GAAG,OAAO,OAAO,KAAK,SAAS,GAAG,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,UAAU,CAAC;AACtF,IAAI,IAAI,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;AAC9C,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACtD,KAAK;AACL,GAAG;AACH,CAAC,CAAC;AACF,WAAW,CAAC,SAAS,GAAG;AACxB,EAAE,IAAI,EAAE;AACR,IAAI,aAAa,CAAC,WAAW;AAC7B,IAAI,aAAa,CAAC,YAAY;AAC9B,IAAI,aAAa,CAAC,YAAY;AAC9B,GAAG;AACH,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,QAAQ,EAAE,CAAC;AACb,CAAC,CAAC;AACF,WAAW,CAAC,cAAc,GAAG;AAC7B;AACA;AACA;AACA;AACA,EAAE,KAAK,EAAE,GAAG;AACZ;AACA;AACA;AACA;AACA,EAAE,MAAM,EAAE,GAAG;AACb;AACA;AACA;AACA;AACA,EAAE,WAAW,EAAE,KAAK;AACpB;AACA;AACA;AACA;AACA,EAAE,SAAS,EAAE,KAAK;AAClB,CAAC,CAAC;AACF,IAAI,UAAU,GAAG,WAAW,CAAC;AACxB,MAAC,aAAa,GAAG;AACtB,EAAE,gBAAgB;AAClB,EAAE,mBAAmB;AACrB,EAAE,WAAW;AACb,EAAE,UAAU;AACZ,EAAE,iBAAiB;AACnB,EAAE,eAAe;AACjB,EAAE,qBAAqB;AACvB,EAAE,aAAa;AACf,EAAE,gBAAgB;AAClB,EAAE,kBAAkB;AACpB,EAAE,eAAe;AACjB,EAAE;AACG,MAAC,iBAAiB,GAAG;AAC1B,EAAE,aAAa;AACf,EAAE,WAAW;AACb,EAAE,UAAU;AACZ,EAAE,eAAe;AACjB,EAAE,aAAa;AACf,EAAE,eAAe;AACjB,EAAE,aAAa;AACf,EAAE,gBAAgB;AAClB;;;;"}