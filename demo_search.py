"""
DTrans模型搜索功能演示
展示智能搜索和过滤功能的使用方法
"""

from app import DTransApp

def demo_search_examples():
    """演示各种搜索示例"""
    print("🔍 DTrans智能模型搜索功能演示")
    print("=" * 60)
    
    app = DTransApp()
    all_models = app.load_models()
    
    print(f"📊 总共加载了 {len(all_models)} 个模型")
    print()
    
    # 搜索示例
    search_examples = [
        {
            "query": "claude",
            "description": "搜索Claude系列模型"
        },
        {
            "query": "gpt-4",
            "description": "搜索GPT-4系列模型"
        },
        {
            "query": "快速",
            "description": "搜索快速模型（按标签）"
        },
        {
            "query": "经济",
            "description": "搜索经济实惠的模型"
        },
        {
            "query": "长上下文",
            "description": "搜索支持长上下文的模型"
        },
        {
            "query": "高质量 推理",
            "description": "多关键词搜索：高质量且推理能力强"
        },
        {
            "query": "anthropic",
            "description": "按提供商搜索：Anthropic"
        },
        {
            "query": "gemini",
            "description": "搜索Google Gemini系列"
        }
    ]
    
    for example in search_examples:
        query = example["query"]
        description = example["description"]
        
        print(f"🔍 {description}")
        print(f"   搜索词: \"{query}\"")
        
        filtered_models = app.filter_models(query, all_models)
        print(f"   结果: {len(filtered_models)} 个模型")
        
        if filtered_models:
            print("   前3个匹配结果:")
            for i, (model_display, model_id) in enumerate(filtered_models[:3], 1):
                # 获取模型详细信息
                model_info = None
                for model in all_models:
                    if model['id'] == model_id:
                        model_info = model
                        break
                
                tags = ""
                if model_info and model_info.get('tags'):
                    tags = f" [{', '.join(model_info['tags'])}]"
                
                print(f"     {i}. {model_display}{tags}")
            
            if len(filtered_models) > 3:
                print(f"     ... 还有 {len(filtered_models) - 3} 个模型")
        else:
            print("     (无匹配结果)")
        
        print()

def demo_model_recommendations():
    """演示模型推荐"""
    print("💡 模型选择建议")
    print("=" * 60)
    
    recommendations = [
        {
            "scenario": "复杂文档翻译",
            "search": "claude",
            "reason": "Claude系列在长文本理解和专业术语处理方面表现优秀"
        },
        {
            "scenario": "快速翻译任务",
            "search": "快速",
            "reason": "GPT-3.5系列响应速度快，成本较低"
        },
        {
            "scenario": "高质量翻译",
            "search": "gpt-4",
            "reason": "GPT-4系列在翻译质量和准确性方面表现出色"
        },
        {
            "scenario": "长文档处理",
            "search": "长上下文",
            "reason": "支持长上下文的模型能处理更大的文档"
        },
        {
            "scenario": "成本敏感场景",
            "search": "经济",
            "reason": "经济型模型在保证基本质量的同时降低成本"
        }
    ]
    
    app = DTransApp()
    all_models = app.load_models()
    
    for rec in recommendations:
        print(f"📋 场景: {rec['scenario']}")
        print(f"   推荐搜索: \"{rec['search']}\"")
        print(f"   原因: {rec['reason']}")
        
        filtered_models = app.filter_models(rec['search'], all_models)
        if filtered_models:
            top_model = filtered_models[0]
            print(f"   推荐模型: {top_model[0]}")
        
        print()

def demo_search_tips():
    """演示搜索技巧"""
    print("🎯 搜索技巧和提示")
    print("=" * 60)
    
    tips = [
        {
            "tip": "使用模型名称快速定位",
            "examples": ["claude", "gpt-4", "gemini", "llama"]
        },
        {
            "tip": "按特性搜索找到合适模型",
            "examples": ["快速", "经济", "高质量", "长上下文"]
        },
        {
            "tip": "按提供商搜索查看所有选项",
            "examples": ["anthropic", "openai", "google", "meta"]
        },
        {
            "tip": "组合关键词精确搜索",
            "examples": ["高质量 推理", "快速 经济", "长上下文 claude"]
        },
        {
            "tip": "使用版本号搜索特定版本",
            "examples": ["3.5", "4.0", "2024"]
        }
    ]
    
    for tip_info in tips:
        print(f"💡 {tip_info['tip']}")
        print(f"   示例: {', '.join(tip_info['examples'])}")
        print()

def main():
    """主演示函数"""
    print("🌐 DTrans - 智能模型搜索功能演示")
    print("=" * 70)
    print()
    
    try:
        demo_search_examples()
        demo_model_recommendations()
        demo_search_tips()
        
        print("🎉 演示完成！")
        print()
        print("💡 使用提示:")
        print("- 启动应用后，在模型搜索框中输入任何关键词")
        print("- 搜索结果会实时更新，并显示匹配的模型数量")
        print("- 选择模型后会显示详细信息和使用建议")
        print("- 支持中英文关键词搜索")
        print()
        print("🚀 运行 'python app.py' 或 'start_app.bat' 启动完整应用")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
