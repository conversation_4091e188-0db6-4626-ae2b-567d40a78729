# DTrans 项目结构说明

## 📁 项目目录结构

```
DTrans/
├── 📄 app.py                    # 主应用文件 - Gradio Web界面
├── 📄 requirements.txt          # Python依赖列表
├── 📄 .env.example             # 环境变量示例文件
├── 📄 README.md                # 项目说明文档
├── 📄 项目结构说明.md           # 本文件
├── 📄 test_app.py              # 应用测试脚本
├── 📄 install.bat              # 自动安装脚本
├── 📄 start_app.bat            # 应用启动脚本 (CMD)
├── 📄 start_app.ps1            # 应用启动脚本 (PowerShell)
├── 📁 dtrans_env/              # Python虚拟环境目录
│   ├── Scripts/                # 虚拟环境可执行文件
│   ├── Lib/                    # 虚拟环境库文件
│   └── Include/                # 虚拟环境头文件
└── 📁 utils/                   # 工具模块目录
    ├── __init__.py             # 包初始化文件
    ├── openrouter_client.py    # OpenRouter API客户端
    ├── document_parser.py      # 文档解析器
    └── translator.py           # 翻译引擎
```

## 📋 文件功能说明

### 核心应用文件

- **app.py**: 主应用入口，包含Gradio Web界面的完整实现
- **requirements.txt**: 列出所有Python依赖包及版本要求
- **.env.example**: 环境变量配置模板，需要复制为.env并设置API密钥

### 工具模块 (utils/)

- **openrouter_client.py**: 
  - 与OpenRouter API通信
  - 获取可用模型列表
  - 执行文本翻译请求

- **document_parser.py**:
  - 解析.docx和.pdf文件
  - 提取文档段落结构
  - 分析参考文件的翻译对应关系
  - 生成翻译后的.docx文档

- **translator.py**:
  - 翻译引擎核心逻辑
  - 整合参考文件分析和翻译执行
  - 管理翻译进度和状态

### 辅助脚本

- **install.bat**: 一键安装脚本，自动创建虚拟环境并安装依赖
- **start_app.bat**: CMD启动脚本，激活虚拟环境并启动应用
- **start_app.ps1**: PowerShell启动脚本，功能同上
- **test_app.py**: 测试脚本，验证应用各模块是否正常工作

### 虚拟环境 (dtrans_env/)

- 独立的Python运行环境
- 包含所有项目依赖
- 避免与系统Python环境冲突

## 🚀 快速开始流程

1. **自动安装**: 运行 `install.bat`
2. **配置API**: 编辑 `.env` 文件设置OpenRouter API密钥
3. **启动应用**: 运行 `start_app.bat`
4. **访问界面**: 打开浏览器访问 `http://localhost:7860`

## 🔧 开发说明

### 添加新功能

1. 在 `utils/` 目录下创建新的模块文件
2. 在 `app.py` 中导入并集成新功能
3. 更新 `requirements.txt` 如果需要新的依赖
4. 在 `test_app.py` 中添加相应测试

### 修改界面

- 主要界面代码在 `app.py` 的 `create_interface()` 方法中
- 使用Gradio组件构建用户界面
- 事件处理逻辑也在同一文件中

### 扩展翻译功能

- 修改 `utils/translator.py` 添加新的翻译策略
- 在 `utils/document_parser.py` 中支持新的文档格式
- 在 `utils/openrouter_client.py` 中添加新的API集成

## 📦 依赖说明

### 主要依赖

- **gradio**: Web界面框架
- **openai**: OpenAI API客户端（兼容OpenRouter）
- **python-docx**: Word文档处理
- **PyPDF2**: PDF文档处理
- **requests**: HTTP请求库
- **python-dotenv**: 环境变量管理

### 辅助依赖

- **pandas**: 数据处理
- **numpy**: 数值计算
- **tqdm**: 进度条显示

## 🛠️ 故障排除

### 常见问题

1. **虚拟环境创建失败**: 确保Python版本3.8+
2. **依赖安装失败**: 检查网络连接，尝试使用国内镜像源
3. **应用启动失败**: 检查.env文件配置，确保API密钥正确
4. **文档解析错误**: 确认文件格式支持，检查文件是否损坏

### 调试模式

- 运行 `test_app.py` 检查各模块状态
- 查看控制台输出的详细错误信息
- 检查虚拟环境是否正确激活

## 📞 技术支持

如遇到问题，请：
1. 首先运行测试脚本诊断问题
2. 查看控制台错误信息
3. 检查项目文档和代码注释
4. 提交Issue描述具体问题
