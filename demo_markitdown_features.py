"""
DTrans MarkItDown功能演示
展示多格式文档处理和Markdown转换功能
"""

import os
import tempfile
from utils.markitdown_parser import MarkItDownParser

def demo_document_conversion():
    """演示文档转换功能"""
    print("🔄 文档转换功能演示")
    print("=" * 60)
    
    parser = MarkItDownParser()
    
    # 创建不同格式的测试文档
    test_documents = [
        {
            "name": "HTML文档",
            "content": """<!DOCTYPE html>
<html>
<head>
    <title>测试HTML文档</title>
</head>
<body>
    <h1>公司年报</h1>
    <h2>财务概况</h2>
    <p>本公司在报告期内实现营业收入<strong>100万元</strong>，同比增长15%。</p>
    
    <h2>业务发展</h2>
    <ul>
        <li>技术创新</li>
        <li>市场拓展</li>
        <li>产品优化</li>
    </ul>
    
    <table border="1">
        <tr>
            <th>项目</th>
            <th>金额（万元）</th>
        </tr>
        <tr>
            <td>营业收入</td>
            <td>100</td>
        </tr>
        <tr>
            <td>净利润</td>
            <td>15</td>
        </tr>
    </table>
</body>
</html>""",
            "extension": ".html"
        },
        {
            "name": "JSON数据",
            "content": """{
    "company": "测试公司",
    "report": {
        "year": 2024,
        "revenue": 1000000,
        "growth": 0.15,
        "departments": [
            {
                "name": "技术部",
                "employees": 50,
                "budget": 200000
            },
            {
                "name": "销售部", 
                "employees": 30,
                "budget": 150000
            }
        ]
    },
    "risks": [
        "市场风险",
        "技术风险",
        "政策风险"
    ]
}""",
            "extension": ".json"
        },
        {
            "name": "CSV数据",
            "content": """部门,员工数,预算
技术部,50,200000
销售部,30,150000
财务部,10,80000
人事部,8,60000""",
            "extension": ".csv"
        }
    ]
    
    for doc in test_documents:
        print(f"\n📄 转换 {doc['name']}:")
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(
            mode='w', 
            suffix=doc['extension'], 
            delete=False, 
            encoding='utf-8'
        ) as f:
            f.write(doc['content'])
            temp_file = f.name
        
        try:
            # 转换为Markdown
            markdown_content = parser.convert_to_markdown(temp_file)
            
            print(f"   ✅ 转换成功，内容长度: {len(markdown_content)} 字符")
            print(f"   📝 Markdown预览 (前200字符):")
            print(f"   {markdown_content[:200]}...")
            
        except Exception as e:
            print(f"   ❌ 转换失败: {e}")
        
        finally:
            # 清理临时文件
            if os.path.exists(temp_file):
                os.unlink(temp_file)

def demo_markdown_output():
    """演示Markdown输出功能"""
    print("\n📝 Markdown输出功能演示")
    print("=" * 60)
    
    parser = MarkItDownParser()
    
    # 创建示例翻译内容
    translation_content = """# 翻译文档

## 文档信息
- **原文件**: annual_report.pdf
- **文件格式**: .pdf
- **原文字数**: 1500 词
- **原文字符数**: 8500 字符
- **翻译时间**: 2024-01-15 14:30:00
- **翻译模型**: anthropic/claude-3-sonnet-20240229

## 翻译统计
- **参考翻译对**: 25 组
- **术语对照**: 15 个

---

# 翻译内容

## Annual Report

### Financial Overview

The Company achieved revenue of **1 million yuan** during the reporting period, representing a year-on-year growth of 15%.

### Business Development

We focus on:
- Technological innovation
- Market expansion  
- Product optimization

### Financial Data

| Item | Amount (10k RMB) |
|------|------------------|
| Revenue | 100 |
| Net Profit | 15 |

### Risk Warning

Investors should be aware of market risks and carefully read this report.

---

*Translation completed by DTrans AI Translation System*
"""
    
    # 创建Markdown文件
    output_path = os.path.join(tempfile.gettempdir(), "demo_translation.md")
    
    try:
        created_file = parser.create_markdown_file(translation_content, output_path)
        print(f"✅ Markdown文件创建成功: {created_file}")
        
        # 验证文件内容
        with open(created_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📊 文件信息:")
        print(f"   - 文件大小: {len(content)} 字符")
        print(f"   - 行数: {len(content.splitlines())} 行")
        
        print(f"\n📝 文件内容预览 (前300字符):")
        print(content[:300] + "...")
        
        # 清理文件
        if os.path.exists(created_file):
            os.unlink(created_file)
            print(f"🧹 清理临时文件完成")
        
    except Exception as e:
        print(f"❌ 创建Markdown文件失败: {e}")

def demo_document_structure_analysis():
    """演示文档结构分析功能"""
    print("\n🔍 文档结构分析演示")
    print("=" * 60)
    
    parser = MarkItDownParser()
    
    # 创建复杂的测试文档
    complex_content = """# 香港上市公司年度报告

## 执行摘要

本报告概述了公司2024年度的经营情况和财务表现。

### 主要亮点

- 营业收入增长15%
- 净利润率提升2个百分点
- 成功拓展海外市场

## 第一章 公司概况

### 1.1 公司简介

本公司成立于2010年，是一家专注于技术创新的企业。

### 1.2 主营业务

#### 1.2.1 产品线A
专注于人工智能解决方案。

#### 1.2.2 产品线B
提供云计算服务。

## 第二章 财务分析

### 2.1 收入分析

本年度总收入达到1000万港币。

### 2.2 成本分析

运营成本控制在合理范围内。

## 第三章 风险管理

### 3.1 市场风险

密切关注市场变化。

### 3.2 技术风险

持续投入研发以降低技术风险。

## 结论

公司发展前景良好。
"""
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(
        mode='w', 
        suffix='.txt', 
        delete=False, 
        encoding='utf-8'
    ) as f:
        f.write(complex_content)
        temp_file = f.name
    
    try:
        # 解析文档
        doc_info = parser.parse_document(temp_file)
        
        print(f"📊 文档基本信息:")
        print(f"   - 文件名: {doc_info['file_name']}")
        print(f"   - 字数: {doc_info['word_count']}")
        print(f"   - 字符数: {doc_info['char_count']}")
        print(f"   - 结构段落数: {len(doc_info['sections'])}")
        
        print(f"\n📋 文档结构层次:")
        for i, section in enumerate(doc_info['sections'], 1):
            level = section.get('level', 0)
            title = section.get('title', '无标题')
            word_count = section.get('word_count', 0)
            indent = "  " * (level - 1)
            print(f"   {i:2d}. {indent}{'#' * level} {title} ({word_count} 词)")
        
    except Exception as e:
        print(f"❌ 文档分析失败: {e}")
    
    finally:
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)

def main():
    """主演示函数"""
    print("🌐 DTrans MarkItDown功能完整演示")
    print("=" * 70)
    print()
    
    try:
        # 演示文档转换
        demo_document_conversion()
        
        # 演示Markdown输出
        demo_markdown_output()
        
        # 演示文档结构分析
        demo_document_structure_analysis()
        
        print("\n🎉 演示完成！")
        print("\n💡 MarkItDown集成功能特点:")
        print("- ✅ 支持多种文件格式（PDF、Word、Excel、PowerPoint、HTML等）")
        print("- ✅ 自动转换为Markdown格式")
        print("- ✅ 保持文档结构和格式")
        print("- ✅ 智能文档结构分析")
        print("- ✅ 翻译结果Markdown输出")
        print("- ✅ 支持预览和下载")
        print()
        print("🚀 启动完整应用: python app.py")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
