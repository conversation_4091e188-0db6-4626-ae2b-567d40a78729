{"version": 3, "file": "_page.svelte-BVjg9E0t.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/entries/pages/_...catchall_/_page.svelte.js"], "sourcesContent": ["import { create_ssr_component, subscribe, add_attribute, validate_component, missing_component } from \"svelte/internal\";\nimport { $ as $format, s as setupi18n, C as Client, E as Embed } from \"../../../chunks/client.js\";\nimport { writable } from \"svelte/store\";\nimport { createEventDispatcher, onMount, onDestroy } from \"svelte\";\nimport { p as page } from \"../../../chunks/stores.js\";\nimport { b as browser } from \"../../../chunks/index5.js\";\nvar inject_fonts = () => {\n  const source_sans_pro = document.createElement(\"link\");\n  source_sans_pro.href = \"https://fonts.googleapis.com/css2?family=Source+Sans+Pro:ital,wght@0,200;0,300;0,400;0,600;0,700;0,900;1,200;1,300;1,400;1,600;1,700;1,900&display=swap\";\n  source_sans_pro.rel = \"stylesheet\";\n  const ibm_mono = document.createElement(\"link\");\n  ibm_mono.href = \"https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:wght@400;600;700&display=swap\";\n  ibm_mono.rel = \"stylesheet\";\n  document.head.appendChild(source_sans_pro);\n  document.head.appendChild(ibm_mono);\n};\nvar Box = () => {\n  const box = document.createElement(\"div\");\n  box.style.backgroundImage = \"linear-gradient(to top, #f9fafb, white)\";\n  box.style.border = \"1px solid #e5e7eb\";\n  box.style.borderRadius = \"0.75rem\";\n  box.style.boxShadow = \"0 0 10px rgba(0, 0, 0, 0.1)\";\n  box.style.color = \"#374151\";\n  box.style.display = \"flex\";\n  box.style.flexDirection = \"row\";\n  box.style.alignItems = \"center\";\n  box.style.height = \"40px\";\n  box.style.justifyContent = \"space-between\";\n  box.style.overflow = \"hidden\";\n  box.style.position = \"fixed\";\n  box.style.right = \".75rem\";\n  box.style.top = \".75rem\";\n  box.style.width = \"auto\";\n  box.style.zIndex = \"20\";\n  box.style.paddingLeft = \"1rem\";\n  box.setAttribute(\"id\", \"huggingface-space-header\");\n  window.matchMedia(\"(max-width: 768px)\").addEventListener(\"change\", (e) => {\n    if (e.matches) {\n      box.style.display = \"none\";\n    } else {\n      box.style.display = \"flex\";\n    }\n  });\n  return box;\n};\nvar ArrowCollapse = () => {\n  const arrow = document.createElementNS(\"http://www.w3.org/2000/svg\", \"svg\");\n  arrow.setAttribute(\"xmlns\", \"http://www.w3.org/2000/svg\");\n  arrow.setAttribute(\"xmlns:link\", \"http://www.w3.org/1999/xlink\");\n  arrow.setAttribute(\"aria-hidden\", \"true\");\n  arrow.setAttribute(\"focusable\", \"false\");\n  arrow.setAttribute(\"role\", \"img\");\n  arrow.setAttribute(\"width\", \"1em\");\n  arrow.setAttribute(\"height\", \"1em\");\n  arrow.setAttribute(\"preserveAspectRatio\", \"xMidYMid meet\");\n  arrow.setAttribute(\"viewBox\", \"0 0 12 12\");\n  arrow.setAttribute(\"fill\", \"currentColor\");\n  const path = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n  path.setAttribute(\n    \"d\",\n    \"M0.375001 10.3828L0.375 1.61719C0.375 1.104 0.816001 0.687501 1.35938 0.687501L10.6406 0.6875C10.9017 0.6875 11.1521 0.785449 11.3367 0.959797C11.5213 1.13415 11.625 1.37062 11.625 1.61719V10.3828C11.625 10.6294 11.5213 10.8659 11.3367 11.0402C11.1521 11.2145 10.9017 11.3125 10.6406 11.3125H1.35938C0.816001 11.3125 0.375001 10.896 0.375001 10.3828ZM1.35938 10.5156H10.6406C10.7183 10.5156 10.7813 10.4561 10.7813 10.3828V4.40625H1.21875V10.3828C1.21875 10.418 1.23356 10.4518 1.25994 10.4767C1.28631 10.5017 1.32208 10.5156 1.35938 10.5156ZM4.61052 6.38251L5.9999 7.69472L7.38927 6.38251C7.44083 6.33007 7.50645 6.29173 7.57913 6.27153C7.6518 6.25134 7.72898 6.25003 7.8024 6.26776C7.87583 6.28549 7.9428 6.3216 7.99628 6.37227C8.04983 6.42295 8.08785 6.48631 8.10645 6.5557C8.12528 6.62497 8.12393 6.69773 8.10263 6.76635C8.0814 6.83497 8.0409 6.8969 7.98555 6.94564L6.29802 8.53936C6.21892 8.61399 6.11169 8.65592 5.9999 8.65592C5.8881 8.65592 5.78087 8.61399 5.70177 8.53936L4.01427 6.94564C3.95874 6.89694 3.91814 6.835 3.89676 6.76633C3.87538 6.69766 3.874 6.62483 3.89277 6.55549C3.91154 6.48615 3.94977 6.42287 4.00343 6.37233C4.05708 6.32179 4.12418 6.28585 4.19765 6.2683C4.27098 6.25054 4.34803 6.25178 4.42068 6.27188C4.49334 6.29198 4.55891 6.3302 4.61052 6.38251Z\"\n  );\n  arrow.appendChild(path);\n  return arrow;\n};\nvar Collapse = (space, callback) => {\n  const box = document.createElement(\"div\");\n  box.setAttribute(\"id\", \"space-header__collapse\");\n  box.style.display = \"flex\";\n  box.style.flexDirection = \"row\";\n  box.style.alignItems = \"center\";\n  box.style.justifyContent = \"center\";\n  box.style.fontSize = \"16px\";\n  box.style.paddingLeft = \"10px\";\n  box.style.paddingRight = \"10px\";\n  box.style.height = \"40px\";\n  box.style.cursor = \"pointer\";\n  box.style.color = \"#40546e\";\n  box.style.transitionDuration = \"0.1s\";\n  box.style.transitionProperty = \"all\";\n  box.style.transitionTimingFunction = \"ease-in-out\";\n  box.appendChild(ArrowCollapse());\n  box.addEventListener(\"click\", (e) => {\n    e.preventDefault();\n    e.stopPropagation();\n    callback();\n  });\n  box.addEventListener(\"mouseenter\", () => {\n    box.style.color = \"#213551\";\n  });\n  box.addEventListener(\"mouseleave\", () => {\n    box.style.color = \"#40546e\";\n  });\n  return box;\n};\nvar Count = (count) => {\n  const text = document.createElement(\"p\");\n  text.style.margin = \"0\";\n  text.style.padding = \"0\";\n  text.style.color = \"#9ca3af\";\n  text.style.fontSize = \"14px\";\n  text.style.fontFamily = \"Source Sans Pro, sans-serif\";\n  text.style.padding = \"0px 6px\";\n  text.style.borderLeft = \"1px solid #e5e7eb\";\n  text.style.marginLeft = \"4px\";\n  text.textContent = (count ?? 0).toString();\n  return text;\n};\nvar Heart = () => {\n  const heart = document.createElementNS(\"http://www.w3.org/2000/svg\", \"svg\");\n  heart.setAttribute(\"xmlns\", \"http://www.w3.org/2000/svg\");\n  heart.setAttribute(\"xmlns:link\", \"http://www.w3.org/1999/xlink\");\n  heart.setAttribute(\"aria-hidden\", \"true\");\n  heart.setAttribute(\"focusable\", \"false\");\n  heart.setAttribute(\"role\", \"img\");\n  heart.setAttribute(\"width\", \"1em\");\n  heart.setAttribute(\"height\", \"1em\");\n  heart.setAttribute(\"preserveAspectRatio\", \"xMidYMid meet\");\n  heart.setAttribute(\"viewBox\", \"0 0 32 32\");\n  heart.setAttribute(\"fill\", \"#6b7280\");\n  const path = document.createElementNS(\"http://www.w3.org/2000/svg\", \"path\");\n  path.setAttribute(\n    \"d\",\n    \"M22.45,6a5.47,5.47,0,0,1,3.91,1.64,5.7,5.7,0,0,1,0,8L16,26.13,5.64,15.64a5.7,5.7,0,0,1,0-8,5.48,5.48,0,0,1,7.82,0L16,10.24l2.53-2.58A5.44,5.44,0,0,1,22.45,6m0-2a7.47,7.47,0,0,0-5.34,2.24L16,7.36,14.89,6.24a7.49,7.49,0,0,0-10.68,0,7.72,7.72,0,0,0,0,10.82L16,29,27.79,17.06a7.72,7.72,0,0,0,0-10.82A7.49,7.49,0,0,0,22.45,4Z\"\n  );\n  heart.appendChild(path);\n  return heart;\n};\nvar Like = (space) => {\n  const box = document.createElement(\"a\");\n  box.setAttribute(\"href\", `https://huggingface.co/spaces/${space.id}`);\n  box.setAttribute(\"rel\", \"noopener noreferrer\");\n  box.setAttribute(\"target\", \"_blank\");\n  box.style.border = \"1px solid #e5e7eb\";\n  box.style.borderRadius = \"6px\";\n  box.style.display = \"flex\";\n  box.style.flexDirection = \"row\";\n  box.style.alignItems = \"center\";\n  box.style.margin = \"0 0 0 12px\";\n  box.style.fontSize = \"14px\";\n  box.style.paddingLeft = \"4px\";\n  box.style.textDecoration = \"none\";\n  box.appendChild(Heart());\n  box.appendChild(Count(space.likes));\n  return box;\n};\nvar Avatar = (username) => {\n  const element = document.createElement(\"img\");\n  element.src = `https://huggingface.co/api/users/${username}/avatar`;\n  element.style.width = \"0.875rem\";\n  element.style.height = \"0.875rem\";\n  element.style.borderRadius = \"50%\";\n  element.style.flex = \"none\";\n  element.style.marginRight = \"0.375rem\";\n  return element;\n};\nvar Namespace = (id2) => {\n  const [_, spaceName] = id2.split(\"/\");\n  const element = document.createElement(\"a\");\n  element.setAttribute(\"href\", `https://huggingface.co/spaces/${id2}`);\n  element.setAttribute(\"rel\", \"noopener noreferrer\");\n  element.setAttribute(\"target\", \"_blank\");\n  element.style.color = \"#1f2937\";\n  element.style.textDecoration = \"none\";\n  element.style.fontWeight = \"600\";\n  element.style.fontSize = \"15px\";\n  element.style.lineHeight = \"24px\";\n  element.style.flex = \"none\";\n  element.style.fontFamily = \"IBM Plex Mono, sans-serif\";\n  element.addEventListener(\"mouseover\", () => {\n    element.style.color = \"#2563eb\";\n  });\n  element.addEventListener(\"mouseout\", () => {\n    element.style.color = \"#1f2937\";\n  });\n  element.textContent = spaceName;\n  return element;\n};\nvar Separation = () => {\n  const separation = document.createElement(\"div\");\n  separation.style.marginLeft = \".125rem\";\n  separation.style.marginRight = \".125rem\";\n  separation.style.color = \"#d1d5db\";\n  separation.textContent = \"/\";\n  return separation;\n};\nvar Username = (username) => {\n  const element = document.createElement(\"a\");\n  element.setAttribute(\"href\", `https://huggingface.co/${username}`);\n  element.setAttribute(\"rel\", \"noopener noreferrer\");\n  element.setAttribute(\"target\", \"_blank\");\n  element.style.color = \"rgb(107, 114, 128)\";\n  element.style.textDecoration = \"none\";\n  element.style.fontWeight = \"400\";\n  element.style.fontSize = \"16px\";\n  element.style.lineHeight = \"24px\";\n  element.style.flex = \"none\";\n  element.style.fontFamily = \"Source Sans Pro, sans-serif\";\n  element.addEventListener(\"mouseover\", () => {\n    element.style.color = \"#2563eb\";\n  });\n  element.addEventListener(\"mouseout\", () => {\n    element.style.color = \"rgb(107, 114, 128)\";\n  });\n  element.textContent = username;\n  return element;\n};\nvar Content = (space) => {\n  const content = document.createElement(\"div\");\n  content.style.display = \"flex\";\n  content.style.flexDirection = \"row\";\n  content.style.alignItems = \"center\";\n  content.style.justifyContent = \"center\";\n  content.style.borderRight = \"1px solid #e5e7eb\";\n  content.style.paddingRight = \"12px\";\n  content.style.height = \"40px\";\n  content.appendChild(Avatar(space.author));\n  content.appendChild(Username(space.author));\n  content.appendChild(Separation());\n  content.appendChild(Namespace(space.id));\n  content.appendChild(Like(space));\n  return content;\n};\nvar create = (space) => {\n  const box = Box();\n  const handleCollapse = () => box.style.display = \"none\";\n  box.appendChild(Content(space));\n  box.appendChild(Collapse(space, handleCollapse));\n  return box;\n};\nvar get_space = async (space_id) => {\n  try {\n    const response = await fetch(`https://huggingface.co/api/spaces/${space_id}`);\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    return null;\n  }\n};\nvar inject = (element, options) => {\n  if (document.body === null) {\n    return console.error(\"document.body is null\");\n  }\n  document.body.appendChild(element);\n};\nasync function main(initialSpace, options) {\n  if (window === void 0)\n    return console.error(\"Please run this script in a browser environment\");\n  const has_huggingface_ancestor = Object.values(\n    window.location?.ancestorOrigins ?? {\n      0: window.document.referrer\n    }\n  ).some((origin) => new URL(origin)?.origin === \"https://huggingface.co\");\n  if (has_huggingface_ancestor)\n    return;\n  inject_fonts();\n  let space;\n  if (typeof initialSpace === \"string\") {\n    space = await get_space(initialSpace);\n    if (space === null)\n      return console.error(\"Space not found\");\n  } else {\n    space = initialSpace;\n  }\n  const mini_header_element = create(space);\n  inject(mini_header_element);\n  return {\n    element: mini_header_element\n  };\n}\nvar init = (space, options) => main(space);\nlet id = -1;\nfunction create_intersection_store() {\n  const intersecting = writable({});\n  const els = /* @__PURE__ */ new Map();\n  const observer = new IntersectionObserver((entries) => {\n    entries.forEach((entry) => {\n      if (entry.isIntersecting) {\n        let _el = els.get(entry.target);\n        if (_el !== void 0)\n          intersecting.update((s) => ({ ...s, [_el]: true }));\n      }\n    });\n  });\n  function register(_id, el) {\n    els.set(el, _id);\n    observer.observe(el);\n  }\n  return {\n    register,\n    subscribe: intersecting.subscribe\n  };\n}\nlet loader_status = \"complete\";\nasync function add_custom_html_head(head_string) {\n  if (head_string) {\n    const parser = new DOMParser();\n    const parsed_head_html = Array.from(parser.parseFromString(head_string, \"text/html\").head.children);\n    if (parsed_head_html) {\n      for (let head_element of parsed_head_html) {\n        let newElement = document.createElement(head_element.tagName);\n        Array.from(head_element.attributes).forEach((attr) => {\n          newElement.setAttribute(attr.name, attr.value);\n        });\n        newElement.textContent = head_element.textContent;\n        if (newElement.tagName == \"META\") {\n          const propertyAttr = newElement.getAttribute(\"property\");\n          const nameAttr = newElement.getAttribute(\"name\");\n          if (propertyAttr || nameAttr) {\n            const domMetaList = Array.from(document.head.getElementsByTagName(\"meta\") ?? []);\n            const matched = domMetaList.find((el) => {\n              if (propertyAttr && el.getAttribute(\"property\") === propertyAttr) {\n                return !el.isEqualNode(newElement);\n              }\n              if (nameAttr && el.getAttribute(\"name\") === nameAttr) {\n                return !el.isEqualNode(newElement);\n              }\n              return false;\n            });\n            if (matched) {\n              document.head.replaceChild(newElement, matched);\n              continue;\n            }\n          }\n        }\n        document.head.appendChild(newElement);\n      }\n    }\n  }\n}\nconst Page = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let config;\n  let $_, $$unsubscribe__;\n  let $page, $$unsubscribe_page;\n  $$unsubscribe__ = subscribe($format, (value) => $_ = value);\n  $$unsubscribe_page = subscribe(page, (value) => $page = value);\n  setupi18n();\n  const dispatch = createEventDispatcher();\n  let { data } = $$props;\n  let { autoscroll = false } = $$props;\n  let { version = \"5-31-0\" } = $$props;\n  let { initial_height } = $$props;\n  let { app_mode = true } = $$props;\n  let { is_embed = false } = $$props;\n  let { theme_mode = null } = $$props;\n  let { control_page_title = true } = $$props;\n  let { container } = $$props;\n  let stream;\n  let active_theme_mode;\n  let { space } = $$props;\n  let _id = id++;\n  let wrapper;\n  let ready = false;\n  let render_complete = false;\n  $_(\"common.loading\") + \"...\";\n  let intersecting = {\n    register: () => {\n    },\n    subscribe: writable({}).subscribe\n  };\n  let app = data.app;\n  function handle_status(_status) {\n  }\n  let pending_deep_link_error = false;\n  onMount(async () => {\n    config = data.config;\n    window.gradio_config = config;\n    window.gradio_config = data.config;\n    config = data.config;\n    if (config.deep_link_state === \"invalid\") {\n      pending_deep_link_error = true;\n    }\n    if (!app.config) {\n      throw new Error(\"Could not resolve app config\");\n    }\n    window.__gradio_space__ = config.space_id;\n    window.__gradio_session_hash__ = app.session_hash;\n    window.__is_colab__ = config.is_colab;\n    await add_custom_html_head(config.head);\n    const supports_zerogpu_headers = \"supports-zerogpu-headers\";\n    window.addEventListener(\"message\", (event) => {\n      if (event.data === supports_zerogpu_headers) {\n        window.supports_zerogpu_headers = true;\n      }\n    });\n    const hostname = window.location.hostname;\n    const origin = hostname.includes(\".dev.\") ? `https://moon-${hostname.split(\".\")[1]}.dev.spaces.huggingface.tech` : `https://huggingface.co`;\n    window.parent.postMessage(supports_zerogpu_headers, origin);\n    dispatch(\"loaded\");\n    if (config.dev_mode) {\n      setTimeout(\n        () => {\n          const { host } = new URL(data.api_url);\n          let url = new URL(`http://${host}${app.api_prefix}/dev/reload`);\n          stream = new EventSource(url);\n          stream.addEventListener(\"error\", async (e) => {\n            new_message_fn(\"Error\", \"Error reloading app\", \"error\");\n            console.error(JSON.parse(e.data));\n          });\n          stream.addEventListener(\"reload\", async (event) => {\n            app.close();\n            app = await Client.connect(data.api_url, {\n              status_callback: handle_status,\n              with_null_state: true,\n              events: [\"data\", \"log\", \"status\", \"render\"]\n            });\n            if (!app.config) {\n              throw new Error(\"Could not resolve app config\");\n            }\n            config = app.config;\n            window.__gradio_space__ = config.space_id;\n          });\n        },\n        200\n      );\n    }\n  });\n  let new_message_fn;\n  onMount(async () => {\n    intersecting = create_intersection_store();\n    intersecting.register(_id, wrapper);\n  });\n  let spaceheader;\n  async function mount_space_header(space_id, is_embed2) {\n    if (space_id && !is_embed2 && window.self === window.top) {\n      if (spaceheader) {\n        spaceheader.remove();\n        spaceheader = void 0;\n      }\n      const header = await init(space_id);\n      if (header)\n        spaceheader = header.element;\n    }\n  }\n  onDestroy(() => {\n    spaceheader?.remove();\n  });\n  if ($$props.data === void 0 && $$bindings.data && data !== void 0)\n    $$bindings.data(data);\n  if ($$props.autoscroll === void 0 && $$bindings.autoscroll && autoscroll !== void 0)\n    $$bindings.autoscroll(autoscroll);\n  if ($$props.version === void 0 && $$bindings.version && version !== void 0)\n    $$bindings.version(version);\n  if ($$props.initial_height === void 0 && $$bindings.initial_height && initial_height !== void 0)\n    $$bindings.initial_height(initial_height);\n  if ($$props.app_mode === void 0 && $$bindings.app_mode && app_mode !== void 0)\n    $$bindings.app_mode(app_mode);\n  if ($$props.is_embed === void 0 && $$bindings.is_embed && is_embed !== void 0)\n    $$bindings.is_embed(is_embed);\n  if ($$props.theme_mode === void 0 && $$bindings.theme_mode && theme_mode !== void 0)\n    $$bindings.theme_mode(theme_mode);\n  if ($$props.control_page_title === void 0 && $$bindings.control_page_title && control_page_title !== void 0)\n    $$bindings.control_page_title(control_page_title);\n  if ($$props.container === void 0 && $$bindings.container && container !== void 0)\n    $$bindings.container(container);\n  if ($$props.space === void 0 && $$bindings.space && space !== void 0)\n    $$bindings.space(space);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    config = data.config;\n    {\n      if (config?.app_id) {\n        config.app_id;\n      }\n    }\n    {\n      if (new_message_fn && pending_deep_link_error) {\n        new_message_fn(\"Error\", \"Deep link was not valid\", \"error\");\n        pending_deep_link_error = false;\n      }\n    }\n    {\n      if (render_complete) {\n        wrapper.dispatchEvent(new CustomEvent(\n          \"render\",\n          {\n            bubbles: true,\n            cancelable: false,\n            composed: true\n          }\n        ));\n      }\n    }\n    app?.config && browser && mount_space_header(app?.config?.space_id, is_embed);\n    $$rendered = `${$$result.head += `<!-- HEAD_svelte-19rwpdo_START --><link rel=\"stylesheet\"${add_attribute(\"href\", \"./theme.css?v=\" + config?.theme_hash, 0)}><link rel=\"manifest\" href=\"/manifest.json\"><!-- HEAD_svelte-19rwpdo_END -->`, \"\"} ${validate_component(Embed, \"Embed\").$$render(\n      $$result,\n      {\n        display: container && is_embed,\n        is_embed,\n        info: false,\n        version,\n        initial_height,\n        space,\n        pages: config.pages,\n        current_page: config.current_page,\n        root: config.root,\n        loaded: loader_status === \"complete\",\n        fill_width: config?.fill_width || false,\n        wrapper\n      },\n      {\n        wrapper: ($$value) => {\n          wrapper = $$value;\n          $$settled = false;\n        }\n      },\n      {\n        default: () => {\n          return `${config?.auth_required ? `${validate_component(data.Render || missing_component, \"svelte:component\").$$render(\n            $$result,\n            {\n              auth_message: config.auth_message,\n              root: config.root,\n              space_id: space,\n              app_mode\n            },\n            {},\n            {}\n          )}` : `${config && app ? `${validate_component(data.Render || missing_component, \"svelte:component\").$$render(\n            $$result,\n            Object.assign(\n              {},\n              { app },\n              config,\n              {\n                fill_height: !is_embed && config.fill_height\n              },\n              { theme_mode: active_theme_mode },\n              { control_page_title },\n              { target: wrapper },\n              { autoscroll },\n              { show_footer: !is_embed },\n              { app_mode },\n              { version },\n              { search_params: $page.url.searchParams },\n              { initial_layout: data.layout },\n              { ready },\n              { render_complete },\n              { add_new_message: new_message_fn }\n            ),\n            {\n              ready: ($$value) => {\n                ready = $$value;\n                $$settled = false;\n              },\n              render_complete: ($$value) => {\n                render_complete = $$value;\n                $$settled = false;\n              },\n              add_new_message: ($$value) => {\n                new_message_fn = $$value;\n                $$settled = false;\n              }\n            },\n            {}\n          )}` : ``}`}`;\n        }\n      }\n    )}`;\n  } while (!$$settled);\n  $$unsubscribe__();\n  $$unsubscribe_page();\n  return $$rendered;\n});\nexport {\n  Page as default\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAMA,IAAI,YAAY,GAAG,MAAM;AACzB,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AACzD,EAAE,eAAe,CAAC,IAAI,GAAG,yJAAyJ,CAAC;AACnL,EAAE,eAAe,CAAC,GAAG,GAAG,YAAY,CAAC;AACrC,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AAClD,EAAE,QAAQ,CAAC,IAAI,GAAG,sFAAsF,CAAC;AACzG,EAAE,QAAQ,CAAC,GAAG,GAAG,YAAY,CAAC;AAC9B,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;AAC7C,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACtC,CAAC,CAAC;AACF,IAAI,GAAG,GAAG,MAAM;AAChB,EAAE,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC5C,EAAE,GAAG,CAAC,KAAK,CAAC,eAAe,GAAG,yCAAyC,CAAC;AACxE,EAAE,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,mBAAmB,CAAC;AACzC,EAAE,GAAG,CAAC,KAAK,CAAC,YAAY,GAAG,SAAS,CAAC;AACrC,EAAE,GAAG,CAAC,KAAK,CAAC,SAAS,GAAG,6BAA6B,CAAC;AACtD,EAAE,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;AAC9B,EAAE,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AAC7B,EAAE,GAAG,CAAC,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC;AAClC,EAAE,GAAG,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;AAClC,EAAE,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AAC5B,EAAE,GAAG,CAAC,KAAK,CAAC,cAAc,GAAG,eAAe,CAAC;AAC7C,EAAE,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAChC,EAAE,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAC;AAC/B,EAAE,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,QAAQ,CAAC;AAC7B,EAAE,GAAG,CAAC,KAAK,CAAC,GAAG,GAAG,QAAQ,CAAC;AAC3B,EAAE,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC;AAC3B,EAAE,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;AAC1B,EAAE,GAAG,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;AACjC,EAAE,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,0BAA0B,CAAC,CAAC;AACrD,EAAE,MAAM,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK;AAC5E,IAAI,IAAI,CAAC,CAAC,OAAO,EAAE;AACnB,MAAM,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AACjC,KAAK,MAAM;AACX,MAAM,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AACjC,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AACF,IAAI,aAAa,GAAG,MAAM;AAC1B,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;AAC9E,EAAE,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,4BAA4B,CAAC,CAAC;AAC5D,EAAE,KAAK,CAAC,YAAY,CAAC,YAAY,EAAE,8BAA8B,CAAC,CAAC;AACnE,EAAE,KAAK,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AAC5C,EAAE,KAAK,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AAC3C,EAAE,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACpC,EAAE,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACrC,EAAE,KAAK,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AACtC,EAAE,KAAK,CAAC,YAAY,CAAC,qBAAqB,EAAE,eAAe,CAAC,CAAC;AAC7D,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;AAC7C,EAAE,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC;AAC7C,EAAE,MAAM,IAAI,GAAG,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,MAAM,CAAC,CAAC;AAC9E,EAAE,IAAI,CAAC,YAAY;AACnB,IAAI,GAAG;AACP,IAAI,gwCAAgwC;AACpwC,GAAG,CAAC;AACJ,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AACF,IAAI,QAAQ,GAAG,CAAC,KAAK,EAAE,QAAQ,KAAK;AACpC,EAAE,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC5C,EAAE,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,wBAAwB,CAAC,CAAC;AACnD,EAAE,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AAC7B,EAAE,GAAG,CAAC,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC;AAClC,EAAE,GAAG,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;AAClC,EAAE,GAAG,CAAC,KAAK,CAAC,cAAc,GAAG,QAAQ,CAAC;AACtC,EAAE,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC;AAC9B,EAAE,GAAG,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;AACjC,EAAE,GAAG,CAAC,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC;AAClC,EAAE,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AAC5B,EAAE,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,SAAS,CAAC;AAC/B,EAAE,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;AAC9B,EAAE,GAAG,CAAC,KAAK,CAAC,kBAAkB,GAAG,MAAM,CAAC;AACxC,EAAE,GAAG,CAAC,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC;AACvC,EAAE,GAAG,CAAC,KAAK,CAAC,wBAAwB,GAAG,aAAa,CAAC;AACrD,EAAE,GAAG,CAAC,WAAW,CAAC,aAAa,EAAE,CAAC,CAAC;AACnC,EAAE,GAAG,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,KAAK;AACvC,IAAI,CAAC,CAAC,cAAc,EAAE,CAAC;AACvB,IAAI,CAAC,CAAC,eAAe,EAAE,CAAC;AACxB,IAAI,QAAQ,EAAE,CAAC;AACf,GAAG,CAAC,CAAC;AACL,EAAE,GAAG,CAAC,gBAAgB,CAAC,YAAY,EAAE,MAAM;AAC3C,IAAI,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;AAChC,GAAG,CAAC,CAAC;AACL,EAAE,GAAG,CAAC,gBAAgB,CAAC,YAAY,EAAE,MAAM;AAC3C,IAAI,GAAG,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;AAChC,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AACF,IAAI,KAAK,GAAG,CAAC,KAAK,KAAK;AACvB,EAAE,MAAM,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AAC3C,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC;AAC1B,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;AAC3B,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;AAC/B,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC;AAC/B,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,6BAA6B,CAAC;AACxD,EAAE,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,SAAS,CAAC;AACjC,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,mBAAmB,CAAC;AAC9C,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;AAChC,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC;AAC7C,EAAE,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AACF,IAAI,KAAK,GAAG,MAAM;AAClB,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;AAC9E,EAAE,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,4BAA4B,CAAC,CAAC;AAC5D,EAAE,KAAK,CAAC,YAAY,CAAC,YAAY,EAAE,8BAA8B,CAAC,CAAC;AACnE,EAAE,KAAK,CAAC,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC,CAAC;AAC5C,EAAE,KAAK,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC;AAC3C,EAAE,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACpC,EAAE,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACrC,EAAE,KAAK,CAAC,YAAY,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AACtC,EAAE,KAAK,CAAC,YAAY,CAAC,qBAAqB,EAAE,eAAe,CAAC,CAAC;AAC7D,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;AAC7C,EAAE,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;AACxC,EAAE,MAAM,IAAI,GAAG,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,MAAM,CAAC,CAAC;AAC9E,EAAE,IAAI,CAAC,YAAY;AACnB,IAAI,GAAG;AACP,IAAI,kUAAkU;AACtU,GAAG,CAAC;AACJ,EAAE,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AACF,IAAI,IAAI,GAAG,CAAC,KAAK,KAAK;AACtB,EAAE,MAAM,GAAG,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AAC1C,EAAE,GAAG,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,8BAA8B,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACxE,EAAE,GAAG,CAAC,YAAY,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC;AACjD,EAAE,GAAG,CAAC,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACvC,EAAE,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,mBAAmB,CAAC;AACzC,EAAE,GAAG,CAAC,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC;AACjC,EAAE,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AAC7B,EAAE,GAAG,CAAC,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC;AAClC,EAAE,GAAG,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;AAClC,EAAE,GAAG,CAAC,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC;AAClC,EAAE,GAAG,CAAC,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC;AAC9B,EAAE,GAAG,CAAC,KAAK,CAAC,WAAW,GAAG,KAAK,CAAC;AAChC,EAAE,GAAG,CAAC,KAAK,CAAC,cAAc,GAAG,MAAM,CAAC;AACpC,EAAE,GAAG,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC;AAC3B,EAAE,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACtC,EAAE,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AACF,IAAI,MAAM,GAAG,CAAC,QAAQ,KAAK;AAC3B,EAAE,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAChD,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC,iCAAiC,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AACtE,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,UAAU,CAAC;AACnC,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,CAAC;AACpC,EAAE,OAAO,CAAC,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC;AACrC,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC;AAC9B,EAAE,OAAO,CAAC,KAAK,CAAC,WAAW,GAAG,UAAU,CAAC;AACzC,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AACF,IAAI,SAAS,GAAG,CAAC,GAAG,KAAK;AACzB,EAAE,MAAM,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACxC,EAAE,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AAC9C,EAAE,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,8BAA8B,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACvE,EAAE,OAAO,CAAC,YAAY,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC;AACrD,EAAE,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC3C,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;AAClC,EAAE,OAAO,CAAC,KAAK,CAAC,cAAc,GAAG,MAAM,CAAC;AACxC,EAAE,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;AACnC,EAAE,OAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC;AAClC,EAAE,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC;AACpC,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC;AAC9B,EAAE,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,2BAA2B,CAAC;AACzD,EAAE,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM;AAC9C,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;AACpC,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,MAAM;AAC7C,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;AACpC,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC;AAClC,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AACF,IAAI,UAAU,GAAG,MAAM;AACvB,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACnD,EAAE,UAAU,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC;AAC1C,EAAE,UAAU,CAAC,KAAK,CAAC,WAAW,GAAG,SAAS,CAAC;AAC3C,EAAE,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;AACrC,EAAE,UAAU,CAAC,WAAW,GAAG,GAAG,CAAC;AAC/B,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,CAAC;AACF,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK;AAC7B,EAAE,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;AAC9C,EAAE,OAAO,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AACrE,EAAE,OAAO,CAAC,YAAY,CAAC,KAAK,EAAE,qBAAqB,CAAC,CAAC;AACrD,EAAE,OAAO,CAAC,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC3C,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,oBAAoB,CAAC;AAC7C,EAAE,OAAO,CAAC,KAAK,CAAC,cAAc,GAAG,MAAM,CAAC;AACxC,EAAE,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC;AACnC,EAAE,OAAO,CAAC,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC;AAClC,EAAE,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,MAAM,CAAC;AACpC,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC;AAC9B,EAAE,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,6BAA6B,CAAC;AAC3D,EAAE,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,MAAM;AAC9C,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,SAAS,CAAC;AACpC,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,CAAC,gBAAgB,CAAC,UAAU,EAAE,MAAM;AAC7C,IAAI,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,oBAAoB,CAAC;AAC/C,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;AACjC,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AACF,IAAI,OAAO,GAAG,CAAC,KAAK,KAAK;AACzB,EAAE,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAChD,EAAE,OAAO,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AACjC,EAAE,OAAO,CAAC,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC;AACtC,EAAE,OAAO,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC;AACtC,EAAE,OAAO,CAAC,KAAK,CAAC,cAAc,GAAG,QAAQ,CAAC;AAC1C,EAAE,OAAO,CAAC,KAAK,CAAC,WAAW,GAAG,mBAAmB,CAAC;AAClD,EAAE,OAAO,CAAC,KAAK,CAAC,YAAY,GAAG,MAAM,CAAC;AACtC,EAAE,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC;AAChC,EAAE,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;AAC5C,EAAE,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;AAC9C,EAAE,OAAO,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC;AACpC,EAAE,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3C,EAAE,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACnC,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AACF,IAAI,MAAM,GAAG,CAAC,KAAK,KAAK;AACxB,EAAE,MAAM,GAAG,GAAG,GAAG,EAAE,CAAC;AACpB,EAAE,MAAM,cAAc,GAAG,MAAM,GAAG,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;AAC1D,EAAE,GAAG,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;AAClC,EAAE,GAAG,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,EAAE,cAAc,CAAC,CAAC,CAAC;AACnD,EAAE,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AACF,IAAI,SAAS,GAAG,OAAO,QAAQ,KAAK;AACpC,EAAE,IAAI;AACN,IAAI,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,CAAC,kCAAkC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClF,IAAI,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;AACvC,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG,CAAC,OAAO,KAAK,EAAE;AAClB,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,CAAC,CAAC;AACF,IAAI,MAAM,GAAG,CAAC,OAAO,EAAE,OAAO,KAAK;AACnC,EAAE,IAAI,QAAQ,CAAC,IAAI,KAAK,IAAI,EAAE;AAC9B,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;AAClD,GAAG;AACH,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AACrC,CAAC,CAAC;AACF,eAAe,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE;AAC3C,EAAE,IAAI,MAAM,KAAK,KAAK,CAAC;AACvB,IAAI,OAAO,OAAO,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAC;AAC5E,EAAE,MAAM,wBAAwB,GAAG,MAAM,CAAC,MAAM;AAChD,IAAI,MAAM,CAAC,QAAQ,EAAE,eAAe,IAAI;AACxC,MAAM,CAAC,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ;AACjC,KAAK;AACL,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,MAAM,KAAK,wBAAwB,CAAC,CAAC;AAC3E,EAAE,IAAI,wBAAwB;AAC9B,IAAI,OAAO;AACX,EAAE,YAAY,EAAE,CAAC;AACjB,EAAE,IAAI,KAAK,CAAC;AACZ,EAAE,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;AACxC,IAAI,KAAK,GAAG,MAAM,SAAS,CAAC,YAAY,CAAC,CAAC;AAC1C,IAAI,IAAI,KAAK,KAAK,IAAI;AACtB,MAAM,OAAO,OAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;AAC9C,GAAG,MAAM;AACT,IAAI,KAAK,GAAG,YAAY,CAAC;AACzB,GAAG;AACH,EAAE,MAAM,mBAAmB,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAC5C,EAAE,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAC9B,EAAE,OAAO;AACT,IAAI,OAAO,EAAE,mBAAmB;AAChC,GAAG,CAAC;AACJ,CAAC;AACD,IAAI,IAAI,GAAG,CAAC,KAAK,EAAE,OAAO,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC;AAuB3C,IAAI,aAAa,GAAG,UAAU,CAAC;AAqC1B,MAAC,IAAI,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC5E,EAAE,IAAI,MAAM,CAAC;AACb,EAAE,IAAI,EAAE,EAAE,eAAe,CAAC;AAC1B,EAAE,IAAI,KAAK,EAAE,kBAAkB,CAAC;AAChC,EAAE,eAAe,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,KAAK,KAAK,EAAE,GAAG,KAAK,CAAC,CAAC;AAC9D,EAAE,kBAAkB,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC,KAAK,KAAK,KAAK,GAAG,KAAK,CAAC,CAAC;AACjE,EAAE,SAAS,EAAE,CAAC;AACd,EAAmB,qBAAqB,GAAG;AAC3C,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,OAAO,GAAG,QAAQ,EAAE,GAAG,OAAO,CAAC;AACvC,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,QAAQ,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACpC,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,UAAU,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,kBAAkB,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAC9C,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,OAAO,CAAC;AAE9B,EAAE,IAAI,iBAAiB,CAAC;AACxB,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAE1B,EAAE,IAAI,OAAO,CAAC;AACd,EAAE,IAAI,KAAK,GAAG,KAAK,CAAC;AACpB,EAAE,IAAI,eAAe,GAAG,KAAK,CAAC;AAC9B,EAAE,EAAE,CAAC,gBAAgB,CAAC,GAAG,KAAK,CAAC;AAC/B,GAAqB;AACrB,IAAI,QAAQ,EAAE,MAAM;AACpB,KAAK;AACL,IAAI,SAAS,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS;AACrC,KAAI;AACJ,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AAGrB,EAAE,IAAI,uBAAuB,GAAG,KAAK,CAAC;AAsDtC,EAAE,IAAI,cAAc,CAAC;AAKrB,EAAE,IAAI,WAAW,CAAC;AAClB,EAAE,eAAe,kBAAkB,CAAC,QAAQ,EAAE,SAAS,EAAE;AACzD,IAAI,IAAI,QAAQ,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC,IAAI,KAAK,MAAM,CAAC,GAAG,EAAE;AAC9D,MAAM,IAAI,WAAW,EAAE;AACvB,QAAQ,WAAW,CAAC,MAAM,EAAE,CAAC;AAC7B,QAAQ,WAAW,GAAG,KAAK,CAAC,CAAC;AAC7B,OAAO;AACP,MAAM,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC1C,MAAM,IAAI,MAAM;AAChB,QAAQ,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC;AACrC,KAAK;AACL,GAAG;AACH,EAAE,SAAS,CAAC,MAAM;AAClB,IAAI,WAAW,EAAE,MAAM,EAAE,CAAC;AAC1B,GAAG,CAAC,CAAC;AACL,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,UAAU,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,UAAU,IAAI,UAAU,KAAK,KAAK,CAAC;AACrF,IAAI,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,IAAI,OAAO,CAAC,kBAAkB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,kBAAkB,IAAI,kBAAkB,KAAK,KAAK,CAAC;AAC7G,IAAI,UAAU,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,CAAC;AACtD,EAAE,IAAI,OAAO,CAAC,SAAS,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,SAAS,IAAI,SAAS,KAAK,KAAK,CAAC;AAClF,IAAI,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;AACpC,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AACzB,IAAI;AACJ,MAAM,IAAI,MAAM,EAAE,MAAM,EAAE;AAC1B,QAAQ,MAAM,CAAC,MAAM,CAAC;AACtB,OAAO;AACP,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,cAAc,IAAI,uBAAuB,EAAE;AACrD,QAAQ,cAAc,CAAC,OAAO,EAAE,yBAAyB,EAAE,OAAO,CAAC,CAAC;AACpE,QAAQ,uBAAuB,GAAG,KAAK,CAAC;AACxC,OAAO;AACP,KAAK;AACL,IAAI;AACJ,MAAM,IAAI,eAAe,EAAE;AAC3B,QAAQ,OAAO,CAAC,aAAa,CAAC,IAAI,WAAW;AAC7C,UAAU,QAAQ;AAClB,UAAU;AACV,YAAY,OAAO,EAAE,IAAI;AACzB,YAAY,UAAU,EAAE,KAAK;AAC7B,YAAY,QAAQ,EAAE,IAAI;AAC1B,WAAW;AACX,SAAS,CAAC,CAAC;AACX,OAAO;AACP,KAAK;AACL,IAAI,GAAG,EAAE,MAAM,IAAI,OAAO,IAAI,kBAAkB,CAAC,GAAG,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAClF,IAAI,UAAU,GAAG,CAAC,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,wDAAwD,EAAE,aAAa,CAAC,MAAM,EAAE,gBAAgB,GAAG,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,4EAA4E,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,kBAAkB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,QAAQ;AAChS,MAAM,QAAQ;AACd,MAAM;AACN,QAAQ,OAAO,EAAE,SAAS,IAAI,QAAQ;AACtC,QAAQ,QAAQ;AAChB,QAAQ,IAAI,EAAE,KAAK;AACnB,QAAQ,OAAO;AACf,QAAQ,cAAc;AACtB,QAAQ,KAAK;AACb,QAAQ,KAAK,EAAE,MAAM,CAAC,KAAK;AAC3B,QAAQ,YAAY,EAAE,MAAM,CAAC,YAAY;AACzC,QAAQ,IAAI,EAAE,MAAM,CAAC,IAAI;AACzB,QAAQ,MAAM,EAAE,aAAa,KAAK,UAAU;AAC5C,QAAQ,UAAU,EAAE,MAAM,EAAE,UAAU,IAAI,KAAK;AAC/C,QAAQ,OAAO;AACf,OAAO;AACP,MAAM;AACN,QAAQ,OAAO,EAAE,CAAC,OAAO,KAAK;AAC9B,UAAU,OAAO,GAAG,OAAO,CAAC;AAC5B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,MAAM,EAAE,aAAa,GAAG,CAAC,EAAE,kBAAkB,CAAC,IAAI,CAAC,MAAM,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AAChI,YAAY,QAAQ;AACpB,YAAY;AACZ,cAAc,YAAY,EAAE,MAAM,CAAC,YAAY;AAC/C,cAAc,IAAI,EAAE,MAAM,CAAC,IAAI;AAC/B,cAAc,QAAQ,EAAE,KAAK;AAC7B,cAAc,QAAQ;AACtB,aAAa;AACb,YAAY,EAAE;AACd,YAAY,EAAE;AACd,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE,kBAAkB,CAAC,IAAI,CAAC,MAAM,IAAI,iBAAiB,EAAE,kBAAkB,CAAC,CAAC,QAAQ;AACvH,YAAY,QAAQ;AACpB,YAAY,MAAM,CAAC,MAAM;AACzB,cAAc,EAAE;AAChB,cAAc,EAAE,GAAG,EAAE;AACrB,cAAc,MAAM;AACpB,cAAc;AACd,gBAAgB,WAAW,EAAE,CAAC,QAAQ,IAAI,MAAM,CAAC,WAAW;AAC5D,eAAe;AACf,cAAc,EAAE,UAAU,EAAE,iBAAiB,EAAE;AAC/C,cAAc,EAAE,kBAAkB,EAAE;AACpC,cAAc,EAAE,MAAM,EAAE,OAAO,EAAE;AACjC,cAAc,EAAE,UAAU,EAAE;AAC5B,cAAc,EAAE,WAAW,EAAE,CAAC,QAAQ,EAAE;AACxC,cAAc,EAAE,QAAQ,EAAE;AAC1B,cAAc,EAAE,OAAO,EAAE;AACzB,cAAc,EAAE,aAAa,EAAE,KAAK,CAAC,GAAG,CAAC,YAAY,EAAE;AACvD,cAAc,EAAE,cAAc,EAAE,IAAI,CAAC,MAAM,EAAE;AAC7C,cAAc,EAAE,KAAK,EAAE;AACvB,cAAc,EAAE,eAAe,EAAE;AACjC,cAAc,EAAE,eAAe,EAAE,cAAc,EAAE;AACjD,aAAa;AACb,YAAY;AACZ,cAAc,KAAK,EAAE,CAAC,OAAO,KAAK;AAClC,gBAAgB,KAAK,GAAG,OAAO,CAAC;AAChC,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,cAAc,eAAe,EAAE,CAAC,OAAO,KAAK;AAC5C,gBAAgB,eAAe,GAAG,OAAO,CAAC;AAC1C,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,cAAc,eAAe,EAAE,CAAC,OAAO,KAAK;AAC5C,gBAAgB,cAAc,GAAG,OAAO,CAAC;AACzC,gBAAgB,SAAS,GAAG,KAAK,CAAC;AAClC,eAAe;AACf,aAAa;AACb,YAAY,EAAE;AACd,WAAW,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACvB,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,CAAC;AACR,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,eAAe,EAAE,CAAC;AACpB,EAAE,kBAAkB,EAAE,CAAC;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC;;;;"}